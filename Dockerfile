FROM openjdk:8-slim

ENV DEBUG=${DEBUG:-"false"}
ENV DISCOVERY_URL=${DISCOVERY_URL:-"http://localhost:8086"}
ENV AUTENTICACAO_URL=${AUTENTICACAO_URL:-"http://localhost:8080"}
ENV SERVER_PORT=${SERVER_PORT:-"8080"}
ENV AUTH_SECRET_PATH=${AUTH_SECRET_PATH:-"/keys/auth-secret"}
ENV AUTH_SECRET_PERSONA=${AUTH_SECRET_PERSONA:-"/keys/auth-secret-persona"}
ENV CONTEXTO=${CONTEXTO:-"relatorio-ms"}
ENV TZ=America/Sao_Paulo
ENV OAMD_URL=${OAMD_URL:-"************************************"}
ENV OAMD_USERNAME=${OAMD_USERNAME:-"postgres"}
ENV OAMD_PASSWORD=${OAMD_PASSWORD:-"pactodb"}

RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone

COPY ./target/relatorio-ms /app
COPY src/main/resources/application.properties /app/application.properties
COPY docker/bin/*.sh /bin/
COPY docker/keys/* /keys/

RUN chmod +x /bin/*.sh
ADD https://ssl-ccp.godaddy.com/repository/gd_bundle-g2-g1.crt $JAVA_HOME/lib/security/gd_bundle-g2-g1.crt

RUN cd $JAVA_HOME/lib/security/ \
    && keytool -import -alias gd_bundle-g2-g1 -file gd_bundle-g2-g1.crt -keystore cacerts -trustcacerts -storepass changeit


RUN sed -i 's/archive.ubuntu.com/br.archive.ubuntu.com/g' /etc/apt/sources.list && \
  sed -i 's/ftp.us.debian.org/ftp.br.debian.org/g' /etc/apt/sources.list && \  
  apt-get update && apt-get install -y cabextract fontconfig && \  
  rm -rf /var/lib/apt/lists/*

ADD https://www.freedesktop.org/software/fontconfig/webfonts/webfonts.tar.gz /



RUN cd / && tar -xzf /webfonts.tar.gz && cd msfonts/ && cabextract *.exe && ls *Arial* && cp -rp * /usr/share/fonts

RUN fc-cache -f -v


ENTRYPOINT ["bash", "/bin/entrypoint.sh"]

