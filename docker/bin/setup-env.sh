#!/usr/bin/env bash

set -e

file_properties=/app/WEB-INF/classes/application.properties
original_file_properties=/app/application.properties
cp $original_file_properties $file_properties

if [ -f "$file_properties" ]; then
    sed -i "s~@discovery.url@~$DISCOVERY_URL~g" $file_properties
    sed -i "s~@autenticacao.url@~$AUTENTICACAO_URL~g" $file_properties
    sed -i "s~@server.port@~$SERVER_PORT~g" $file_properties
    sed -i "s~@AUTH_SECRET_PATH@~$AUTH_SECRET_PATH~g" $file_properties
    sed -i "s~@AUTH_SECRET_PERSONA@~$AUTH_SECRET_PERSONA~g" $file_properties
    sed -i "s~server.servlet.context-path=.*~contexto=$CONTEXTO~g" $file_properties
    sed -i "s~spring.datasource.oamd.jdbcUrl=.*~spring.datasource.oamd.jdbcUrl=$OAMD_URL~g" $file_properties
    sed -i "s~spring.datasource.oamd.username=.*~spring.datasource.oamd.username=$OAMD_USERNAME~g" $file_properties
    sed -i "s~spring.datasource.oamd.password=.*~spring.datasource.oamd.password=$OAMD_PASSWORD~g" $file_properties
    echo "Application properties:"
    cat $file_properties
    exit 0
else
    echo "Setup environment failed"
    echo "File $file_properties not found"
    exit 0
fi

