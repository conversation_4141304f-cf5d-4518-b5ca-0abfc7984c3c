<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.19.0.final using JasperReports Library version 6.19.0-646c68931cebf1a58bc65c4359d1f0ca223c5e94  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="ListaAcessoRelCliente" pageWidth="878" pageHeight="680" orientation="Landscape" columnWidth="838" leftMargin="20" rightMargin="20" topMargin="10" bottomMargin="10" whenResourceMissingType="Empty" uuid="c3712a0b-5fb2-4b22-a5d4-caf41248db4e">
	<property name="ireport.scriptlethandling" value="0"/>
	<property name="ireport.encoding" value="ISO-8859-1"/>
	<property name="ireport.zoom" value="1.5"/>
	<property name="ireport.x" value="215"/>
	<property name="ireport.y" value="0"/>
	<import value="net.sf.jasperreports.engine.*"/>
	<import value="java.util.*"/>
	<import value="net.sf.jasperreports.engine.data.*"/>
	<subDataset name="Teste" uuid="de8141b0-913a-440a-93ff-dea671f7343c"/>
	<parameter name="logoPadraoRelatorio" class="java.io.InputStream" isForPrompting="false"/>
	<parameter name="imagemPadraoCliente" class="java.io.InputStream" isForPrompting="false"/>
	<parameter name="tituloRelatorio" class="java.lang.String" isForPrompting="false"/>
	<parameter name="versaoSoftware" class="java.lang.String" isForPrompting="false"/>
	<parameter name="nomeUsuario" class="java.lang.String" isForPrompting="false"/>
	<parameter name="filtros" class="java.lang.String" isForPrompting="false"/>
	<parameter name="SUBREPORT_DIR" class="java.lang.String">
		<defaultValueExpression><![CDATA["D:\\PactoJ\\Desenvolvimento\\Sistemas\\ZillyonWeb\\tronco-novo\\src\\java\\relatorio\\designRelatorio\\outros\\"]]></defaultValueExpression>
	</parameter>
	<parameter name="nomeEmpresa" class="java.lang.String"/>
	<parameter name="enderecoEmpresa" class="java.lang.String"/>
	<parameter name="cidadeEmpresa" class="java.lang.String"/>
	<parameter name="dataIni" class="java.lang.String" isForPrompting="false"/>
	<parameter name="dataFim" class="java.lang.String" isForPrompting="false"/>
	<parameter name="SUBREPORT_DIR1" class="java.lang.String"/>
	<parameter name="exibirMaisDetalhesCliente" class="java.lang.Boolean"/>
	<parameter name="urlBaseImagemCliente" class="java.lang.String"/>
	<parameter name="apresentarAcessosNessaUnidade" class="java.lang.Boolean">
		<defaultValueExpression><![CDATA[false]]></defaultValueExpression>
	</parameter>
	<queryString>
		<![CDATA[]]>
	</queryString>
	<field name="cliente.matricula" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="cliente.pessoa.fotoIS" class="java.io.InputStream"/>
	<field name="cliente.pessoa.cfp" class="java.lang.String"/>
	<field name="cliente.pessoa.rg" class="java.lang.String"/>
	<field name="intervaloDataHoras" class="java.lang.String"/>
	<field name="cliente.pessoa.endereco" class="java.lang.String"/>
	<field name="cliente.pessoa.nome" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="dataHoraEntrada" class="java.util.Date">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="dataHoraSaida" class="java.util.Date">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="sentido" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="usuario.primeiroNomeConcatenado" class="java.lang.String"/>
	<field name="situacao.descricao" class="java.lang.String"/>
	<field name="situacao.id" class="java.lang.String"/>
	<field name="meioIdentificacaoEntrada.descricao" class="java.lang.String"/>
	<field name="localAcesso.empresa.nome" class="java.lang.String"/>
	<field name="coletor.descricao" class="java.lang.String"/>
	<field name="cliente.pessoa.email" class="java.lang.String"/>
	<field name="nomeCodEmpresaAcessou" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="cliente.pessoa.dataNasc" class="java.util.Date"/>
	<field name="matriculaClienteOrigem" class="java.lang.String"/>
	<field name="nomeClienteOrigem" class="java.lang.String"/>
	<field name="cpfClienteOrigem" class="java.lang.String"/>
	<field name="nomeCodEmpresaOrigem" class="java.lang.String"/>
	<variable name="totalclientes" class="java.lang.Integer" calculation="Count">
		<variableExpression><![CDATA[new Integer($F{matriculaClienteOrigem})]]></variableExpression>
	</variable>
	<variable name="totalalunos" class="java.lang.Integer" calculation="DistinctCount">
		<variableExpression><![CDATA[new Integer($F{matriculaClienteOrigem})]]></variableExpression>
	</variable>
	<group name="nomeCliente">
		<groupExpression><![CDATA[($F{cliente.pessoa.nome} != null ? $F{cliente.pessoa.nome} : "") + ($F{nomeClienteOrigem} != null ? $F{nomeClienteOrigem} : "")]]></groupExpression>
		<groupHeader>
			<band height="20">
				<printWhenExpression><![CDATA[new Boolean($P{exibirMaisDetalhesCliente}.booleanValue() == false)]]></printWhenExpression>
				<line>
					<reportElement x="0" y="0" width="834" height="1" uuid="84674f84-3778-436c-a2c3-9ade758d9fe7"/>
				</line>
				<textField textAdjust="StretchHeight" isBlankWhenNull="true">
					<reportElement key="textField-224" positionType="Float" x="2" y="2" width="830" height="10" uuid="337c96dc-dd2e-4ee8-aedf-6c9e8e97c5d9"/>
					<textElement>
						<font size="11" isBold="true" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{nomeClienteOrigem} + ($F{cpfClienteOrigem}==null || $F{cpfClienteOrigem}.trim().equals("") ? "" :  (" - " + $F{cpfClienteOrigem})) + ($F{cliente.pessoa.email}==null || $F{cliente.pessoa.email}.trim().equals("") ? "" :  (" - " + $F{cliente.pessoa.email}))]]></textFieldExpression>
				</textField>
			</band>
			<band height="86">
				<printWhenExpression><![CDATA[$P{exibirMaisDetalhesCliente}]]></printWhenExpression>
				<line>
					<reportElement x="0" y="0" width="834" height="1" uuid="3cdc3e7f-a4e3-4d2f-8b25-dc7bec12ab9a"/>
				</line>
				<image isUsingCache="true" onErrorType="Blank">
					<reportElement x="0" y="3" width="80" height="80" uuid="92ae2a82-d9b2-44a3-aba8-a4d25ab681f5"/>
					<imageExpression><![CDATA[$F{cliente.pessoa.fotoIS} == null ? $P{imagemPadraoCliente} : $F{cliente.pessoa.fotoIS}]]></imageExpression>
				</image>
				<textField textAdjust="StretchHeight" isBlankWhenNull="true">
					<reportElement x="85" y="2" width="830" height="10" uuid="0c5e1f8c-c6cb-4115-ae43-c3c967812840"/>
					<textElement>
						<font size="10" isBold="true" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<textFieldExpression><![CDATA["NOME: " + $F{nomeClienteOrigem}]]></textFieldExpression>
				</textField>
				<textField textAdjust="StretchHeight" isBlankWhenNull="true">
					<reportElement x="85" y="14" width="830" height="10" uuid="a4f5edb6-3a73-4431-adb8-e22b5ae6083e"/>
					<textElement>
						<font size="10" isBold="true" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{cpfClienteOrigem} == null ? "CPF: não cadastrado" : "CPF: " + $F{cpfClienteOrigem}]]></textFieldExpression>
				</textField>
				<textField textAdjust="StretchHeight" isBlankWhenNull="true">
					<reportElement x="85" y="26" width="830" height="10" uuid="32dbee98-0230-4fe9-a1df-f238c3cb3e9f"/>
					<textElement>
						<font size="10" isBold="true" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{cliente.pessoa.rg} == null ? "RG: não cadastrado" : "RG: " + $F{cliente.pessoa.rg}]]></textFieldExpression>
				</textField>
				<textField textAdjust="StretchHeight" pattern="dd/MM/yyyy" isBlankWhenNull="true">
					<reportElement x="85" y="38" width="830" height="10" uuid="9ff820aa-d5b0-4006-b289-df2c842afdf3"/>
					<textElement>
						<font size="10" isBold="true" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<textFieldExpression><![CDATA["Data de Nascimento: " +(($F{cliente.pessoa.dataNasc} != null ) ? (new java.text.SimpleDateFormat("dd/MM/yyyy").format($F{cliente.pessoa.dataNasc}))  : "não cadastrada")]]></textFieldExpression>
				</textField>
				<textField textAdjust="StretchHeight" isBlankWhenNull="true">
					<reportElement x="85" y="50" width="830" height="10" uuid="cecbbbc1-9128-42c8-8815-1f1b48e21557"/>
					<textElement>
						<font size="10" isBold="true" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{cliente.pessoa.endereco} == null ? "Endereço: não cadastrado" : "Endereço: " + $F{cliente.pessoa.endereco}]]></textFieldExpression>
				</textField>
				<textField textAdjust="StretchHeight" isBlankWhenNull="true">
					<reportElement x="85" y="62" width="830" height="10" uuid="fd5f227b-f017-4bc6-a55b-3e5f16a5b9f8"/>
					<textElement>
						<font size="10" isBold="true" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<textFieldExpression><![CDATA["E-mail: " + (($F{cliente.pessoa.email}==null || $F{cliente.pessoa.email}.trim().equals("")) ? "" :  $F{cliente.pessoa.email})]]></textFieldExpression>
				</textField>
				<line>
					<reportElement x="0" y="84" width="834" height="1" uuid="ed6d30ca-d90e-4d4f-aaa2-5c41b8c19ff5"/>
				</line>
			</band>
		</groupHeader>
		<groupFooter>
			<band height="19">
				<staticText>
					<reportElement x="613" y="0" width="90" height="15" uuid="016f0867-b8e3-498a-af4d-b480d53f5e7e"/>
					<textElement textAlignment="Right">
						<font pdfFontName="Helvetica-Bold"/>
					</textElement>
					<text><![CDATA[Total de Acessos:]]></text>
				</staticText>
				<textField textAdjust="StretchHeight">
					<reportElement x="706" y="0" width="130" height="15" uuid="d22f6784-64d6-4324-a20f-9f5efe8470f7"/>
					<textFieldExpression><![CDATA[$V{nomeCliente_COUNT}]]></textFieldExpression>
				</textField>
			</band>
		</groupFooter>
	</group>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band splitType="Stretch"/>
	</title>
	<pageHeader>
		<band height="69" splitType="Stretch">
			<textField evaluationTime="Report" isBlankWhenNull="false">
				<reportElement key="textField-212" x="803" y="51" width="31" height="17" uuid="5f556b0f-c336-49e9-9f66-fcca85fa94fa"/>
				<box bottomPadding="2">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement>
					<font fontName="Arial" size="10" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression><![CDATA[" " + $V{PAGE_NUMBER} + ""]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement key="staticText-15" mode="Opaque" x="721" y="35" width="111" height="12" uuid="55fdcebb-1553-474e-8013-3e7e2cd69f98"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Top">
					<font fontName="Microsoft Sans Serif" size="9" isBold="true" isItalic="true" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica-BoldOblique"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<text><![CDATA[(62) 3414-0314]]></text>
			</staticText>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-211" x="723" y="51" width="75" height="17" uuid="fa61443b-5c9c-4219-b8fe-84ae9a7674d3"/>
				<box bottomPadding="2">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right">
					<font fontName="Arial" size="10" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression><![CDATA["Página: " + $V{PAGE_NUMBER} + " de "]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement key="staticText-13" x="320" y="31" width="189" height="27" uuid="cad1f95b-cc84-499b-980c-e50a0ed31a23"/>
				<textElement textAlignment="Center">
					<font size="20" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Lista de Acessos]]></text>
			</staticText>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-209" x="86" y="31" width="113" height="14" uuid="86a5ce6d-3df2-4e82-9696-33f035548440"/>
				<textElement>
					<font isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{enderecoEmpresa}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-208" x="86" y="15" width="113" height="14" uuid="622792ed-d329-48b4-b69f-ad35b9129272"/>
				<textElement>
					<font isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{nomeEmpresa}]]></textFieldExpression>
			</textField>
			<image isUsingCache="true" onErrorType="Blank" evaluationTime="Page">
				<reportElement key="image-1" x="0" y="16" width="82" height="46" isPrintWhenDetailOverflows="true" uuid="87d4c100-af8d-47bc-b787-0280b275abb8"/>
				<box>
					<topPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<imageExpression><![CDATA[$P{logoPadraoRelatorio}]]></imageExpression>
			</image>
			<staticText>
				<reportElement key="staticText-14" mode="Opaque" x="570" y="6" width="262" height="23" uuid="3d2d7123-e86f-4b05-9c9c-19f77ce6e5ec"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Top">
					<font fontName="Microsoft Sans Serif" size="9" isBold="true" isItalic="true" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica-BoldOblique"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<text><![CDATA[Pacto - Sistema Administrativo para Academias Desenvolvido por PACTO Soluções Tecnológicas Ltda.]]></text>
			</staticText>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-210" x="86" y="47" width="113" height="14" uuid="0d1a5981-d672-4daa-bad6-e7560392ae8e"/>
				<textElement>
					<font isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{cidadeEmpresa}]]></textFieldExpression>
			</textField>
		</band>
	</pageHeader>
	<columnHeader>
		<band height="58" splitType="Stretch">
			<line>
				<reportElement key="line-1" x="0" y="2" width="838" height="1" uuid="6e8e34f5-01f4-463c-8c43-fe74a595fe43"/>
			</line>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-214" x="0" y="3" width="838" height="30" uuid="de44bfbc-119b-42ed-a698-d3f796f59faa"/>
				<box>
					<topPen lineWidth="0.5" lineStyle="Solid"/>
					<leftPen lineWidth="0.5" lineStyle="Solid"/>
					<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.5" lineStyle="Solid"/>
				</box>
				<textElement textAlignment="Center">
					<font fontName="Arial" size="10" isBold="true" isItalic="true" pdfFontName="Helvetica-BoldOblique"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{filtros}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement key="staticText-2" x="2" y="39" width="76" height="14" uuid="76dcceb0-fb67-47b9-b358-0307d4da4787"/>
				<textElement>
					<font isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Mat. Cliente]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-8" x="78" y="39" width="81" height="14" uuid="8e5a0e01-d1b4-4c2c-b115-5912f4224a78"/>
				<textElement>
					<font isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Data Entrada]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-8" x="159" y="39" width="85" height="14" uuid="e50db601-a124-468e-9c3b-5055019057e0"/>
				<textElement>
					<font isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Data Saída]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-8" x="244" y="39" width="43" height="14" uuid="dd3089a6-62a4-4b1e-b39d-065e82a1ba3e"/>
				<textElement>
					<font isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Tempo]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-8" x="287" y="39" width="45" height="14" uuid="29c1e8e7-5135-40a9-a0e0-1b38571b5fc3"/>
				<textElement>
					<font isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Sentido]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-8" x="761" y="39" width="78" height="14" uuid="f8871f98-29e5-497a-87e8-fe154dd6bba9"/>
				<textElement>
					<font isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Usuário Lib.]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-8" x="332" y="39" width="91" height="14" uuid="4fdd3d5b-5537-4a6d-8a49-fcbbca7f3ae4"/>
				<textElement>
					<font isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Bloqueio]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-8" x="650" y="39" width="108" height="14" uuid="db5e941e-e032-481c-847a-92ef4eb54eaf"/>
				<textElement>
					<font isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Coletor]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-8" x="424" y="39" width="126" height="14" uuid="af6c75ea-52dc-4c5f-be75-e99886e9fac0"/>
				<textElement>
					<font isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Empresa Acesso]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-8" x="550" y="39" width="100" height="14" uuid="ea686dca-c457-4b53-b22a-53a4062f9c79"/>
				<textElement>
					<font isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Empresa Origem]]></text>
			</staticText>
		</band>
	</columnHeader>
	<detail>
		<band height="29" splitType="Stretch">
			<textField textAdjust="StretchHeight" isBlankWhenNull="true">
				<reportElement key="textField-223" stretchType="RelativeToTallestObject" x="244" y="1" width="40" height="14" uuid="091eb841-ddbf-4784-91ca-de7f7142e3f4"/>
				<textElement textAlignment="Left" verticalAlignment="Middle"/>
				<textFieldExpression><![CDATA[$F{intervaloDataHoras}]]></textFieldExpression>
			</textField>
			<textField textAdjust="StretchHeight" pattern="" isBlankWhenNull="true">
				<reportElement key="textField" stretchType="RelativeToTallestObject" x="284" y="1" width="35" height="14" uuid="fe6a6053-c4c2-41c1-b99b-c59ce22fc812"/>
				<textElement textAlignment="Left" verticalAlignment="Middle"/>
				<textFieldExpression><![CDATA[$F{sentido}]]></textFieldExpression>
			</textField>
			<textField textAdjust="StretchHeight" pattern="dd/MM/yyyy HH.mm" isBlankWhenNull="true">
				<reportElement key="textField-224" stretchType="RelativeToTallestObject" x="159" y="1" width="85" height="14" uuid="59a14cc4-444d-4b5a-bed8-aaaa6c43cf90"/>
				<textElement textAlignment="Left" verticalAlignment="Middle"/>
				<textFieldExpression><![CDATA[$F{dataHoraSaida}]]></textFieldExpression>
			</textField>
			<textField textAdjust="StretchHeight" pattern="" isBlankWhenNull="true">
				<reportElement key="textField" stretchType="RelativeToTallestObject" x="761" y="1" width="76" height="14" uuid="486e9f0d-c2ec-49e7-9042-04e1b4df7146"/>
				<textElement textAlignment="Left" verticalAlignment="Middle"/>
				<textFieldExpression><![CDATA[$F{usuario.primeiroNomeConcatenado}]]></textFieldExpression>
			</textField>
			<textField textAdjust="StretchHeight" isBlankWhenNull="true">
				<reportElement key="textField-223" stretchType="RelativeToTallestObject" x="1" y="1" width="76" height="14" uuid="a326cdca-ef26-444e-85a3-e2bd0e646251"/>
				<textElement textAlignment="Left" verticalAlignment="Middle"/>
				<textFieldExpression><![CDATA[$F{matriculaClienteOrigem}]]></textFieldExpression>
			</textField>
			<textField textAdjust="StretchHeight" pattern="" isBlankWhenNull="true">
				<reportElement key="textField" stretchType="RelativeToTallestObject" x="320" y="1" width="102" height="14" uuid="a9918627-d872-4c3e-a22c-0a7388cead04">
					<printWhenExpression><![CDATA[!$F{situacao.id}.equals("RV_LIBACESSOAUTORIZADO")]]></printWhenExpression>
				</reportElement>
				<textElement textAlignment="Left" verticalAlignment="Middle"/>
				<textFieldExpression><![CDATA[$F{situacao.descricao}]]></textFieldExpression>
			</textField>
			<textField textAdjust="StretchHeight" pattern="" isBlankWhenNull="true">
				<reportElement key="textField" stretchType="RelativeToTallestObject" x="93" y="15" width="739" height="14" uuid="4da58748-12e9-400a-92ca-4a96a79c330c"/>
				<textElement textAlignment="Left" verticalAlignment="Top">
					<font size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{meioIdentificacaoEntrada.descricao}]]></textFieldExpression>
			</textField>
			<textField textAdjust="StretchHeight" pattern="dd/MM/yyyy HH.mm" isBlankWhenNull="true">
				<reportElement key="textField-224" stretchType="RelativeToTallestObject" x="78" y="1" width="81" height="14" uuid="1f2764b4-329e-4b4b-8a01-dd46f6e9a0dc"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font size="10" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{dataHoraEntrada}]]></textFieldExpression>
			</textField>
			<textField textAdjust="StretchHeight" pattern="" isBlankWhenNull="true">
				<reportElement key="textField" stretchType="RelativeToTallestObject" x="650" y="2" width="108" height="14" uuid="4a4e6e36-43e0-4a77-a665-29ac306148f7"/>
				<textElement textAlignment="Left" verticalAlignment="Middle"/>
				<textFieldExpression><![CDATA[$F{coletor.descricao}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement key="staticText-8" x="2" y="15" width="97" height="14" uuid="6678843a-1ba7-4036-9c16-6661a74e1403"/>
				<textElement>
					<font size="9" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Meio Identificação:]]></text>
			</staticText>
			<textField textAdjust="StretchHeight" pattern="" isBlankWhenNull="true">
				<reportElement key="textField" stretchType="RelativeToTallestObject" x="424" y="2" width="120" height="14" uuid="5b8f8df4-731c-4b0e-9ee9-2d9b2391a2f9"/>
				<textElement textAlignment="Left" verticalAlignment="Middle"/>
				<textFieldExpression><![CDATA[$F{nomeCodEmpresaAcessou}]]></textFieldExpression>
			</textField>
			<textField textAdjust="StretchHeight" pattern="" isBlankWhenNull="true">
				<reportElement key="textField" stretchType="RelativeToTallestObject" x="550" y="1" width="100" height="14" uuid="6058a3cb-b53a-4afb-a289-7b072b4b15c9"/>
				<textElement textAlignment="Left" verticalAlignment="Middle"/>
				<textFieldExpression><![CDATA[$F{nomeCodEmpresaOrigem}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
	<columnFooter>
		<band splitType="Stretch"/>
	</columnFooter>
	<pageFooter>
		<band splitType="Stretch"/>
	</pageFooter>
	<lastPageFooter>
		<band height="75" splitType="Stretch">
			<staticText>
				<reportElement x="390" y="20" width="123" height="20" uuid="45679039-06eb-4543-bfed-c4080104c353"/>
				<textElement>
					<font pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Total Geral de Acessos:]]></text>
			</staticText>
			<line>
				<reportElement key="line-4" x="1" y="-2" width="838" height="1" uuid="5a74521b-0c09-48be-a9ee-e97bcc4013e8"/>
			</line>
			<line>
				<reportElement key="line-5" x="1" y="49" width="837" height="1" uuid="f082cd72-3214-4b27-ba76-3476dd868d70"/>
			</line>
			<line>
				<reportElement key="line-6" x="1" y="51" width="837" height="1" uuid="1db88276-e6fd-4cac-b58e-4e21d21e8a43"/>
			</line>
			<textField pattern="" isBlankWhenNull="false">
				<reportElement key="textField-207" x="1" y="55" width="837" height="19" uuid="d7a3fd25-4418-461b-b010-4c10ed0579c3"/>
				<box>
					<topPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="8" isItalic="true" pdfFontName="Helvetica-Oblique"/>
				</textElement>
				<textFieldExpression><![CDATA[" "+" Usuário: " + $P{nomeUsuario}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="513" y="20" width="100" height="20" uuid="c7cd5ec4-116e-4fc3-be95-b796d9df9e6c"/>
				<textFieldExpression><![CDATA[$V{totalclientes}]]></textFieldExpression>
			</textField>
			<textField pattern="dd/MM/yyyy HH.mm.ss" isBlankWhenNull="true">
				<reportElement key="dataRel-1" mode="Opaque" x="702" y="59" width="126" height="13" backcolor="#FFFFFF" uuid="99232a51-9a24-4803-ab91-fe3c4b361f57"/>
				<box bottomPadding="2">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="false" isItalic="true" isUnderline="false" pdfFontName="Helvetica-Oblique"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[new Date()]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="736" y="20" width="100" height="20" uuid="338c5c48-c3cc-41b6-b9ed-fa0368f303bf"/>
				<textFieldExpression><![CDATA[$V{totalalunos}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="613" y="20" width="123" height="20" uuid="766c9bd3-5ea4-47d8-a73c-8687957563fc"/>
				<textElement>
					<font pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Total Geral de Alunos:]]></text>
			</staticText>
		</band>
	</lastPageFooter>
	<summary>
		<band splitType="Stretch"/>
	</summary>
</jasperReport>
