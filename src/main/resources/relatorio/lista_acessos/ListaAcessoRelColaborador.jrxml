<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.19.0.final using JasperReports Library version 6.5.1  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="ListaAcessoRelColaborador" pageWidth="680" pageHeight="878" columnWidth="640" leftMargin="20" rightMargin="20" topMargin="10" bottomMargin="10" whenResourceMissingType="Empty" uuid="b721a36e-19dc-42bb-8b4f-f616bd70e44d">
	<property name="ireport.scriptlethandling" value="0"/>
	<property name="ireport.encoding" value="ISO-8859-1"/>
	<property name="ireport.zoom" value="1.3636363636363635"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<import value="net.sf.jasperreports.engine.*"/>
	<import value="java.util.*"/>
	<import value="net.sf.jasperreports.engine.data.*"/>
	<subDataset name="Teste" uuid="01bee1aa-3c25-43c4-a8d8-1a9675f48479"/>
	<parameter name="logoPadraoRelatorio" class="java.io.InputStream" isForPrompting="false"/>
	<parameter name="tituloRelatorio" class="java.lang.String" isForPrompting="false"/>
	<parameter name="versaoSoftware" class="java.lang.String" isForPrompting="false"/>
	<parameter name="nomeUsuario" class="java.lang.String" isForPrompting="false"/>
	<parameter name="filtros" class="java.lang.String" isForPrompting="false"/>
	<parameter name="SUBREPORT_DIR" class="java.lang.String">
		<defaultValueExpression><![CDATA["D:\\PactoJ\\Desenvolvimento\\Sistemas\\ZillyonWeb\\tronco-novo\\src\\java\\relatorio\\designRelatorio\\outros\\"]]></defaultValueExpression>
	</parameter>
	<parameter name="nomeEmpresa" class="java.lang.String"/>
	<parameter name="enderecoEmpresa" class="java.lang.String"/>
	<parameter name="cidadeEmpresa" class="java.lang.String"/>
	<parameter name="dataIni" class="java.lang.String" isForPrompting="false"/>
	<parameter name="dataFim" class="java.lang.String" isForPrompting="false"/>
	<parameter name="SUBREPORT_DIR1" class="java.lang.String"/>
	<field name="colaborador.codigo" class="java.lang.Integer">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="intervaloDataHoras" class="java.lang.String"/>
	<field name="colaborador.pessoa.nome" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="dataHoraEntrada" class="java.util.Date">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="dataHoraSaida" class="java.util.Date">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="sentido" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="meioIdentificacaoEntrada.descricao" class="java.lang.String"/>
	<field name="localAcesso.empresa.nome" class="java.lang.String"/>
	<field name="colaborador.pessoa.email" class="java.lang.String"/>
	<variable name="totalcolaboradores" class="java.lang.Integer" calculation="Count">
		<variableExpression><![CDATA[$F{colaborador.codigo}]]></variableExpression>
	</variable>
	<group name="nomeColaborador">
		<groupExpression><![CDATA[$F{colaborador.pessoa.nome}]]></groupExpression>
		<groupHeader>
			<band height="16">
				<textField isStretchWithOverflow="true" isBlankWhenNull="true">
					<reportElement key="textField-224" x="8" y="1" width="630" height="15" uuid="4d5b9c06-2346-4070-bf10-1c080445a2c5"/>
					<textElement>
						<font size="10" isBold="true" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{colaborador.pessoa.nome} + ($F{colaborador.pessoa.email} == null || $F{colaborador.pessoa.email}.trim().equals("") ? "" :  (" - " + $F{colaborador.pessoa.email}))]]></textFieldExpression>
				</textField>
			</band>
		</groupHeader>
		<groupFooter>
			<band height="18">
				<staticText>
					<reportElement x="368" y="3" width="93" height="15" uuid="c364a103-7771-4097-bd49-a40b073f0182"/>
					<textElement>
						<font size="9" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<text><![CDATA[Total de Acessos:]]></text>
				</staticText>
				<textField>
					<reportElement x="461" y="3" width="143" height="15" uuid="542198cc-3dd0-415b-93d5-0ef9e82e3647"/>
					<textElement>
						<font size="9"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{nomeColaborador_COUNT}]]></textFieldExpression>
				</textField>
			</band>
		</groupFooter>
	</group>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band splitType="Stretch"/>
	</title>
	<pageHeader>
		<band height="69" splitType="Stretch">
			<textField evaluationTime="Report" isBlankWhenNull="false">
				<reportElement key="textField-212" x="604" y="51" width="31" height="17" uuid="bab582cf-b4a1-42e0-9e78-eec89878e7e6"/>
				<box bottomPadding="2">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement>
					<font fontName="Arial" size="10" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression><![CDATA[" " + $V{PAGE_NUMBER} + ""]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement key="staticText-15" mode="Opaque" x="522" y="35" width="111" height="12" uuid="c3172e08-8f93-4afa-946c-2b15a2423be8"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Top">
					<font fontName="Microsoft Sans Serif" size="9" isBold="true" isItalic="true" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica-BoldOblique"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<text><![CDATA[(62) 3414-0314]]></text>
			</staticText>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-211" x="524" y="51" width="75" height="17" uuid="bbdb51f8-9b8b-4769-bbfa-f4362acefcb5"/>
				<box bottomPadding="2">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right">
					<font fontName="Arial" size="10" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression><![CDATA["Página: " + $V{PAGE_NUMBER} + " de "]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement key="staticText-13" x="244" y="35" width="189" height="27" uuid="08fa8447-5f3c-40c3-9a23-228f835c20f3"/>
				<textElement textAlignment="Center">
					<font size="20" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Lista de Acessos]]></text>
			</staticText>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-209" x="86" y="31" width="113" height="14" uuid="b336f389-0748-4688-86b3-b6fb2b2c0639"/>
				<textElement>
					<font isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{enderecoEmpresa}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-208" x="86" y="15" width="113" height="14" uuid="2d8e1293-4220-4a0b-a8fc-aa61842e0476"/>
				<textElement>
					<font isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{nomeEmpresa}]]></textFieldExpression>
			</textField>
			<image isUsingCache="true" onErrorType="Blank" evaluationTime="Page">
				<reportElement key="image-1" x="0" y="16" width="82" height="46" isPrintWhenDetailOverflows="true" uuid="f8e41ff2-fc17-4f23-a03e-d92f01bbe5b7"/>
				<box>
					<topPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<imageExpression><![CDATA[$P{logoPadraoRelatorio}]]></imageExpression>
			</image>
			<staticText>
				<reportElement key="staticText-14" mode="Opaque" x="371" y="6" width="262" height="23" uuid="2e781c65-b361-46e5-933b-27716c12ecfc"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Top">
					<font fontName="Microsoft Sans Serif" size="9" isBold="true" isItalic="true" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica-BoldOblique"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<text><![CDATA[Pacto - Sistema Administrativo para Academias Desenvolvido por PACTO Soluções Tecnológicas Ltda.]]></text>
			</staticText>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-210" x="86" y="47" width="113" height="14" uuid="a9e31c19-d585-4e26-b38d-7cd9c8029a9e"/>
				<textElement>
					<font isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{cidadeEmpresa}]]></textFieldExpression>
			</textField>
		</band>
	</pageHeader>
	<columnHeader>
		<band height="54" splitType="Stretch">
			<line>
				<reportElement key="line-1" x="0" y="2" width="640" height="1" uuid="45e0c789-c593-4d30-b25e-400953be837f"/>
			</line>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-214" x="0" y="3" width="640" height="30" uuid="28916175-0a42-4d0e-bfe0-3443366c56c5"/>
				<box>
					<topPen lineWidth="0.5" lineStyle="Solid"/>
					<leftPen lineWidth="0.5" lineStyle="Solid"/>
					<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.5" lineStyle="Solid"/>
				</box>
				<textElement textAlignment="Center">
					<font fontName="Arial" size="10" isBold="true" isItalic="true" pdfFontName="Helvetica-BoldOblique"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{filtros}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement key="staticText-2" x="1" y="39" width="40" height="14" uuid="8e7d483a-87ed-4fd2-ae7b-495205f77197"/>
				<textElement>
					<font isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Código]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-8" x="41" y="39" width="88" height="14" uuid="9c2d5a4c-074b-44de-b894-df9a7345e298"/>
				<textElement>
					<font isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Data Entrada]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-8" x="129" y="39" width="125" height="14" uuid="49823eb7-c544-464d-9e36-1dc84954eb85"/>
				<textElement>
					<font isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Data Saída]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-8" x="254" y="39" width="52" height="14" uuid="e35dc508-645e-4910-ab2b-fb9a068f3359"/>
				<textElement>
					<font isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Tempo]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-8" x="306" y="39" width="45" height="14" uuid="50b18c7c-a3cc-4251-a9f0-580b1a883f37"/>
				<textElement>
					<font isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Sentido]]></text>
			</staticText>
			<line>
				<reportElement key="line-1" x="0" y="53" width="640" height="1" uuid="13fdaec1-651b-4986-b644-f2f0bd8f1b06"/>
			</line>
			<staticText>
				<reportElement key="staticText-8" x="351" y="39" width="148" height="14" uuid="28c601be-d854-489b-9af5-984c6479cd40"/>
				<textElement>
					<font isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Meio Identificação]]></text>
			</staticText>
		</band>
	</columnHeader>
	<detail>
		<band height="15" splitType="Stretch">
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement key="textField-223" x="254" y="1" width="52" height="14" uuid="313459cb-e7d7-4210-9c50-94b62402d256"/>
				<textFieldExpression><![CDATA[$F{intervaloDataHoras}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement key="textField" positionType="Float" x="306" y="1" width="43" height="14" uuid="2cd1d96e-db4f-4c39-bbf8-71b7c6be2033"/>
				<textElement textAlignment="Left" verticalAlignment="Top"/>
				<textFieldExpression><![CDATA[$F{sentido}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="dd/MM/yyyy HH.mm" isBlankWhenNull="true">
				<reportElement key="textField-224" x="129" y="1" width="125" height="14" uuid="8dea6e39-fb7c-45f2-af6d-e3a7411689d4"/>
				<textFieldExpression><![CDATA[$F{dataHoraSaida}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="dd/MM/yyyy HH.mm" isBlankWhenNull="true">
				<reportElement key="textField-224" x="41" y="1" width="91" height="14" uuid="cec54c2f-4c20-4e02-886f-cf0c393a0b3f"/>
				<textElement>
					<font size="10" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{dataHoraEntrada}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement key="textField-223" x="1" y="1" width="40" height="14" uuid="25f195e4-8aab-48ba-bd05-29cf74b2e7ef"/>
				<textFieldExpression><![CDATA[$F{colaborador.codigo}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement key="textField" x="351" y="1" width="148" height="14" uuid="5011794e-61d5-4632-aac0-a9b7df53c96c"/>
				<textElement textAlignment="Left" verticalAlignment="Top"/>
				<textFieldExpression><![CDATA[$F{meioIdentificacaoEntrada.descricao}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
	<columnFooter>
		<band splitType="Stretch"/>
	</columnFooter>
	<pageFooter>
		<band splitType="Stretch"/>
	</pageFooter>
	<lastPageFooter>
		<band height="75" splitType="Stretch">
			<staticText>
				<reportElement x="369" y="20" width="123" height="20" uuid="7ea1b5e4-f39e-4f2c-9cdb-ef6f4e9d2504"/>
				<textElement>
					<font pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Total Geral de Acessos:]]></text>
			</staticText>
			<line>
				<reportElement key="line-4" x="1" y="11" width="640" height="1" uuid="ba090957-04c0-4e2b-aec6-60f3db6d07e1"/>
			</line>
			<line>
				<reportElement key="line-5" x="1" y="49" width="640" height="1" uuid="db6d5970-18ce-4e57-8288-073634837aee"/>
			</line>
			<line>
				<reportElement key="line-6" x="1" y="51" width="640" height="1" uuid="6cb288aa-9f1d-45af-b75f-7115ea45af99"/>
			</line>
			<textField pattern="" isBlankWhenNull="false">
				<reportElement key="textField-207" x="1" y="55" width="640" height="19" uuid="7879dd3d-4952-44bb-9c79-a4dcf2f93973"/>
				<box>
					<topPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="8" isItalic="true" pdfFontName="Helvetica-Oblique"/>
				</textElement>
				<textFieldExpression><![CDATA[" "+" Usuário: " + $P{nomeUsuario}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="492" y="20" width="100" height="20" uuid="5b6ede92-79a7-431d-a979-f14222c43c56"/>
				<textFieldExpression><![CDATA[$V{totalcolaboradores}]]></textFieldExpression>
			</textField>
			<textField pattern="dd/MM/yyyy HH.mm.ss" isBlankWhenNull="true">
				<reportElement key="dataRel-1" mode="Opaque" x="512" y="59" width="126" height="13" backcolor="#FFFFFF" uuid="ec25c079-4e17-4796-b139-94d35a759d6d"/>
				<box bottomPadding="2">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="false" isItalic="true" isUnderline="false" pdfFontName="Helvetica-Oblique"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[new Date()]]></textFieldExpression>
			</textField>
		</band>
	</lastPageFooter>
	<summary>
		<band splitType="Stretch"/>
	</summary>
</jasperReport>
