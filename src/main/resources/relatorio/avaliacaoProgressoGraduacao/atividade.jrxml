<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.5.1.final using JasperReports Library version 6.5.1  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="Anamnese" pageWidth="550" pageHeight="842" columnWidth="550" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0" isSummaryNewPage="true" uuid="4e97cda0-7e4a-494e-b0d4-6ae08374f035">
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="One Empty Record"/>
	<parameter name="valor4" class="java.lang.String"/>
	<parameter name="sub_atividadeJR" class="java.lang.Object"/>
	<parameter name="SUBREPORT_DIR" class="java.lang.String"/>
	<queryString>
		<![CDATA[]]>
	</queryString>
	<field name="valor1" class="java.lang.String"/>
	<field name="valor2" class="java.lang.String"/>
	<field name="valor3" class="java.lang.String"/>
	<field name="sub_atividadeJR" class="java.lang.Object"/>
	<field name="SUBREPORT_DIR" class="java.lang.String"/>
	<field name="valor4" class="java.lang.String"/>
	<field name="possuiSubAtividade" class="java.lang.Boolean"/>
	<background>
		<band splitType="Stretch"/>
	</background>
	<detail>
		<band height="210" splitType="Stretch">
			<textField isStretchWithOverflow="true">
				<reportElement positionType="Float" stretchType="RelativeToTallestObject" x="0" y="10" width="530" height="20" forecolor="#6F747B" uuid="8a6a2cd8-b7a8-42c5-9c24-02b38b38c8d6"/>
				<textElement textAlignment="Justified">
					<font fontName="Arial" size="14" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{valor1}]]></textFieldExpression>
			</textField>
			<frame>
				<reportElement x="20" y="44" width="511" height="150" uuid="04d66134-5615-4a14-9274-b25e67bf511c"/>
				<image hAlign="Center" vAlign="Top" onErrorType="Icon">
					<reportElement x="0" y="2" width="67" height="67" uuid="d67f1cd7-9e5b-4a26-bb09-f9ac021bfe9a"/>
					<imageExpression><![CDATA[$F{valor4}]]></imageExpression>
				</image>
				<textField isStretchWithOverflow="true" isBlankWhenNull="true">
					<reportElement x="80" y="0" width="430" height="30" isRemoveLineWhenBlank="true" uuid="dcb8f5e9-32b2-48dc-9253-7a937b96cc5d"/>
					<textElement>
						<font size="11"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{valor2} != null && !$F{valor2}.trim().isEmpty() ? $F{valor2} : ""]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" isBlankWhenNull="true">
					<reportElement x="81" y="110" width="430" height="25" isRemoveLineWhenBlank="true" uuid="20502ea4-f65e-4899-8fc0-4d0128f88546">
						<printWhenExpression><![CDATA[$F{possuiSubAtividade} == null || !$F{possuiSubAtividade}]]></printWhenExpression>
					</reportElement>
					<textElement>
						<font size="10" isItalic="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{valor3} != null && !$F{valor3}.trim().isEmpty() ? $F{valor3} : ""]]></textFieldExpression>
				</textField>
				<subreport>
					<reportElement x="80" y="50" width="430" height="90" uuid="dbe5a69a-970b-4720-a7e9-7cd6980a935f">
						<printWhenExpression><![CDATA[$F{possuiSubAtividade} == true]]></printWhenExpression>
					</reportElement>
					<dataSourceExpression><![CDATA[$F{sub_atividadeJR}]]></dataSourceExpression>
					<subreportExpression><![CDATA[$F{SUBREPORT_DIR} + "sub_atividade.jasper"]]></subreportExpression>
				</subreport>
			</frame>
		</band>
	</detail>
</jasperReport>
