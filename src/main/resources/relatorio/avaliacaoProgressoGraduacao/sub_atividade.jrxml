<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.5.1.final using JasperReports Library version 6.5.1  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="sub_atividade" pageWidth="550" pageHeight="842" columnWidth="550" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0" isSummaryNewPage="true" uuid="4e97cda0-7e4a-494e-b0d4-6ae08374f035">
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="One Empty Record"/>
	<style name="EstiloVermelho" forecolor="#DB2C3D"/>
	<style name="EstiloVerde" forecolor="#1DC06F"/>
	<style name="EstiloPreto" forecolor="#000000"/>
	<queryString>
		<![CDATA[]]>
	</queryString>
	<field name="valor1" class="java.lang.String"/>
	<field name="valor2" class="java.lang.String"/>
	<field name="valor3" class="java.lang.String"/>
	<background>
		<band splitType="Stretch"/>
	</background>
	<detail>
		<band height="105" splitType="Stretch">
			<frame>
				<reportElement x="0" y="0" width="550" height="70" uuid="c86a170c-9f0e-4e75-880f-7009637eae82"/>
				<textField isStretchWithOverflow="true">
					<reportElement key="" x="202" y="20" width="120" height="24" forecolor="#000000" uuid="b8e93d23-3ced-4c07-8985-b0f32d9882b4"/>
					<textElement textAlignment="Justified" verticalAlignment="Middle">
						<font fontName="Arial" size="12"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{valor1}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true">
					<reportElement x="0" y="70" width="430" height="30" forecolor="#000000" uuid="2bdd0443-adf1-42b7-88b3-049bbee12c6f"/>
					<textElement textAlignment="Justified" verticalAlignment="Middle" markup="styled">
						<font fontName="Arial" size="12"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{valor2}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement x="0" y="0" width="100" height="19" forecolor="#6F747B" uuid="cb7e9a4f-b891-4aef-b9fb-b6aa5f923a4a"/>
					<textElement textAlignment="Justified" verticalAlignment="Middle">
						<font fontName="Arial" size="12"/>
					</textElement>
					<text><![CDATA[Sub Atividade: ]]></text>
				</staticText>
				<staticText>
					<reportElement x="202" y="0" width="100" height="19" forecolor="#6F747B" uuid="b2e072e2-1885-40d7-a983-b2f19f174585"/>
					<textElement textAlignment="Justified" verticalAlignment="Middle">
						<font fontName="Arial" size="12"/>
					</textElement>
					<text><![CDATA[Resposta: ]]></text>
				</staticText>
				<textField isStretchWithOverflow="true" isBlankWhenNull="true">
					<reportElement x="0" y="21" width="190" height="22" isRemoveLineWhenBlank="true" forecolor="#000000" uuid="9620db7a-c1d6-4d2f-983d-a8689572592d"/>
					<textElement textAlignment="Justified" verticalAlignment="Middle">
						<font fontName="Arial" size="12" isItalic="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{valor3} != null && !$F{valor3}.trim().isEmpty() ? $F{valor3}.replaceAll("^.*?\\.\\s+", "") : ""]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="0" y="51" width="136" height="19" forecolor="#6F747B" uuid="8c3a3235-097d-4f17-a3bb-6a6a3023f678"/>
					<textElement textAlignment="Justified" verticalAlignment="Middle">
						<font fontName="Arial" size="12"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{valor2} != null && !$F{valor2}.trim().isEmpty() ? "Observação: " : ""]]></textFieldExpression>
				</textField>
			</frame>
		</band>
	</detail>
</jasperReport>
