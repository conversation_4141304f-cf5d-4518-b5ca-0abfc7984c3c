�� sr (net.sf.jasperreports.engine.JasperReport      '� L compileDatat Ljava/io/Serializable;L compileNameSuffixt Ljava/lang/String;L 
compilerClassq ~ xr -net.sf.jasperreports.engine.base.JRBaseReport      '� +I PSEUDO_SERIAL_VERSION_UIDI bottomMarginI columnCountI 
columnSpacingI columnWidthZ ignorePaginationZ isFloatColumnFooterZ isSummaryNewPageZ  isSummaryWithPageHeaderAndFooterZ isTitleNewPageI 
leftMarginB orientationI 
pageHeightI 	pageWidthB 
printOrderI rightMarginI 	topMarginB whenNoDataTypeL 
backgroundt $Lnet/sf/jasperreports/engine/JRBand;L columnDirectiont 3Lnet/sf/jasperreports/engine/type/RunDirectionEnum;L columnFooterq ~ L columnHeaderq ~ [ datasetst ([Lnet/sf/jasperreports/engine/JRDataset;L defaultStylet %Lnet/sf/jasperreports/engine/JRStyle;L detailq ~ L 
detailSectiont 'Lnet/sf/jasperreports/engine/JRSection;L formatFactoryClassq ~ L 
importsSett Ljava/util/Set;L languageq ~ L lastPageFooterq ~ L mainDatasett 'Lnet/sf/jasperreports/engine/JRDataset;L nameq ~ L noDataq ~ L orientationValuet 2Lnet/sf/jasperreports/engine/type/OrientationEnum;L 
pageFooterq ~ L 
pageHeaderq ~ L printOrderValuet 1Lnet/sf/jasperreports/engine/type/PrintOrderEnum;L sectionTypet 2Lnet/sf/jasperreports/engine/type/SectionTypeEnum;[ stylest &[Lnet/sf/jasperreports/engine/JRStyle;L summaryq ~ [ 	templatest /[Lnet/sf/jasperreports/engine/JRReportTemplate;L titleq ~ L whenNoDataTypeValuet 5Lnet/sf/jasperreports/engine/type/WhenNoDataTypeEnum;xp  �                        J            p~r 1net.sf.jasperreports.engine.type.RunDirectionEnum          xr java.lang.Enum          xpt LTRpsr +net.sf.jasperreports.engine.base.JRBaseBand      '� I PSEUDO_SERIAL_VERSION_UIDI heightZ isSplitAllowedL printWhenExpressiont *Lnet/sf/jasperreports/engine/JRExpression;L 
propertiesMapt -Lnet/sf/jasperreports/engine/JRPropertiesMap;L returnValuest Ljava/util/List;L 	splitTypet Ljava/lang/Byte;L splitTypeValuet 0Lnet/sf/jasperreports/engine/type/SplitTypeEnum;xr 3net.sf.jasperreports.engine.base.JRBaseElementGroup      '� L childrenq ~ L elementGroupt ,Lnet/sf/jasperreports/engine/JRElementGroup;xpsr java.util.ArrayListx����a� I sizexp   w   sr 1net.sf.jasperreports.engine.base.JRBaseStaticText      '� L textq ~ xr 2net.sf.jasperreports.engine.base.JRBaseTextElement      '� I PSEUDO_SERIAL_VERSION_UIDL fontNameq ~ L fontSizet Ljava/lang/Integer;L fontsizet Ljava/lang/Float;L horizontalAlignmentq ~ L horizontalAlignmentValuet 6Lnet/sf/jasperreports/engine/type/HorizontalAlignEnum;L horizontalTextAlignt :Lnet/sf/jasperreports/engine/type/HorizontalTextAlignEnum;L isBoldt Ljava/lang/Boolean;L isItalicq ~ 'L 
isPdfEmbeddedq ~ 'L isStrikeThroughq ~ 'L isStyledTextq ~ 'L isUnderlineq ~ 'L lineBoxt 'Lnet/sf/jasperreports/engine/JRLineBox;L lineSpacingq ~ L lineSpacingValuet 2Lnet/sf/jasperreports/engine/type/LineSpacingEnum;L markupq ~ L 	paragrapht )Lnet/sf/jasperreports/engine/JRParagraph;L pdfEncodingq ~ L pdfFontNameq ~ L rotationq ~ L 
rotationValuet /Lnet/sf/jasperreports/engine/type/RotationEnum;L verticalAlignmentq ~ L verticalAlignmentValuet 4Lnet/sf/jasperreports/engine/type/VerticalAlignEnum;L verticalTextAlignt 8Lnet/sf/jasperreports/engine/type/VerticalTextAlignEnum;xr .net.sf.jasperreports.engine.base.JRBaseElement      '� I PSEUDO_SERIAL_VERSION_UIDI heightZ isPrintInFirstWholeBandZ isPrintRepeatedValuesZ isPrintWhenDetailOverflowsZ isRemoveLineWhenBlankB positionTypeB stretchTypeI widthI xI yL 	backcolort Ljava/awt/Color;L defaultStyleProvidert 4Lnet/sf/jasperreports/engine/JRDefaultStyleProvider;L elementGroupq ~ L 	forecolorq ~ /L keyq ~ L modeq ~ L 	modeValuet +Lnet/sf/jasperreports/engine/type/ModeEnum;L parentStyleq ~ L parentStyleNameReferenceq ~ L positionTypeValuet 3Lnet/sf/jasperreports/engine/type/PositionTypeEnum;L printWhenExpressionq ~ L printWhenGroupChangest %Lnet/sf/jasperreports/engine/JRGroup;L 
propertiesMapq ~ [ propertyExpressionst 3[Lnet/sf/jasperreports/engine/JRPropertyExpression;L stretchTypeValuet 2Lnet/sf/jasperreports/engine/type/StretchTypeEnum;L uuidt Ljava/util/UUID;xp  �           B   �   pq ~ q ~ pppppp~r 1net.sf.jasperreports.engine.type.PositionTypeEnum          xq ~ t FIX_RELATIVE_TO_TOPpppp~r 0net.sf.jasperreports.engine.type.StretchTypeEnum          xq ~ t 
NO_STRETCHsr java.util.UUID����m�/ J leastSigBitsJ mostSigBitsxp�8��Ap�*�%�.M/  �ppppppsr java.lang.Boolean� r�՜�� Z valuexppppppsr .net.sf.jasperreports.engine.base.JRBaseLineBox      '� L 
bottomPaddingq ~ #L 	bottomPent +Lnet/sf/jasperreports/engine/base/JRBoxPen;L boxContainert ,Lnet/sf/jasperreports/engine/JRBoxContainer;L leftPaddingq ~ #L leftPenq ~ CL paddingq ~ #L penq ~ CL rightPaddingq ~ #L rightPenq ~ CL 
topPaddingq ~ #L topPenq ~ Cxppsr 3net.sf.jasperreports.engine.base.JRBaseBoxBottomPen      '�  xr -net.sf.jasperreports.engine.base.JRBaseBoxPen      '� L lineBoxq ~ (xr *net.sf.jasperreports.engine.base.JRBasePen      '� I PSEUDO_SERIAL_VERSION_UIDL 	lineColorq ~ /L 	lineStyleq ~ L lineStyleValuet 0Lnet/sf/jasperreports/engine/type/LineStyleEnum;L 	lineWidthq ~ $L penContainert ,Lnet/sf/jasperreports/engine/JRPenContainer;xp  �ppppq ~ Eq ~ Eq ~ 7psr 1net.sf.jasperreports.engine.base.JRBaseBoxLeftPen      '�  xq ~ G  �ppppq ~ Eq ~ Epsq ~ G  �ppppq ~ Eq ~ Epsr 2net.sf.jasperreports.engine.base.JRBaseBoxRightPen      '�  xq ~ G  �ppppq ~ Eq ~ Epsr 0net.sf.jasperreports.engine.base.JRBaseBoxTopPen      '�  xq ~ G  �ppppq ~ Eq ~ Epppsr 0net.sf.jasperreports.engine.base.JRBaseParagraph      '� 
L firstLineIndentq ~ #L 
leftIndentq ~ #L lineSpacingq ~ )L lineSpacingSizeq ~ $L paragraphContainert 2Lnet/sf/jasperreports/engine/JRParagraphContainer;L rightIndentq ~ #L spacingAfterq ~ #L 
spacingBeforeq ~ #L tabStopWidthq ~ #L tabStopsq ~ xpppppq ~ 7ppppppppppppt 
Esquerda (cm)sq ~ !  �           B   �   pq ~ q ~ ppppppq ~ 9ppppq ~ <sq ~ >�Bo���
�I���E�  �ppppppq ~ Apppppsq ~ Bpsq ~ F  �ppppq ~ Yq ~ Yq ~ Wpsq ~ L  �ppppq ~ Yq ~ Ypsq ~ G  �ppppq ~ Yq ~ Ypsq ~ O  �ppppq ~ Yq ~ Ypsq ~ Q  �ppppq ~ Yq ~ Ypppsq ~ Sppppq ~ Wppppppppppppt Direita (cm)xp  �   ppppppppsr .net.sf.jasperreports.engine.base.JRBaseSection      '� [ bandst %[Lnet/sf/jasperreports/engine/JRBand;[ partst %[Lnet/sf/jasperreports/engine/JRPart;xpur %[Lnet.sf.jasperreports.engine.JRBand;��~�ʅ5  xp   sq ~ sq ~    w   sr 0net.sf.jasperreports.engine.base.JRBaseTextField      '� I PSEUDO_SERIAL_VERSION_UIDI 
bookmarkLevelB evaluationTimeB hyperlinkTargetB 
hyperlinkTypeZ isStretchWithOverflowL anchorNameExpressionq ~ L bookmarkLevelExpressionq ~ L evaluationGroupq ~ 3L evaluationTimeValuet 5Lnet/sf/jasperreports/engine/type/EvaluationTimeEnum;L 
expressionq ~ L hyperlinkAnchorExpressionq ~ L hyperlinkPageExpressionq ~ [ hyperlinkParameterst 3[Lnet/sf/jasperreports/engine/JRHyperlinkParameter;L hyperlinkReferenceExpressionq ~ L hyperlinkTooltipExpressionq ~ L hyperlinkWhenExpressionq ~ L isBlankWhenNullq ~ 'L 
linkTargetq ~ L linkTypeq ~ L patternq ~ L patternExpressionq ~ L 
textAdjustt 1Lnet/sf/jasperreports/engine/type/TextAdjustEnum;xq ~ "  �           �        pq ~ q ~ gppppppq ~ 9ppppq ~ <sq ~ >�]��rdש�=9eA�  �ppppppppppppsq ~ Bpsq ~ F  �sr java.awt.Color���3u F falphaI valueL cst Ljava/awt/color/ColorSpace;[ 	frgbvaluet [F[ fvalueq ~ sxp    ����pppppsr java.lang.Float��ɢ�<�� F valuexr java.lang.Number������  xp?�  q ~ oq ~ oq ~ mpsq ~ L  �ppppq ~ oq ~ opsq ~ G  �ppppq ~ oq ~ opsq ~ O  �ppppq ~ oq ~ opsq ~ Q  �ppppq ~ oq ~ opppsq ~ Sppppq ~ mpppppppppppp  �        ppp~r 3net.sf.jasperreports.engine.type.EvaluationTimeEnum          xq ~ t NOWsr 1net.sf.jasperreports.engine.base.JRBaseExpression      '� I id[ chunkst 0[Lnet/sf/jasperreports/engine/JRExpressionChunk;L typet 5Lnet/sf/jasperreports/engine/type/ExpressionTypeEnum;L valueClassNameq ~ L valueClassRealNameq ~ xp   ur 0[Lnet.sf.jasperreports.engine.JRExpressionChunk;mY��iK�U  xp   sr 6net.sf.jasperreports.engine.base.JRBaseExpressionChunk      '� B typeL textq ~ xpt "  "+sq ~ �t valor1pppppppppppppp~r /net.sf.jasperreports.engine.type.TextAdjustEnum          xq ~ t STRETCH_HEIGHTsq ~ i  �           B   �    pq ~ q ~ gppppppq ~ 9ppppq ~ <sq ~ >�ݷ��M��8
/�N  �ppppppppppppsq ~ Bpsq ~ F  �sq ~ q    ����pppppsq ~ u?�  q ~ �q ~ �q ~ �psq ~ L  �ppppq ~ �q ~ �psq ~ G  �ppppq ~ �q ~ �psq ~ O  �ppppq ~ �q ~ �psq ~ Q  �ppppq ~ �q ~ �pppsq ~ Sppppq ~ �pppppppppppp  �        pppq ~ ~sq ~ �   	uq ~ �   sq ~ �t valor2ppppppppppppppq ~ �sq ~ i  �           B   �    pq ~ q ~ gppppppq ~ 9ppppq ~ <sq ~ >�F ��F�t�A  �ppppppppppppsq ~ Bpsq ~ F  �sq ~ q    ����pppppsq ~ u?�  q ~ �q ~ �q ~ �psq ~ L  �ppppq ~ �q ~ �psq ~ G  �ppppq ~ �q ~ �psq ~ O  �ppppq ~ �q ~ �psq ~ Q  �ppppq ~ �q ~ �pppsq ~ Sppppq ~ �pppppppppppp  �        pppq ~ ~sq ~ �   
uq ~ �   sq ~ �t valor3ppppppppppppppq ~ �xp  �   pppp~r .net.sf.jasperreports.engine.type.SplitTypeEnum          xq ~ t STRETCHpppt javapsr .net.sf.jasperreports.engine.base.JRBaseDataset      '� I PSEUDO_SERIAL_VERSION_UIDZ isMainB whenResourceMissingType[ fieldst &[Lnet/sf/jasperreports/engine/JRField;L filterExpressionq ~ [ groupst &[Lnet/sf/jasperreports/engine/JRGroup;L nameq ~ [ 
parameterst *[Lnet/sf/jasperreports/engine/JRParameter;L 
propertiesMapq ~ [ propertyExpressionst 8[Lnet/sf/jasperreports/engine/DatasetPropertyExpression;L queryt %Lnet/sf/jasperreports/engine/JRQuery;L resourceBundleq ~ L scriptletClassq ~ [ 
scriptletst *[Lnet/sf/jasperreports/engine/JRScriptlet;[ 
sortFieldst *[Lnet/sf/jasperreports/engine/JRSortField;L uuidq ~ 6[ 	variablest )[Lnet/sf/jasperreports/engine/JRVariable;L whenResourceMissingTypeValuet >Lnet/sf/jasperreports/engine/type/WhenResourceMissingTypeEnum;xp  � ur &[Lnet.sf.jasperreports.engine.JRField;<��N*�p  xp   sr ,net.sf.jasperreports.engine.base.JRBaseField      '� L descriptionq ~ L nameq ~ L 
propertiesMapq ~ [ propertyExpressionsq ~ 4L valueClassNameq ~ L valueClassRealNameq ~ xppt valor1sr +net.sf.jasperreports.engine.JRPropertiesMap      '� L baseq ~ L propertiesListq ~ L 
propertiesMapt Ljava/util/Map;xpppppt java.lang.Stringpsq ~ �pt valor2sq ~ �ppppt java.lang.Stringpsq ~ �pt valor3sq ~ �ppppt java.lang.Stringpppt 
Perimetriaur *[Lnet.sf.jasperreports.engine.JRParameter;" �*�`!  xp   sr 0net.sf.jasperreports.engine.base.JRBaseParameter      '� 
Z isForPromptingZ isSystemDefinedL defaultValueExpressionq ~ L descriptionq ~ L evaluationTimet >Lnet/sf/jasperreports/engine/type/ParameterEvaluationTimeEnum;L nameq ~ L nestedTypeNameq ~ L 
propertiesMapq ~ L valueClassNameq ~ L valueClassRealNameq ~ xppppt REPORT_CONTEXTpsq ~ �pppt )net.sf.jasperreports.engine.ReportContextpsq ~ �pppt REPORT_PARAMETERS_MAPpsq ~ �pppt 
java.util.Mappsq ~ �pppt JASPER_REPORTS_CONTEXTpsq ~ �pppt 0net.sf.jasperreports.engine.JasperReportsContextpsq ~ �pppt 
JASPER_REPORTpsq ~ �pppt (net.sf.jasperreports.engine.JasperReportpsq ~ �pppt REPORT_CONNECTIONpsq ~ �pppt java.sql.Connectionpsq ~ �pppt REPORT_MAX_COUNTpsq ~ �pppt java.lang.Integerpsq ~ �pppt REPORT_DATA_SOURCEpsq ~ �pppt (net.sf.jasperreports.engine.JRDataSourcepsq ~ �pppt REPORT_SCRIPTLETpsq ~ �pppt /net.sf.jasperreports.engine.JRAbstractScriptletpsq ~ �pppt 
REPORT_LOCALEpsq ~ �pppt java.util.Localepsq ~ �pppt REPORT_RESOURCE_BUNDLEpsq ~ �pppt java.util.ResourceBundlepsq ~ �pppt REPORT_TIME_ZONEpsq ~ �pppt java.util.TimeZonepsq ~ �pppt REPORT_FORMAT_FACTORYpsq ~ �pppt .net.sf.jasperreports.engine.util.FormatFactorypsq ~ �pppt REPORT_CLASS_LOADERpsq ~ �pppt java.lang.ClassLoaderpsq ~ �pppt REPORT_TEMPLATESpsq ~ �pppt java.util.Collectionpsq ~ �pppt SORT_FIELDSpsq ~ �pppt java.util.Listpsq ~ �pppt FILTERpsq ~ �pppt )net.sf.jasperreports.engine.DatasetFilterpsq ~ �pppt REPORT_VIRTUALIZERpsq ~ �pppt )net.sf.jasperreports.engine.JRVirtualizerpsq ~ �pppt IS_IGNORE_PAGINATIONpsq ~ �pppt java.lang.Booleanpsq ~ �psq ~    
w   
t -com.jaspersoft.studio.data.defaultdataadaptert com.jaspersoft.studio.unit.t %com.jaspersoft.studio.unit.pageHeightt $com.jaspersoft.studio.unit.pageWidtht $com.jaspersoft.studio.unit.topMargint 'com.jaspersoft.studio.unit.bottomMargint %com.jaspersoft.studio.unit.leftMargint &com.jaspersoft.studio.unit.rightMargint &com.jaspersoft.studio.unit.columnWidtht (com.jaspersoft.studio.unit.columnSpacingxsr java.util.HashMap���`� F 
loadFactorI 	thresholdxp?@     w      
q ~ t pixelq ~!t pixelq ~t New Data Adapter q ~t pixelq ~t pixelq ~$t pixelq ~t pixelq ~"t pixelq ~t pixelq ~#t pixelxpsr ,net.sf.jasperreports.engine.base.JRBaseQuery      '� [ chunkst +[Lnet/sf/jasperreports/engine/JRQueryChunk;L languageq ~ xppt sqlppppsq ~ >��j��t�5N�͠~JINur )[Lnet.sf.jasperreports.engine.JRVariable;b�|�,�D  xp   sr /net.sf.jasperreports.engine.base.JRBaseVariable      '� I PSEUDO_SERIAL_VERSION_UIDB calculationB 
incrementTypeZ isSystemDefinedB 	resetTypeL calculationValuet 2Lnet/sf/jasperreports/engine/type/CalculationEnum;L descriptionq ~ L 
expressionq ~ L incrementGroupq ~ 3L incrementTypeValuet 4Lnet/sf/jasperreports/engine/type/IncrementTypeEnum;L incrementerFactoryClassNameq ~ L incrementerFactoryClassRealNameq ~ L initialValueExpressionq ~ L nameq ~ L 
resetGroupq ~ 3L resetTypeValuet 0Lnet/sf/jasperreports/engine/type/ResetTypeEnum;L valueClassNameq ~ L valueClassRealNameq ~ xp  w�   ~r 0net.sf.jasperreports.engine.type.CalculationEnum          xq ~ t SYSTEMppp~r 2net.sf.jasperreports.engine.type.IncrementTypeEnum          xq ~ t NONEppsq ~ �    uq ~ �   sq ~ �t new java.lang.Integer(1)pppt PAGE_NUMBERp~r .net.sf.jasperreports.engine.type.ResetTypeEnum          xq ~ t REPORTq ~ �psq ~8  w�   q ~>pppq ~Apppt MASTER_CURRENT_PAGEpq ~Iq ~ �psq ~8  w�   q ~>pppq ~Apppt MASTER_TOTAL_PAGESpq ~Iq ~ �psq ~8  w�   q ~>pppq ~Appsq ~ �   uq ~ �   sq ~ �t new java.lang.Integer(1)pppt 
COLUMN_NUMBERp~q ~Ht PAGEq ~ �psq ~8  w�   ~q ~=t COUNTpsq ~ �   uq ~ �   sq ~ �t new java.lang.Integer(1)ppppq ~Appsq ~ �   uq ~ �   sq ~ �t new java.lang.Integer(0)pppt REPORT_COUNTpq ~Iq ~ �psq ~8  w�   q ~Xpsq ~ �   uq ~ �   sq ~ �t new java.lang.Integer(1)ppppq ~Appsq ~ �   uq ~ �   sq ~ �t new java.lang.Integer(0)pppt 
PAGE_COUNTpq ~Uq ~ �psq ~8  w�   q ~Xpsq ~ �   uq ~ �   sq ~ �t new java.lang.Integer(1)ppppq ~Appsq ~ �   uq ~ �   sq ~ �t new java.lang.Integer(0)pppt COLUMN_COUNTp~q ~Ht COLUMNq ~ �p~r <net.sf.jasperreports.engine.type.WhenResourceMissingTypeEnum          xq ~ t NULLq ~ �p~r 0net.sf.jasperreports.engine.type.OrientationEnum          xq ~ t PORTRAITpp~r /net.sf.jasperreports.engine.type.PrintOrderEnum          xq ~ t VERTICAL~r 0net.sf.jasperreports.engine.type.SectionTypeEnum          xq ~ t BANDpppppsr 6net.sf.jasperreports.engine.design.JRReportCompileData      '� L crosstabCompileDataq ~ �L datasetCompileDataq ~ �L mainDatasetCompileDataq ~ xpsq ~%?@      w       xsq ~%?@      w       xsr =net.sf.jasperreports.compilers.ReportExpressionEvaluationData      '� L compileDataq ~ L directEvaluationsq ~ �xpur [B���T�  xp  B����   4 H  Perimetria_1698774392304_207237  ,net/sf/jasperreports/engine/fill/JREvaluator field_valor1 .Lnet/sf/jasperreports/engine/fill/JRFillField; <init> ()V Code
    	  
   LineNumberTable customizedInit 0(Ljava/util/Map;Ljava/util/Map;Ljava/util/Map;)V
     
initParams (Ljava/util/Map;)V
     
initFields
     initVars  valor1     
java/util/Map ! " get &(Ljava/lang/Object;)Ljava/lang/Object; $ ,net/sf/jasperreports/engine/fill/JRFillField evaluate (I)Ljava/lang/Object; 
Exceptions ) java/lang/Throwable + java/lang/StringBuilder -   
 * /  0 (Ljava/lang/String;)V
 # 2 3 4 getValue ()Ljava/lang/Object; 6 java/lang/String
 * 8 9 : append -(Ljava/lang/String;)Ljava/lang/StringBuilder;
 * < = > toString ()Ljava/lang/String; 
StackMapTable A java/lang/Object evaluateOld
 # D E 4 getOldValue evaluateEstimated 
SourceFile !                	   *     
*� 
*� �              	      	   4     *+� *,� *-� �           %  & 
 '  (     	         �           0     	   ,     *+�  � #� �       
    8  9     	         �           A  % &  '     ( 	   c     0M�   +         � *Y,� .*� � 1� 5� 7� ;M,�           I  K  O . W ?   	 �  @  B &  '     ( 	   c     0M�   +         � *Y,� .*� � C� 5� 7� ;M,�           `  b  f . n ?   	 �  @  F &  '     ( 	   c     0M�   +         � *Y,� .*� � 1� 5� 7� ;M,�           w  y  } . � ?   	 �  @  G    sq ~%?@     w      
sr java.lang.Integer⠤���8 I valuexq ~ v    sr ;net.sf.jasperreports.compilers.ConstantExpressionEvaluation      '� L valuet Ljava/lang/Object;xpsq ~�   q ~�q ~�sq ~�   q ~�sq ~�   sq ~�q ~�sq ~�   q ~�sq ~�   q ~�sq ~�   q ~�sq ~�   q ~�sq ~�   	sr .net.sf.jasperreports.compilers.FieldEvaluation      '� L nameq ~ xpq ~ �sq ~�   
sq ~�q ~ �xt _1698774392304_207237t 2net.sf.jasperreports.engine.design.JRJavacCompiler