�� sr (net.sf.jasperreports.engine.JasperReport      '� L compileDatat Ljava/io/Serializable;L compileNameSuffixt Ljava/lang/String;L 
compilerClassq ~ xr -net.sf.jasperreports.engine.base.JRBaseReport      '� +I PSEUDO_SERIAL_VERSION_UIDI bottomMarginI columnCountI 
columnSpacingI columnWidthZ ignorePaginationZ isFloatColumnFooterZ isSummaryNewPageZ  isSummaryWithPageHeaderAndFooterZ isTitleNewPageI 
leftMarginB orientationI 
pageHeightI 	pageWidthB 
printOrderI rightMarginI 	topMarginB whenNoDataTypeL 
backgroundt $Lnet/sf/jasperreports/engine/JRBand;L columnDirectiont 3Lnet/sf/jasperreports/engine/type/RunDirectionEnum;L columnFooterq ~ L columnHeaderq ~ [ datasetst ([Lnet/sf/jasperreports/engine/JRDataset;L defaultStylet %Lnet/sf/jasperreports/engine/JRStyle;L detailq ~ L 
detailSectiont 'Lnet/sf/jasperreports/engine/JRSection;L formatFactoryClassq ~ L 
importsSett Ljava/util/Set;L languageq ~ L lastPageFooterq ~ L mainDatasett 'Lnet/sf/jasperreports/engine/JRDataset;L nameq ~ L noDataq ~ L orientationValuet 2Lnet/sf/jasperreports/engine/type/OrientationEnum;L 
pageFooterq ~ L 
pageHeaderq ~ L printOrderValuet 1Lnet/sf/jasperreports/engine/type/PrintOrderEnum;L sectionTypet 2Lnet/sf/jasperreports/engine/type/SectionTypeEnum;[ stylest &[Lnet/sf/jasperreports/engine/JRStyle;L summaryq ~ [ 	templatest /[Lnet/sf/jasperreports/engine/JRReportTemplate;L titleq ~ L whenNoDataTypeValuet 5Lnet/sf/jasperreports/engine/type/WhenNoDataTypeEnum;xp  �             &           J  &          sr +net.sf.jasperreports.engine.base.JRBaseBand      '� I PSEUDO_SERIAL_VERSION_UIDI heightZ isSplitAllowedL printWhenExpressiont *Lnet/sf/jasperreports/engine/JRExpression;L 
propertiesMapt -Lnet/sf/jasperreports/engine/JRPropertiesMap;L returnValuest Ljava/util/List;L 	splitTypet Ljava/lang/Byte;L splitTypeValuet 0Lnet/sf/jasperreports/engine/type/SplitTypeEnum;xr 3net.sf.jasperreports.engine.base.JRBaseElementGroup      '� L childrenq ~ L elementGroupt ,Lnet/sf/jasperreports/engine/JRElementGroup;xpsr java.util.ArrayListx����a� I sizexp    w    xp  �    pppp~r .net.sf.jasperreports.engine.type.SplitTypeEnum          xr java.lang.Enum          xpt STRETCH~r 1net.sf.jasperreports.engine.type.RunDirectionEnum          xq ~ t LTRpppppsr .net.sf.jasperreports.engine.base.JRBaseSection      '� [ bandst %[Lnet/sf/jasperreports/engine/JRBand;[ partst %[Lnet/sf/jasperreports/engine/JRPart;xpur %[Lnet.sf.jasperreports.engine.JRBand;��~�ʅ5  xp   sq ~ sq ~    w   sr 0net.sf.jasperreports.engine.base.JRBaseTextField      '� I PSEUDO_SERIAL_VERSION_UIDI 
bookmarkLevelB evaluationTimeB hyperlinkTargetB 
hyperlinkTypeZ isStretchWithOverflowL anchorNameExpressionq ~ L bookmarkLevelExpressionq ~ L evaluationGroupt %Lnet/sf/jasperreports/engine/JRGroup;L evaluationTimeValuet 5Lnet/sf/jasperreports/engine/type/EvaluationTimeEnum;L 
expressionq ~ L hyperlinkAnchorExpressionq ~ L hyperlinkPageExpressionq ~ [ hyperlinkParameterst 3[Lnet/sf/jasperreports/engine/JRHyperlinkParameter;L hyperlinkReferenceExpressionq ~ L hyperlinkTooltipExpressionq ~ L hyperlinkWhenExpressionq ~ L isBlankWhenNullt Ljava/lang/Boolean;L 
linkTargetq ~ L linkTypeq ~ L patternq ~ L patternExpressionq ~ L 
textAdjustt 1Lnet/sf/jasperreports/engine/type/TextAdjustEnum;xr 2net.sf.jasperreports.engine.base.JRBaseTextElement      '� I PSEUDO_SERIAL_VERSION_UIDL fontNameq ~ L fontSizet Ljava/lang/Integer;L fontsizet Ljava/lang/Float;L horizontalAlignmentq ~ L horizontalAlignmentValuet 6Lnet/sf/jasperreports/engine/type/HorizontalAlignEnum;L horizontalTextAlignt :Lnet/sf/jasperreports/engine/type/HorizontalTextAlignEnum;L isBoldq ~ 0L isItalicq ~ 0L 
isPdfEmbeddedq ~ 0L isStrikeThroughq ~ 0L isStyledTextq ~ 0L isUnderlineq ~ 0L lineBoxt 'Lnet/sf/jasperreports/engine/JRLineBox;L lineSpacingq ~ L lineSpacingValuet 2Lnet/sf/jasperreports/engine/type/LineSpacingEnum;L markupq ~ L 	paragrapht )Lnet/sf/jasperreports/engine/JRParagraph;L pdfEncodingq ~ L pdfFontNameq ~ L rotationq ~ L 
rotationValuet /Lnet/sf/jasperreports/engine/type/RotationEnum;L verticalAlignmentq ~ L verticalAlignmentValuet 4Lnet/sf/jasperreports/engine/type/VerticalAlignEnum;L verticalTextAlignt 8Lnet/sf/jasperreports/engine/type/VerticalTextAlignEnum;xr .net.sf.jasperreports.engine.base.JRBaseElement      '� I PSEUDO_SERIAL_VERSION_UIDI heightZ isPrintInFirstWholeBandZ isPrintRepeatedValuesZ isPrintWhenDetailOverflowsZ isRemoveLineWhenBlankB positionTypeB stretchTypeI widthI xI yL 	backcolort Ljava/awt/Color;L defaultStyleProvidert 4Lnet/sf/jasperreports/engine/JRDefaultStyleProvider;L elementGroupq ~ L 	forecolorq ~ >L keyq ~ L modeq ~ L 	modeValuet +Lnet/sf/jasperreports/engine/type/ModeEnum;L parentStyleq ~ L parentStyleNameReferenceq ~ L positionTypeValuet 3Lnet/sf/jasperreports/engine/type/PositionTypeEnum;L printWhenExpressionq ~ L printWhenGroupChangesq ~ -L 
propertiesMapq ~ [ propertyExpressionst 3[Lnet/sf/jasperreports/engine/JRPropertyExpression;L stretchTypeValuet 2Lnet/sf/jasperreports/engine/type/StretchTypeEnum;L uuidt Ljava/util/UUID;xp  �          &        pq ~ q ~ *pppppp~r 1net.sf.jasperreports.engine.type.PositionTypeEnum          xq ~ t FLOATpppp~r 0net.sf.jasperreports.engine.type.StretchTypeEnum          xq ~ t RELATIVE_TO_TALLEST_OBJECTsr java.util.UUID����m�/ J leastSigBitsJ mostSigBitsxp�]��rdש�=9eA�  �ppppppppppppsr .net.sf.jasperreports.engine.base.JRBaseLineBox      '� L 
bottomPaddingq ~ 3L 	bottomPent +Lnet/sf/jasperreports/engine/base/JRBoxPen;L boxContainert ,Lnet/sf/jasperreports/engine/JRBoxContainer;L leftPaddingq ~ 3L leftPenq ~ OL paddingq ~ 3L penq ~ OL rightPaddingq ~ 3L rightPenq ~ OL 
topPaddingq ~ 3L topPenq ~ Oxppsr 3net.sf.jasperreports.engine.base.JRBaseBoxBottomPen      '�  xr -net.sf.jasperreports.engine.base.JRBaseBoxPen      '� L lineBoxq ~ 7xr *net.sf.jasperreports.engine.base.JRBasePen      '� I PSEUDO_SERIAL_VERSION_UIDL 	lineColorq ~ >L 	lineStyleq ~ L lineStyleValuet 0Lnet/sf/jasperreports/engine/type/LineStyleEnum;L 	lineWidthq ~ 4L penContainert ,Lnet/sf/jasperreports/engine/JRPenContainer;xp  �ppppq ~ Qq ~ Qq ~ Epsr 1net.sf.jasperreports.engine.base.JRBaseBoxLeftPen      '�  xq ~ S  �ppppq ~ Qq ~ Qpsq ~ S  �ppppq ~ Qq ~ Qpsr 2net.sf.jasperreports.engine.base.JRBaseBoxRightPen      '�  xq ~ S  �ppppq ~ Qq ~ Qpsr 0net.sf.jasperreports.engine.base.JRBaseBoxTopPen      '�  xq ~ S  �ppppq ~ Qq ~ Qpppsr 0net.sf.jasperreports.engine.base.JRBaseParagraph      '� 
L firstLineIndentq ~ 3L 
leftIndentq ~ 3L lineSpacingq ~ 8L lineSpacingSizeq ~ 4L paragraphContainert 2Lnet/sf/jasperreports/engine/JRParagraphContainer;L rightIndentq ~ 3L spacingAfterq ~ 3L 
spacingBeforeq ~ 3L tabStopWidthq ~ 3L tabStopsq ~ xpppppq ~ Epppppppppppp  �        ppp~r 3net.sf.jasperreports.engine.type.EvaluationTimeEnum          xq ~ t NOWsr 1net.sf.jasperreports.engine.base.JRBaseExpression      '� I id[ chunkst 0[Lnet/sf/jasperreports/engine/JRExpressionChunk;L typet 5Lnet/sf/jasperreports/engine/type/ExpressionTypeEnum;L valueClassNameq ~ L valueClassRealNameq ~ xp   ur 0[Lnet.sf.jasperreports.engine.JRExpressionChunk;mY��iK�U  xp   sr 6net.sf.jasperreports.engine.base.JRBaseExpressionChunk      '� B typeL textq ~ xpt valor1pppppppppppppp~r /net.sf.jasperreports.engine.type.TextAdjustEnum          xq ~ t STRETCH_HEIGHTxp  �   ppppq ~ sq ~ sq ~    w   sq ~ ,  �        &        pq ~ q ~ qppppppq ~ Gpppp~q ~ It CONTAINER_HEIGHTsq ~ L�H(���,g%��P��@J  �ppppppsr java.lang.Boolean� r�՜�� Z valuexppppppsq ~ Npsq ~ R  �sr java.awt.Color���3u F falphaI valueL cst Ljava/awt/color/ColorSpace;[ 	frgbvaluet [F[ fvalueq ~ }xp    ����pppppsr java.lang.Float��ɢ�<�� F valuexr java.lang.Number������  xp?�  q ~ yq ~ yq ~ spsq ~ X  �ppppq ~ yq ~ ypsq ~ S  �pppsq ~     q ~ yq ~ ypsq ~ [  �ppppq ~ yq ~ ypsq ~ ]  �ppppq ~ yq ~ ypppsq ~ _ppppq ~ spppppppppppp  �        pppq ~ csq ~ e   	uq ~ i   sq ~ kt valor2ppppppppppppppq ~ oxp  �   psr +net.sf.jasperreports.engine.JRPropertiesMap      '� L baseq ~ L propertiesListq ~ L 
propertiesMapt Ljava/util/Map;xppsq ~    w   t com.jaspersoft.studio.layoutxsr java.util.HashMap���`� F 
loadFactorI 	thresholdxp?@     w      q ~ �t 5com.jaspersoft.studio.editor.layout.VerticalRowLayoutxppq ~ pppt javapsr .net.sf.jasperreports.engine.base.JRBaseDataset      '� I PSEUDO_SERIAL_VERSION_UIDZ isMainB whenResourceMissingType[ fieldst &[Lnet/sf/jasperreports/engine/JRField;L filterExpressionq ~ [ groupst &[Lnet/sf/jasperreports/engine/JRGroup;L nameq ~ [ 
parameterst *[Lnet/sf/jasperreports/engine/JRParameter;L 
propertiesMapq ~ [ propertyExpressionst 8[Lnet/sf/jasperreports/engine/DatasetPropertyExpression;L queryt %Lnet/sf/jasperreports/engine/JRQuery;L resourceBundleq ~ L scriptletClassq ~ [ 
scriptletst *[Lnet/sf/jasperreports/engine/JRScriptlet;[ 
sortFieldst *[Lnet/sf/jasperreports/engine/JRSortField;L uuidq ~ D[ 	variablest )[Lnet/sf/jasperreports/engine/JRVariable;L whenResourceMissingTypeValuet >Lnet/sf/jasperreports/engine/type/WhenResourceMissingTypeEnum;xp  � ur &[Lnet.sf.jasperreports.engine.JRField;<��N*�p  xp   sr ,net.sf.jasperreports.engine.base.JRBaseField      '� L descriptionq ~ L nameq ~ L 
propertiesMapq ~ [ propertyExpressionsq ~ BL valueClassNameq ~ L valueClassRealNameq ~ xppt valor1sq ~ �ppppt java.lang.Stringpsq ~ �pt valor2sq ~ �ppppt java.lang.Stringpppt Anamneseur *[Lnet.sf.jasperreports.engine.JRParameter;" �*�`!  xp   sr 0net.sf.jasperreports.engine.base.JRBaseParameter      '� 
Z isForPromptingZ isSystemDefinedL defaultValueExpressionq ~ L descriptionq ~ L evaluationTimet >Lnet/sf/jasperreports/engine/type/ParameterEvaluationTimeEnum;L nameq ~ L nestedTypeNameq ~ L 
propertiesMapq ~ L valueClassNameq ~ L valueClassRealNameq ~ xppppt REPORT_CONTEXTpsq ~ �pppt )net.sf.jasperreports.engine.ReportContextpsq ~ �pppt REPORT_PARAMETERS_MAPpsq ~ �pppt 
java.util.Mappsq ~ �pppt JASPER_REPORTS_CONTEXTpsq ~ �pppt 0net.sf.jasperreports.engine.JasperReportsContextpsq ~ �pppt 
JASPER_REPORTpsq ~ �pppt (net.sf.jasperreports.engine.JasperReportpsq ~ �pppt REPORT_CONNECTIONpsq ~ �pppt java.sql.Connectionpsq ~ �pppt REPORT_MAX_COUNTpsq ~ �pppt java.lang.Integerpsq ~ �pppt REPORT_DATA_SOURCEpsq ~ �pppt (net.sf.jasperreports.engine.JRDataSourcepsq ~ �pppt REPORT_SCRIPTLETpsq ~ �pppt /net.sf.jasperreports.engine.JRAbstractScriptletpsq ~ �pppt 
REPORT_LOCALEpsq ~ �pppt java.util.Localepsq ~ �pppt REPORT_RESOURCE_BUNDLEpsq ~ �pppt java.util.ResourceBundlepsq ~ �pppt REPORT_TIME_ZONEpsq ~ �pppt java.util.TimeZonepsq ~ �pppt REPORT_FORMAT_FACTORYpsq ~ �pppt .net.sf.jasperreports.engine.util.FormatFactorypsq ~ �pppt REPORT_CLASS_LOADERpsq ~ �pppt java.lang.ClassLoaderpsq ~ �pppt REPORT_TEMPLATESpsq ~ �pppt java.util.Collectionpsq ~ �pppt SORT_FIELDSpsq ~ �pppt java.util.Listpsq ~ �pppt FILTERpsq ~ �pppt )net.sf.jasperreports.engine.DatasetFilterpsq ~ �pppt REPORT_VIRTUALIZERpsq ~ �pppt )net.sf.jasperreports.engine.JRVirtualizerpsq ~ �pppt IS_IGNORE_PAGINATIONpsq ~ �pppt java.lang.Booleanpsq ~ �psq ~    w   t -com.jaspersoft.studio.data.defaultdataadapterxsq ~ �?@     w      q ~ �t One Empty Recordxpsr ,net.sf.jasperreports.engine.base.JRBaseQuery      '� [ chunkst +[Lnet/sf/jasperreports/engine/JRQueryChunk;L languageq ~ xppt sqlppppsq ~ L��j��t�5N�͠~JINur )[Lnet.sf.jasperreports.engine.JRVariable;b�|�,�D  xp   sr /net.sf.jasperreports.engine.base.JRBaseVariable      '� I PSEUDO_SERIAL_VERSION_UIDB calculationB 
incrementTypeZ isSystemDefinedB 	resetTypeL calculationValuet 2Lnet/sf/jasperreports/engine/type/CalculationEnum;L descriptionq ~ L 
expressionq ~ L incrementGroupq ~ -L incrementTypeValuet 4Lnet/sf/jasperreports/engine/type/IncrementTypeEnum;L incrementerFactoryClassNameq ~ L incrementerFactoryClassRealNameq ~ L initialValueExpressionq ~ L nameq ~ L 
resetGroupq ~ -L resetTypeValuet 0Lnet/sf/jasperreports/engine/type/ResetTypeEnum;L valueClassNameq ~ L valueClassRealNameq ~ xp  w�   ~r 0net.sf.jasperreports.engine.type.CalculationEnum          xq ~ t SYSTEMppp~r 2net.sf.jasperreports.engine.type.IncrementTypeEnum          xq ~ t NONEppsq ~ e    uq ~ i   sq ~ kt new java.lang.Integer(1)pppt PAGE_NUMBERp~r .net.sf.jasperreports.engine.type.ResetTypeEnum          xq ~ t REPORTq ~ �psq ~  w�   q ~
pppq ~
pppt MASTER_CURRENT_PAGEpq ~q ~ �psq ~  w�   q ~
pppq ~
pppt MASTER_TOTAL_PAGESpq ~q ~ �psq ~  w�   q ~
pppq ~
ppsq ~ e   uq ~ i   sq ~ kt new java.lang.Integer(1)pppt 
COLUMN_NUMBERp~q ~t PAGEq ~ �psq ~  w�   ~q ~	t COUNTpsq ~ e   uq ~ i   sq ~ kt new java.lang.Integer(1)ppppq ~
ppsq ~ e   uq ~ i   sq ~ kt new java.lang.Integer(0)pppt REPORT_COUNTpq ~q ~ �psq ~  w�   q ~$psq ~ e   uq ~ i   sq ~ kt new java.lang.Integer(1)ppppq ~
ppsq ~ e   uq ~ i   sq ~ kt new java.lang.Integer(0)pppt 
PAGE_COUNTpq ~!q ~ �psq ~  w�   q ~$psq ~ e   uq ~ i   sq ~ kt new java.lang.Integer(1)ppppq ~
ppsq ~ e   uq ~ i   sq ~ kt new java.lang.Integer(0)pppt COLUMN_COUNTp~q ~t COLUMNq ~ �p~r <net.sf.jasperreports.engine.type.WhenResourceMissingTypeEnum          xq ~ t NULLq ~ �p~r 0net.sf.jasperreports.engine.type.OrientationEnum          xq ~ t PORTRAITpp~r /net.sf.jasperreports.engine.type.PrintOrderEnum          xq ~ t VERTICAL~r 0net.sf.jasperreports.engine.type.SectionTypeEnum          xq ~ t BANDpppppsr 6net.sf.jasperreports.engine.design.JRReportCompileData      '� L crosstabCompileDataq ~ �L datasetCompileDataq ~ �L mainDatasetCompileDataq ~ xpsq ~ �?@      w       xsq ~ �?@      w       xsr =net.sf.jasperreports.compilers.ReportExpressionEvaluationData      '� L compileDataq ~ L directEvaluationsq ~ �xppsq ~ �?@     w      
sr java.lang.Integer⠤���8 I valuexq ~ �    sr ;net.sf.jasperreports.compilers.ConstantExpressionEvaluation      '� L valuet Ljava/lang/Object;xpsq ~X   q ~]q ~\sq ~X   q ~\sq ~X   sq ~Zq ~Ysq ~X   q ~\sq ~X   q ~`sq ~X   q ~\sq ~X   q ~`sq ~X   sr .net.sf.jasperreports.compilers.FieldEvaluation      '� L nameq ~ xpq ~ msq ~X   	sq ~fq ~ �xt _1698774316472_819097t 2net.sf.jasperreports.engine.design.JRJavacCompiler