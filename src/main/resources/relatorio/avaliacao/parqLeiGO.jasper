�� sr (net.sf.jasperreports.engine.JasperReport      '� L compileDatat Ljava/io/Serializable;L compileNameSuffixt Ljava/lang/String;L 
compilerClassq ~ xr -net.sf.jasperreports.engine.base.JRBaseReport      '� +I PSEUDO_SERIAL_VERSION_UIDI bottomMarginI columnCountI 
columnSpacingI columnWidthZ ignorePaginationZ isFloatColumnFooterZ isSummaryNewPageZ  isSummaryWithPageHeaderAndFooterZ isTitleNewPageI 
leftMarginB orientationI 
pageHeightI 	pageWidthB 
printOrderI rightMarginI 	topMarginB whenNoDataTypeL 
backgroundt $Lnet/sf/jasperreports/engine/JRBand;L columnDirectiont 3Lnet/sf/jasperreports/engine/type/RunDirectionEnum;L columnFooterq ~ L columnHeaderq ~ [ datasetst ([Lnet/sf/jasperreports/engine/JRDataset;L defaultStylet %Lnet/sf/jasperreports/engine/JRStyle;L detailq ~ L 
detailSectiont 'Lnet/sf/jasperreports/engine/JRSection;L formatFactoryClassq ~ L 
importsSett Ljava/util/Set;L languageq ~ L lastPageFooterq ~ L mainDatasett 'Lnet/sf/jasperreports/engine/JRDataset;L nameq ~ L noDataq ~ L orientationValuet 2Lnet/sf/jasperreports/engine/type/OrientationEnum;L 
pageFooterq ~ L 
pageHeaderq ~ L printOrderValuet 1Lnet/sf/jasperreports/engine/type/PrintOrderEnum;L sectionTypet 2Lnet/sf/jasperreports/engine/type/SectionTypeEnum;[ stylest &[Lnet/sf/jasperreports/engine/JRStyle;L summaryq ~ [ 	templatest /[Lnet/sf/jasperreports/engine/JRReportTemplate;L titleq ~ L whenNoDataTypeValuet 5Lnet/sf/jasperreports/engine/type/WhenNoDataTypeEnum;xp  �b            +           J  S        sr +net.sf.jasperreports.engine.base.JRBaseBand      '� I PSEUDO_SERIAL_VERSION_UIDI heightZ isSplitAllowedL printWhenExpressiont *Lnet/sf/jasperreports/engine/JRExpression;L 
propertiesMapt -Lnet/sf/jasperreports/engine/JRPropertiesMap;L returnValuest Ljava/util/List;L 	splitTypet Ljava/lang/Byte;L splitTypeValuet 0Lnet/sf/jasperreports/engine/type/SplitTypeEnum;xr 3net.sf.jasperreports.engine.base.JRBaseElementGroup      '� L childrenq ~ L elementGroupt ,Lnet/sf/jasperreports/engine/JRElementGroup;xpsr java.util.ArrayListx����a� I sizexp    w    xp  �b    pppp~r .net.sf.jasperreports.engine.type.SplitTypeEnum          xr java.lang.Enum          xpt STRETCH~r 1net.sf.jasperreports.engine.type.RunDirectionEnum          xq ~ t LTRpppppsr .net.sf.jasperreports.engine.base.JRBaseSection      '� [ bandst %[Lnet/sf/jasperreports/engine/JRBand;[ partst %[Lnet/sf/jasperreports/engine/JRPart;xpur %[Lnet.sf.jasperreports.engine.JRBand;��~�ʅ5  xp   sq ~ sq ~    w   sr ,net.sf.jasperreports.engine.base.JRBaseImage      '� I PSEUDO_SERIAL_VERSION_UIDI 
bookmarkLevelB evaluationTimeB hyperlinkTargetB 
hyperlinkTypeZ isLazyB onErrorTypeL anchorNameExpressionq ~ L evaluationGroupt %Lnet/sf/jasperreports/engine/JRGroup;L evaluationTimeValuet 5Lnet/sf/jasperreports/engine/type/EvaluationTimeEnum;L 
expressionq ~ L horizontalAlignmentq ~ L horizontalAlignmentValuet 6Lnet/sf/jasperreports/engine/type/HorizontalAlignEnum;L horizontalImageAlignt ;Lnet/sf/jasperreports/engine/type/HorizontalImageAlignEnum;L hyperlinkAnchorExpressionq ~ L hyperlinkPageExpressionq ~ [ hyperlinkParameterst 3[Lnet/sf/jasperreports/engine/JRHyperlinkParameter;L hyperlinkReferenceExpressionq ~ L hyperlinkTooltipExpressionq ~ L hyperlinkWhenExpressionq ~ L isUsingCachet Ljava/lang/Boolean;L lineBoxt 'Lnet/sf/jasperreports/engine/JRLineBox;L 
linkTargetq ~ L linkTypeq ~ L onErrorTypeValuet 2Lnet/sf/jasperreports/engine/type/OnErrorTypeEnum;L 
scaleImageq ~ L scaleImageValuet 1Lnet/sf/jasperreports/engine/type/ScaleImageEnum;L verticalAlignmentq ~ L verticalAlignmentValuet 4Lnet/sf/jasperreports/engine/type/VerticalAlignEnum;L verticalImageAlignt 9Lnet/sf/jasperreports/engine/type/VerticalImageAlignEnum;xr 5net.sf.jasperreports.engine.base.JRBaseGraphicElement      '� I PSEUDO_SERIAL_VERSION_UIDL fillq ~ L 	fillValuet +Lnet/sf/jasperreports/engine/type/FillEnum;L linePent #Lnet/sf/jasperreports/engine/JRPen;xr .net.sf.jasperreports.engine.base.JRBaseElement      '� I PSEUDO_SERIAL_VERSION_UIDI heightZ isPrintInFirstWholeBandZ isPrintRepeatedValuesZ isPrintWhenDetailOverflowsZ isRemoveLineWhenBlankB positionTypeB stretchTypeI widthI xI yL 	backcolort Ljava/awt/Color;L defaultStyleProvidert 4Lnet/sf/jasperreports/engine/JRDefaultStyleProvider;L elementGroupq ~ L 	forecolorq ~ <L keyq ~ L modeq ~ L 	modeValuet +Lnet/sf/jasperreports/engine/type/ModeEnum;L parentStyleq ~ L parentStyleNameReferenceq ~ L positionTypeValuet 3Lnet/sf/jasperreports/engine/type/PositionTypeEnum;L printWhenExpressionq ~ L printWhenGroupChangesq ~ -L 
propertiesMapq ~ [ propertyExpressionst 3[Lnet/sf/jasperreports/engine/JRPropertyExpression;L stretchTypeValuet 2Lnet/sf/jasperreports/engine/type/StretchTypeEnum;L uuidt Ljava/util/UUID;xp  �b   K        Z   	����pq ~ q ~ *pppppp~r 1net.sf.jasperreports.engine.type.PositionTypeEnum          xq ~ t FIX_RELATIVE_TO_BOTTOMpppp~r 0net.sf.jasperreports.engine.type.StretchTypeEnum          xq ~ t RELATIVE_TO_TALLEST_OBJECTsr java.util.UUID����m�/ J leastSigBitsJ mostSigBitsxp���4Dߨ���QH�  w�ppsr *net.sf.jasperreports.engine.base.JRBasePen      '� I PSEUDO_SERIAL_VERSION_UIDL 	lineColorq ~ <L 	lineStyleq ~ L lineStyleValuet 0Lnet/sf/jasperreports/engine/type/LineStyleEnum;L 	lineWidtht Ljava/lang/Float;L penContainert ,Lnet/sf/jasperreports/engine/JRPenContainer;xp  �bppppq ~ C  �b         pp~r 3net.sf.jasperreports.engine.type.EvaluationTimeEnum          xq ~ t REPORTsr 1net.sf.jasperreports.engine.base.JRBaseExpression      '� I id[ chunkst 0[Lnet/sf/jasperreports/engine/JRExpressionChunk;L typet 5Lnet/sf/jasperreports/engine/type/ExpressionTypeEnum;L valueClassNameq ~ L valueClassRealNameq ~ xp   ur 0[Lnet.sf.jasperreports.engine.JRExpressionChunk;mY��iK�U  xp   sr 6net.sf.jasperreports.engine.base.JRBaseExpressionChunk      '� B typeL textq ~ xpt 	fotoAlunoppppp~r 9net.sf.jasperreports.engine.type.HorizontalImageAlignEnum          xq ~ t CENTERppppppsr java.lang.Boolean� r�՜�� Z valuexp sr .net.sf.jasperreports.engine.base.JRBaseLineBox      '� L 
bottomPaddingt Ljava/lang/Integer;L 	bottomPent +Lnet/sf/jasperreports/engine/base/JRBoxPen;L boxContainert ,Lnet/sf/jasperreports/engine/JRBoxContainer;L leftPaddingq ~ cL leftPenq ~ dL paddingq ~ cL penq ~ dL rightPaddingq ~ cL rightPenq ~ dL 
topPaddingq ~ cL topPenq ~ dxppsr 3net.sf.jasperreports.engine.base.JRBaseBoxBottomPen      '�  xr -net.sf.jasperreports.engine.base.JRBaseBoxPen      '� L lineBoxq ~ 3xq ~ L  �bppppq ~ fq ~ fq ~ Cpsr 1net.sf.jasperreports.engine.base.JRBaseBoxLeftPen      '�  xq ~ h  �bppppq ~ fq ~ fpsq ~ h  �bppppq ~ fq ~ fpsr 2net.sf.jasperreports.engine.base.JRBaseBoxRightPen      '�  xq ~ h  �bppppq ~ fq ~ fpsr 0net.sf.jasperreports.engine.base.JRBaseBoxTopPen      '�  xq ~ h  �bppppq ~ fq ~ fpp~r 0net.sf.jasperreports.engine.type.OnErrorTypeEnum          xq ~ t ICONp~r /net.sf.jasperreports.engine.type.ScaleImageEnum          xq ~ t RETAIN_SHAPEpppsr 0net.sf.jasperreports.engine.base.JRBaseTextField      '� I PSEUDO_SERIAL_VERSION_UIDI 
bookmarkLevelB evaluationTimeB hyperlinkTargetB 
hyperlinkTypeZ isStretchWithOverflowL anchorNameExpressionq ~ L evaluationGroupq ~ -L evaluationTimeValueq ~ .L 
expressionq ~ L hyperlinkAnchorExpressionq ~ L hyperlinkPageExpressionq ~ [ hyperlinkParametersq ~ 1L hyperlinkReferenceExpressionq ~ L hyperlinkTooltipExpressionq ~ L hyperlinkWhenExpressionq ~ L isBlankWhenNullq ~ 2L 
linkTargetq ~ L linkTypeq ~ L patternq ~ L patternExpressionq ~ xr 2net.sf.jasperreports.engine.base.JRBaseTextElement      '� I PSEUDO_SERIAL_VERSION_UIDL fontNameq ~ L fontSizeq ~ cL fontsizeq ~ NL horizontalAlignmentq ~ L horizontalAlignmentValueq ~ /L horizontalTextAlignt :Lnet/sf/jasperreports/engine/type/HorizontalTextAlignEnum;L isBoldq ~ 2L isItalicq ~ 2L 
isPdfEmbeddedq ~ 2L isStrikeThroughq ~ 2L isStyledTextq ~ 2L isUnderlineq ~ 2L lineBoxq ~ 3L lineSpacingq ~ L lineSpacingValuet 2Lnet/sf/jasperreports/engine/type/LineSpacingEnum;L markupq ~ L 	paragrapht )Lnet/sf/jasperreports/engine/JRParagraph;L pdfEncodingq ~ L pdfFontNameq ~ L rotationq ~ L 
rotationValuet /Lnet/sf/jasperreports/engine/type/RotationEnum;L verticalAlignmentq ~ L verticalAlignmentValueq ~ 6L verticalTextAlignt 8Lnet/sf/jasperreports/engine/type/VerticalTextAlignEnum;xq ~ ;  �b          �   p����pq ~ q ~ *pppppp~q ~ Dt FIX_RELATIVE_TO_TOPpppp~q ~ Gt 
NO_STRETCHsq ~ J�����T�>�\��jD�  �bppsr java.lang.Float��ɢ�<�� F valuexr java.lang.Number������  xpA�  pppsq ~ `pppppsq ~ bpsq ~ g  �bppppq ~ �q ~ �q ~ ~psq ~ j  �bppppq ~ �q ~ �psq ~ h  �bppppq ~ �q ~ �psq ~ m  �bppppq ~ �q ~ �psq ~ o  �bppppq ~ �q ~ �pppsr 0net.sf.jasperreports.engine.base.JRBaseParagraph      '� 
L firstLineIndentq ~ cL 
leftIndentq ~ cL lineSpacingq ~ zL lineSpacingSizeq ~ NL paragraphContainert 2Lnet/sf/jasperreports/engine/JRParagraphContainer;L rightIndentq ~ cL spacingAfterq ~ cL 
spacingBeforeq ~ cL tabStopWidthq ~ cL tabStopsq ~ xpppppq ~ ~ppppppppppp~r 6net.sf.jasperreports.engine.type.VerticalTextAlignEnum          xq ~ t MIDDLE  �b        pp~q ~ Qt NOWsq ~ T   uq ~ X   sq ~ Zt 	nomeAlunoppppppppppppppsq ~ w  �b           �   �   pq ~ q ~ *ppppppq ~ ppppq ~ �sq ~ J��&p�>�T��r:��J�  �bt Arialpsq ~ �A@  pppppppppsq ~ bpsq ~ g  �bppppq ~ �q ~ �q ~ �psq ~ j  �bppppq ~ �q ~ �psq ~ h  �bppppq ~ �q ~ �psq ~ m  �bppppq ~ �q ~ �psq ~ o  �bppppq ~ �q ~ �pppsq ~ �ppppq ~ �pppppppppppp  �b        pp~q ~ Qt PAGEsq ~ T   uq ~ X   sq ~ Zt idadeppppppppppppppsr 1net.sf.jasperreports.engine.base.JRBaseStaticText      '� L textq ~ xq ~ x  �b           D   p   pq ~ q ~ *ppppppq ~ ppppq ~ �sq ~ J�
�Q�El�p�m�I�  �bppsq ~ �A@  pppq ~ �pppppsq ~ bpsq ~ g  �bppppq ~ �q ~ �q ~ �psq ~ j  �bppppq ~ �q ~ �psq ~ h  �bppppq ~ �q ~ �psq ~ m  �bppppq ~ �q ~ �psq ~ o  �bppppq ~ �q ~ �pppsq ~ �ppppq ~ �ppppppppppppt Idade:sq ~ �  �b           D  T   pq ~ q ~ *ppppppq ~ ppppq ~ �sq ~ J��
�E���E��B�  �bppsq ~ �A@  pppq ~ �pppppsq ~ bpsq ~ g  �bppppq ~ �q ~ �q ~ �psq ~ j  �bppppq ~ �q ~ �psq ~ h  �bppppq ~ �q ~ �psq ~ m  �bppppq ~ �q ~ �psq ~ o  �bppppq ~ �q ~ �pppsq ~ �ppppq ~ �ppppppppppppt Sexo:sq ~ w  �b           �  �   pq ~ q ~ *ppppppq ~ ppppq ~ �sq ~ J�=��O���<��N�  �bt Arialpsq ~ �A@  pppppppppsq ~ bpsq ~ g  �bppppq ~ �q ~ �q ~ �psq ~ j  �bppppq ~ �q ~ �psq ~ h  �bppppq ~ �q ~ �psq ~ m  �bppppq ~ �q ~ �psq ~ o  �bppppq ~ �q ~ �pppsq ~ �ppppq ~ �pppppppppppp  �b        ppq ~ �sq ~ T   uq ~ X   sq ~ Zt sexoppppppppppppppsq ~ �  �b           D   p   5pq ~ q ~ *ppppppq ~ ppppq ~ �sq ~ J�[���s���A3BF
  �bppsq ~ �A@  pppq ~ �pppppsq ~ bpsq ~ g  �bppppq ~ �q ~ �q ~ �psq ~ j  �bppppq ~ �q ~ �psq ~ h  �bppppq ~ �q ~ �psq ~ m  �bppppq ~ �q ~ �psq ~ o  �bppppq ~ �q ~ �pppsq ~ �ppppq ~ �ppppppppppppt Data:sq ~ w  �b           �   �   5pq ~ q ~ *ppppppq ~ ppppq ~ �sq ~ J��>\��K�7(R�[�M�  �bt Arialpsq ~ �A@  pppppppppsq ~ bpsq ~ g  �bppppq ~ �q ~ �q ~ �psq ~ j  �bppppq ~ �q ~ �psq ~ h  �bppppq ~ �q ~ �psq ~ m  �bppppq ~ �q ~ �psq ~ o  �bppppq ~ �q ~ �pppsq ~ �ppppq ~ �pppppppppppp  �b        ppq ~ �sq ~ T   uq ~ X   sq ~ Zt 
dataAvaliacaoppppppppppppppxp  �b   [pppppsq ~ sq ~    	w   	sr 0net.sf.jasperreports.engine.base.JRBaseRectangle      '� L radiusq ~ cxq ~ 8  �b   )       &    ����sr java.awt.Color���3u F falphaI valueL cst Ljava/awt/color/ColorSpace;[ 	frgbvaluet [F[ fvalueq ~ �xp    ����pppq ~ q ~ �ppppppq ~ ppppq ~ �sq ~ J��>�E�4��e6A�  w�ppsq ~ L  �bsq ~ �    ����pppppsq ~ �    q ~ �psq ~ �  �b   )       &    ����pq ~ q ~ �ppppppq ~ ppppq ~ �sq ~ J����J;�4�O�oM�O�  �bppsq ~ �A`  pp~r 8net.sf.jasperreports.engine.type.HorizontalTextAlignEnum          xq ~ t CENTERq ~ �pppppsq ~ bpsq ~ g  �bsq ~ �    �   pppp~r .net.sf.jasperreports.engine.type.LineStyleEnum          xq ~ t SOLIDsq ~ �?�  q ~ �q ~ �q ~ �sr java.lang.Integer⠤���8 I valuexq ~ �    sq ~ j  �bsq ~ �    �   ppppq ~sq ~ �    q ~ �q ~ �psq ~ h  �bppppq ~ �q ~ �psq ~ m  �bsq ~ �    �   ppppq ~sq ~ �    q ~ �q ~ �psq ~ o  �bsq ~ �    �   ppppq ~sq ~ �    q ~ �q ~ �pppsq ~ �ppppq ~ �pppppppppppq ~ �t Par-Qsr 0net.sf.jasperreports.engine.base.JRBaseSubreport      '� 	L connectionExpressionq ~ L dataSourceExpressionq ~ L 
expressionq ~ L isUsingCacheq ~ 2L overflowTypet /Lnet/sf/jasperreports/engine/type/OverflowType;[ 
parameterst 3[Lnet/sf/jasperreports/engine/JRSubreportParameter;L parametersMapExpressionq ~ [ returnValuest 5[Lnet/sf/jasperreports/engine/JRSubreportReturnValue;L runToBottomq ~ 2xq ~ ;  �b          &      0pq ~ q ~ �ppppppq ~ ppppq ~ �sq ~ J�@�6�{����vH�M�psq ~ T   uq ~ X   sq ~ Zt parqJRpppsq ~ T   uq ~ X   sq ~ Zt 
SUBREPORT_DIRsq ~ Zt  + "anamnese.jasper"pppppppppsq ~ w  �b   %       4   ~  ~pq ~ q ~ �pppppp~q ~ Dt FLOATppppq ~ �sq ~ J��t��y���am�K�  �bppsq ~ �A�  ppq ~ �q ~ �pppppsq ~ bpsq ~ g  �bsq ~ �    �   ppppq ~sq ~ �    q ~'q ~'q ~"psq ~ j  �bsq ~ �    �   ppppq ~sq ~ �    q ~'q ~'psq ~ h  �bppppq ~'q ~'psq ~ m  �bsq ~ �    �   ppppq ~sq ~ �    q ~'q ~'psq ~ o  �bsq ~ �    �   ppppq ~sq ~ �    q ~'q ~'pppsq ~ �ppppq ~"pppppppppppq ~ �  �b        ppq ~ �sq ~ T   uq ~ X   sq ~ Zt parqppppppppppppppsq ~ �  �b          4   ~  fpq ~ q ~ �ppppppq ~#ppppq ~ �sq ~ J�K��H�0M�F�g��M9  �bppsq ~ �A`  ppq ~ �q ~ apppppsq ~ bpsq ~ g  �bsq ~ �    �   ppppq ~sq ~ �    q ~=q ~=q ~:q ~sq ~ j  �bsq ~ �    �   ppppq ~sq ~ �    q ~=q ~=psq ~ h  �bppppq ~=q ~=psq ~ m  �bsq ~ �    �   ppppq ~sq ~ �    q ~=q ~=psq ~ o  �bsq ~ �    �   ppppq ~sq ~ �    q ~=q ~=pppsq ~ �ppppq ~:pppppppppppq ~ �t 
Resultado:sq ~ ,  �b   n       &      �pq ~ q ~ �ppppppq ~#sq ~ T   uq ~ X   sq ~ Zt apresentarAssinaturappppppq ~ �sq ~ J��F���-l�~�;��B)  w�ppsq ~ L  �bppppq ~M  �b         ppq ~ Rsq ~ T   uq ~ X   sq ~ Zt 
assinaturapppppq ~ ^ppppppq ~ asq ~ bpsq ~ g  �bppppq ~Xq ~Xq ~Mpsq ~ j  �bppppq ~Xq ~Xpsq ~ h  �bppppq ~Xq ~Xpsq ~ m  �bppppq ~Xq ~Xpsq ~ o  �bppppq ~Xq ~Xppq ~ rpq ~ upp~r 7net.sf.jasperreports.engine.type.VerticalImageAlignEnum          xq ~ t MIDDLEsq ~ �  �b          �   P  pq ~ q ~ �ppppppq ~#sq ~ T   uq ~ X   sq ~ Zt apresentarAssinaturappppppq ~ �sq ~ J���9`����ڟQDh  �bppsq ~ �A   ppq ~ �q ~ �pppppsq ~ bpsq ~ g  �bsq ~ �    �   ppppq ~sq ~ �    q ~hq ~hq ~aq ~sq ~ j  �bsq ~ �    �   ppppq ~sq ~ �    q ~hq ~hpsq ~ h  �bppppq ~hq ~hpsq ~ m  �bsq ~ �    �   ppppq ~sq ~ �    q ~hq ~hpsq ~ o  �bsq ~ �    �   ppppq ~sq ~ �    q ~hq ~hpppsq ~ �ppppq ~apppppppppppq ~ �t Assinatura do Avaliadosq ~ �  �b   �       &      Ppq ~ q ~ �ppppppq ~#pppp~q ~ Gt CONTAINER_BOTTOMsq ~ J���m]�����X_G�  �bppsq ~ �@�  ppq ~ �q ~ apppppsq ~ bpsq ~ g  �bsq ~ �    �   ppppq ~sq ~ �    q ~}q ~}q ~xq ~sq ~ j  �bsq ~ �    �   ppppq ~sq ~ �    q ~}q ~}psq ~ h  �bppppq ~}q ~}psq ~ m  �bsq ~ �    �   ppppq ~sq ~ �    q ~}q ~}psq ~ o  �bsq ~ �    �   ppppq ~sq ~ �    q ~}q ~}pppsq ~ �ppppq ~xpppppppppppq ~ �t�
LEI N° 20.630, DE 08 DE NOVEMBRO DE 2019.

OBRIGA, para a prática de qualquer atividade física e esportiva, o preenchimento do
documento que especifica e dá outras providências.

O GOVERNADOR DO ESTADO DE GOIÁS
A ASSEMBLEIA LEGISLATIVA DO ESTADO DE GOIÁS, nos termos do art. 10 da Constituição Estadual, decreta e eu sanciono a seguinte Lei:

Art. 1º É obrigatório, para a prática de qualquer atividade física e esportiva, em clubes, academias e estabelecimentos similares, o preenchimento, pelo interessado, do Questionário de Prontidão para Atividade Física constante do Anexo Único desta Lei.

Parágrafo único. Se o interessado for menor de idade, o Questionário de Prontidão para Atividade Física deverá ser preenchido e assinado pelo responsável legal, juntamente com sua autorização por escrito.

Art. 2º Somente aos que responderem positivamente a qualquer uma das perguntas do Questionário será exigida a apresentação de atestado médico de aptidão física.

Art. 3º Fica revogada a Lei nº 12.881, de 03 de junho de 1996.

Art. 4º Esta Lei entra em vigor na data de sua publicação.

Goiânia, 08 de novembro de 2019.

RONALDO RAMOS CAIADO
Governador
sq ~ �  �b   2       &     pq ~ q ~ �ppppppq ~#ppppq ~ �sq ~ J�ú0�9%f�.0*E'F�  �bppsq ~ �A   pp~q ~ �t LEFTq ~ apppppsq ~ bpsq ~ g  �bsq ~ �    �   ppppq ~sq ~ �    q ~�q ~�q ~�q ~sq ~ j  �bsq ~ �    �   ppppq ~sq ~ �    q ~�q ~�psq ~ h  �bppppq ~�q ~�psq ~ m  �bsq ~ �    �   ppppq ~sq ~ �    q ~�q ~�psq ~ o  �bsq ~ �    �   ppppq ~sq ~ �    q ~�q ~�pppsq ~ �ppppq ~�pppppppppppq ~ �tVEstou ciente de que é recomendável conversar com um médico antes de aumentar meu nível atual de atividade física, por ter respondido "sim" a uma ou mais perguntas do Questionário de Prontidão para Atividade Física (PAR-Q). Assumo plena responsabilidade por qualquer atividade física praticada sem o atendimento a essa recomendação.xp  �b  :ppppppppt javapsr .net.sf.jasperreports.engine.base.JRBaseDataset      '� I PSEUDO_SERIAL_VERSION_UIDZ isMainB whenResourceMissingType[ fieldst &[Lnet/sf/jasperreports/engine/JRField;L filterExpressionq ~ [ groupst &[Lnet/sf/jasperreports/engine/JRGroup;L nameq ~ [ 
parameterst *[Lnet/sf/jasperreports/engine/JRParameter;L 
propertiesMapq ~ [ propertyExpressionst 8[Lnet/sf/jasperreports/engine/DatasetPropertyExpression;L queryt %Lnet/sf/jasperreports/engine/JRQuery;L resourceBundleq ~ L scriptletClassq ~ [ 
scriptletst *[Lnet/sf/jasperreports/engine/JRScriptlet;[ 
sortFieldst *[Lnet/sf/jasperreports/engine/JRSortField;L uuidq ~ B[ 	variablest )[Lnet/sf/jasperreports/engine/JRVariable;L whenResourceMissingTypeValuet >Lnet/sf/jasperreports/engine/type/WhenResourceMissingTypeEnum;xp  �b pppt parqur *[Lnet.sf.jasperreports.engine.JRParameter;" �*�`!  xp   rsr 0net.sf.jasperreports.engine.base.JRBaseParameter      '� 
Z isForPromptingZ isSystemDefinedL defaultValueExpressionq ~ L descriptionq ~ L evaluationTimet >Lnet/sf/jasperreports/engine/type/ParameterEvaluationTimeEnum;L nameq ~ L nestedTypeNameq ~ L 
propertiesMapq ~ L valueClassNameq ~ L valueClassRealNameq ~ xppppt REPORT_CONTEXTpsr +net.sf.jasperreports.engine.JRPropertiesMap      '� L baseq ~ L propertiesListq ~ L 
propertiesMapt Ljava/util/Map;xppppt )net.sf.jasperreports.engine.ReportContextpsq ~�pppt REPORT_PARAMETERS_MAPpsq ~�pppt 
java.util.Mappsq ~�pppt JASPER_REPORTS_CONTEXTpsq ~�pppt 0net.sf.jasperreports.engine.JasperReportsContextpsq ~�pppt 
JASPER_REPORTpsq ~�pppt (net.sf.jasperreports.engine.JasperReportpsq ~�pppt REPORT_CONNECTIONpsq ~�pppt java.sql.Connectionpsq ~�pppt REPORT_MAX_COUNTpsq ~�pppt java.lang.Integerpsq ~�pppt REPORT_DATA_SOURCEpsq ~�pppt (net.sf.jasperreports.engine.JRDataSourcepsq ~�pppt REPORT_SCRIPTLETpsq ~�pppt /net.sf.jasperreports.engine.JRAbstractScriptletpsq ~�pppt 
REPORT_LOCALEpsq ~�pppt java.util.Localepsq ~�pppt REPORT_RESOURCE_BUNDLEpsq ~�pppt java.util.ResourceBundlepsq ~�pppt REPORT_TIME_ZONEpsq ~�pppt java.util.TimeZonepsq ~�pppt REPORT_FORMAT_FACTORYpsq ~�pppt .net.sf.jasperreports.engine.util.FormatFactorypsq ~�pppt REPORT_CLASS_LOADERpsq ~�pppt java.lang.ClassLoaderpsq ~�pppt REPORT_URL_HANDLER_FACTORYpsq ~�pppt  java.net.URLStreamHandlerFactorypsq ~�pppt REPORT_FILE_RESOLVERpsq ~�pppt -net.sf.jasperreports.engine.util.FileResolverpsq ~�pppt REPORT_TEMPLATESpsq ~�pppt java.util.Collectionpsq ~�pppt SORT_FIELDSpsq ~�pppt java.util.Listpsq ~�pppt FILTERpsq ~�pppt )net.sf.jasperreports.engine.DatasetFilterpsq ~�pppt REPORT_VIRTUALIZERpsq ~�pppt )net.sf.jasperreports.engine.JRVirtualizerpsq ~�pppt IS_IGNORE_PAGINATIONpsq ~�pppt java.lang.Booleanpsq ~�  pt logoPadraoRelatoriopt logoPadraoRelatoriopsq ~�pppt java.lang.Stringpsq ~� pppt diametroJoelhopsq ~�pppt java.lang.Stringpsq ~� pppt 
alcanceMaximopsq ~�pppt java.lang.Stringpsq ~�  pppt nomeEmpresapsq ~�pppt java.lang.Stringpsq ~� pppt empresaNomepsq ~�pppt java.lang.Stringpsq ~� pppt 	pesoAtualpsq ~�pppt java.lang.Stringpsq ~� pppt empresaSitepsq ~�pppt java.lang.Stringpsq ~� pppt empresaEnderecopsq ~�pppt java.lang.Stringpsq ~� pppt 	fotoAlunopsq ~�pppt java.lang.Stringpsq ~� pppt 	nomeAlunopsq ~�pppt java.lang.Stringpsq ~� pppt idadepsq ~�pppt java.lang.Stringpsq ~� pppt 	avaliadorpsq ~�pppt java.lang.Stringpsq ~� sq ~ T    uq ~ X   sq ~ Zt c"C:\\PactoJ\\Sistemas\\treino-tronco\\src\\main\\resources\\br\\com\\pacto\\relatorio\\avaliacao\\"pppppt 
SUBREPORT_DIRpsq ~�pppt java.lang.Stringpsq ~� pppt 
dataAvaliacaopsq ~�pppt java.lang.Stringpsq ~� pppt sexopsq ~�pppt java.lang.Stringpsq ~� pppt contatopsq ~�pppt java.lang.Stringpsq ~� pppt proximaAvaliacaopsq ~�pppt java.lang.Stringpsq ~� pppt imcpsq ~�pppt java.lang.Stringpsq ~�  pppt 
anamneselistapsq ~�pppt java.util.Listpsq ~� pppt anteriorpsq ~�pppt java.lang.Stringpsq ~� pppt perimetriaJRpsq ~�pppt java.lang.Objectpsq ~� pppt 
diametroPunhopsq ~�pppt java.lang.Stringpsq ~� pppt alturapsq ~�pppt java.lang.Stringpsq ~� pppt pesopsq ~�pppt java.lang.Stringpsq ~� pppt resultadoIMCpsq ~�pppt java.lang.Stringpsq ~� pppt gordurapsq ~�pppt java.lang.Stringpsq ~� pppt circunferenciapsq ~�pppt java.lang.Stringpsq ~� pppt circunferenciaResultadopsq ~�pppt java.lang.Stringpsq ~� pppt imcResultadopsq ~�pppt java.lang.Stringpsq ~� pppt usuariopsq ~�pppt java.lang.Stringpsq ~� pppt composicaoResultadopsq ~�pppt java.lang.Stringpsq ~� pppt horaEmissaopsq ~�pppt java.lang.Stringpsq ~� pppt classificacaoFlexibilidadepsq ~�pppt java.lang.Stringpsq ~� pppt percGordurapsq ~�pppt java.lang.Stringpsq ~� pppt 	percOssospsq ~�pppt java.lang.Stringpsq ~� pppt percResiduospsq ~�pppt java.lang.Stringpsq ~� pppt percMusculospsq ~�pppt java.lang.Stringpsq ~� pppt 
recomendacoespsq ~�pppt java.lang.Stringpsq ~� pppt objetivosAlunopsq ~�pppt java.lang.Stringpsq ~� pppt peso1psq ~�pppt java.lang.Stringpsq ~�  pppt alturaAtualpsq ~�pppt java.lang.Stringpsq ~� pppt peso2psq ~�pppt java.lang.Stringpsq ~� pppt 	posteriorpsq ~�pppt java.lang.Stringpsq ~� pppt vo2psq ~�pppt java.lang.Stringpsq ~� pppt pressaoArterialpsq ~�pppt java.lang.Stringpsq ~� pppt peso3psq ~�pppt java.lang.Stringpsq ~�  pppt freqCardiacapsq ~�pppt java.lang.Stringpsq ~� pppt peso4psq ~�pppt java.lang.Stringpsq ~� pppt 	dataPeso1psq ~�pppt java.lang.Stringpsq ~� pppt 	dataPeso2psq ~�pppt java.lang.Stringpsq ~� pppt 	dataPeso3psq ~�pppt java.lang.Stringpsq ~� pppt 	dataPeso4psq ~�pppt java.lang.Stringpsq ~� pppt totalDobraspsq ~�pppt java.lang.Stringpsq ~� pppt 	protocolopsq ~�pppt java.lang.Stringpsq ~�  pppt 
anamneseJRpsq ~�pppt java.lang.Objectpsq ~� pppt 
rmlAbdomenpsq ~�pppt java.lang.Stringpsq ~� pppt ombrosAssimetricospsq ~�pppt java.lang.Stringpsq ~� pppt assimetriaQuadrilpsq ~�pppt java.lang.Stringpsq ~� pppt limiar1psq ~�pppt java.lang.Stringpsq ~� pppt limiar2psq ~�pppt java.lang.Stringpsq ~� pppt 
testeCampopsq ~�pppt java.lang.Stringpsq ~� pppt valor2TesteCampopsq ~�pppt java.lang.Stringpsq ~� pppt valor1TesteCampopsq ~�pppt java.lang.Stringpsq ~� pppt parqpsq ~�pppt java.lang.Stringpsq ~� pppt parqJRpsq ~�pppt java.lang.Objectpsq ~�  pppt dobrasJRpsq ~�pppt java.lang.Objectpsq ~� pppt pesoGordurapsq ~�pppt java.lang.Stringpsq ~� pppt 	pesoOsseopsq ~�pppt java.lang.Stringpsq ~� pppt pesoMuscularpsq ~�pppt java.lang.Stringpsq ~� pppt visaoLateralpsq ~�pppt java.lang.Stringpsq ~� pppt pesoResidualpsq ~�pppt java.lang.Stringpsq ~� pppt obsFlexibilidadepsq ~�pppt java.lang.Stringpsq ~� pppt obsPosturalpsq ~�pppt java.lang.Stringpsq ~� pppt rmlBracopsq ~�pppt java.lang.Stringpsq ~� pppt valor3TesteCampopsq ~�pppt java.lang.Stringpsq ~� pppt 	urlFrentepsq ~�pppt java.lang.Stringpsq ~� pppt 
urlDireitapsq ~�pppt java.lang.Stringpsq ~� pppt urlEsquerdapsq ~�pppt java.lang.Stringpsq ~� pppt urlCostapsq ~�pppt java.lang.Stringpsq ~� pppt 
showdobraspsq ~�pppt java.lang.Booleanpsq ~� pppt showperimetriapsq ~�pppt java.lang.Booleanpsq ~� pppt showflexibilidadepsq ~�pppt java.lang.Booleanpsq ~� pppt showposturalpsq ~�pppt java.lang.Booleanpsq ~� pppt showrmlpsq ~�pppt java.lang.Booleanpsq ~� pppt 
showvo2maxpsq ~�pppt java.lang.Booleanpsq ~� pppt showcomparacoespsq ~�pppt java.lang.Booleanpsq ~� pppt showrecomendacoespsq ~�pppt java.lang.Booleanpsq ~� pppt showanamnesepsq ~�pppt java.lang.Booleanpsq ~� pppt showparqpsq ~�pppt java.lang.Booleanpsq ~� pppt showpesoalturapsq ~�pppt java.lang.Booleanpsq ~� pppt 
showobjetivospsq ~�pppt java.lang.Booleanpsq ~� pppt comparativopsq ~�pppt java.lang.Objectpsq ~� pppt apresentarAssinaturapsq ~�pppt java.lang.Booleanpsq ~� pppt 
assinaturapsq ~�pppt java.lang.Stringpsq ~�psq ~    w   t -com.jaspersoft.studio.data.defaultdataadaptert (com.jaspersoft.studio.report.descriptionxsr java.util.HashMap���`� F 
loadFactorI 	thresholdxp?@     w      q ~�t 	Sample DBq ~�t  xpsr ,net.sf.jasperreports.engine.base.JRBaseQuery      '� [ chunkst +[Lnet/sf/jasperreports/engine/JRQueryChunk;L languageq ~ xppt sqlppppsq ~ J�������+��#�L`ur )[Lnet.sf.jasperreports.engine.JRVariable;b�|�,�D  xp   sr /net.sf.jasperreports.engine.base.JRBaseVariable      '� I PSEUDO_SERIAL_VERSION_UIDB calculationB 
incrementTypeZ isSystemDefinedB 	resetTypeL calculationValuet 2Lnet/sf/jasperreports/engine/type/CalculationEnum;L 
expressionq ~ L incrementGroupq ~ -L incrementTypeValuet 4Lnet/sf/jasperreports/engine/type/IncrementTypeEnum;L incrementerFactoryClassNameq ~ L incrementerFactoryClassRealNameq ~ L initialValueExpressionq ~ L nameq ~ L 
resetGroupq ~ -L resetTypeValuet 0Lnet/sf/jasperreports/engine/type/ResetTypeEnum;L valueClassNameq ~ L valueClassRealNameq ~ xp  w�   ~r 0net.sf.jasperreports.engine.type.CalculationEnum          xq ~ t SYSTEMpp~r 2net.sf.jasperreports.engine.type.IncrementTypeEnum          xq ~ t NONEppsq ~ T   uq ~ X   sq ~ Zt new java.lang.Integer(1)pppt PAGE_NUMBERp~r .net.sf.jasperreports.engine.type.ResetTypeEnum          xq ~ t REPORTq ~�psq ~�  w�   q ~�ppq ~�pppt MASTER_CURRENT_PAGEpq ~�q ~�psq ~�  w�   q ~�ppq ~�pppt MASTER_TOTAL_PAGESpq ~�q ~�psq ~�  w�   q ~�ppq ~�ppsq ~ T   uq ~ X   sq ~ Zt new java.lang.Integer(1)pppt 
COLUMN_NUMBERp~q ~�t PAGEq ~�psq ~�  w�   ~q ~�t COUNTsq ~ T   uq ~ X   sq ~ Zt new java.lang.Integer(1)ppppq ~�ppsq ~ T   uq ~ X   sq ~ Zt new java.lang.Integer(0)pppt REPORT_COUNTpq ~�q ~�psq ~�  w�   q ~�sq ~ T   uq ~ X   sq ~ Zt new java.lang.Integer(1)ppppq ~�ppsq ~ T   uq ~ X   sq ~ Zt new java.lang.Integer(0)pppt 
PAGE_COUNTpq ~�q ~�psq ~�  w�   q ~�sq ~ T   uq ~ X   sq ~ Zt new java.lang.Integer(1)ppppq ~�ppsq ~ T   uq ~ X   sq ~ Zt new java.lang.Integer(0)pppt COLUMN_COUNTp~q ~�t COLUMNq ~�psq ~�  w�    ~q ~�t NOTHINGsq ~ T   	uq ~ X   sq ~ Zt 1ppppq ~�ppsq ~ T   
uq ~ X   sq ~ Zt PAGE_NUMBERsq ~ Zt  + 1pppt REPORT_PAGEpq ~�t java.lang.Integerp~r <net.sf.jasperreports.engine.type.WhenResourceMissingTypeEnum          xq ~ t NULLq ~�p~r 0net.sf.jasperreports.engine.type.OrientationEnum          xq ~ t PORTRAITsq ~ sq ~    w   sq ~ w  �b          &        pq ~ q ~�ppppppq ~ ppppq ~ �sq ~ J����� ;=��B�  �bppsq ~ �A   ppq ~�q ~ �pppppsq ~ bpsq ~ g  �bppppq ~�q ~�q ~�psq ~ j  �bppppq ~�q ~�psq ~ h  �bppppq ~�q ~�psq ~ m  �bppppq ~�q ~�psq ~ o  �bpppsq ~ �?�  q ~�q ~�pppsq ~ �ppppq ~�pppppppppppq ~ �  �b        pp~q ~ Qt AUTOsq ~ T   uq ~ X   sq ~ Zt " Usuário: "+sq ~ Zt usuariosq ~ Zt + " - Data: "+sq ~ Zt horaEmissaosq ~ Zt  +" - Página " + sq ~ Zt REPORT_PAGEsq ~ Zt  + " de " + sq ~ Zt PAGE_NUMBERppppppppppppppxp  �b   pppppsq ~ sq ~    w   sq ~ ,  �b   -        d    ����pq ~ q ~ppppppq ~ pppp~q ~ Gt RELATIVE_TO_BAND_HEIGHTsq ~ J�.���s8T���B�  w�ppsq ~ L  �bppppq ~
  �b         ppq ~ �sq ~ T   uq ~ X   sq ~ Zt logoPadraoRelatorioppppp~q ~ ]t LEFTpppppppsq ~ bq ~sq ~ g  �bppppq ~q ~q ~
q ~sq ~ j  �bppppq ~q ~psq ~ h  �bppppq ~q ~q ~sq ~ m  �bppppq ~q ~q ~sq ~ o  �bppppq ~q ~ppq ~ rpq ~ upppsq ~ w  �b          T   n����pq ~ q ~ppppppq ~ ppppq ~ �sq ~ J���q�|�nF��F  �bppppppppppppsq ~ bpsq ~ g  �bppppq ~q ~q ~psq ~ j  �bppppq ~q ~psq ~ h  �bppppq ~q ~psq ~ m  �bppppq ~q ~psq ~ o  �bppppq ~q ~pppsq ~ �ppppq ~pppppppppppp  �b        ppq ~ �sq ~ T   uq ~ X   sq ~ Zt empresaNomeppppppppppppppsq ~ w  �b          T   n   pq ~ q ~ppppppq ~ ppppq ~ �sq ~ J���]��R$��BG  �bppppppppppppsq ~ bpsq ~ g  �bppppq ~*q ~*q ~(psq ~ j  �bppppq ~*q ~*psq ~ h  �bppppq ~*q ~*psq ~ m  �bppppq ~*q ~*psq ~ o  �bppppq ~*q ~*pppsq ~ �ppppq ~(pppppppppppp  �b        ppq ~ �sq ~ T   
uq ~ X   sq ~ Zt empresaEnderecoppppppppppppppsq ~ w  �b          T   n   pq ~ q ~ppppppq ~ ppppq ~ �sq ~ J���o�F�{)��`�J�  �bppppppppppppsq ~ bpsq ~ g  �bppppq ~7q ~7q ~5psq ~ j  �bppppq ~7q ~7psq ~ h  �bppppq ~7q ~7psq ~ m  �bppppq ~7q ~7psq ~ o  �bppppq ~7q ~7pppsq ~ �ppppq ~5pppppppppppp  �b        ppq ~ �sq ~ T   uq ~ X   sq ~ Zt empresaSiteppppppppppppppsr +net.sf.jasperreports.engine.base.JRBaseLine      '� I PSEUDO_SERIAL_VERSION_UIDB 	directionL directionValuet 4Lnet/sf/jasperreports/engine/type/LineDirectionEnum;xq ~ 8  �b   -           g����sq ~ �    ����pppq ~ q ~sq ~ �    ����ppppppppq ~ ppppq ~ �sq ~ J��n���"�D�k�Ot  w�ppsq ~ L  �bppppq ~D  �b ~r 2net.sf.jasperreports.engine.type.LineDirectionEnum          xq ~ t TOP_DOWNsq ~B  �b          &       #pq ~ q ~ppppppq ~ ppppq ~ �sq ~ J�����p��#�NH�  w�ppsq ~ L  �bppppq ~L  �b q ~Jxp  �b   /ppppp~r /net.sf.jasperreports.engine.type.PrintOrderEnum          xq ~ t VERTICAL~r 0net.sf.jasperreports.engine.type.SectionTypeEnum          xq ~ t BANDpppppsr 6net.sf.jasperreports.engine.design.JRReportCompileData      '� L crosstabCompileDataq ~�L datasetCompileDataq ~�L mainDatasetCompileDataq ~ xpsq ~�?@      w       xsq ~�?@      w       xur [B���T�  xp  ;�����   .�  parq_1744146172546_218184  ,net/sf/jasperreports/engine/fill/JREvaluator parameter_empresaNome 2Lnet/sf/jasperreports/engine/fill/JRFillParameter; parameter_showrecomendacoes parameter_apresentarAssinatura parameter_percGordura parameter_comparativo parameter_showcomparacoes parameter_peso2 parameter_valor2TesteCampo parameter_peso1 parameter_peso4 parameter_peso3 parameter_percOssos parameter_rmlAbdomen parameter_peso parameter_visaoLateral parameter_posterior parameter_freqCardiaca parameter_fotoAluno parameter_showobjetivos parameter_pesoResidual parameter_pressaoArterial parameter_REPORT_FILE_RESOLVER parameter_empresaSite parameter_avaliador parameter_usuario $parameter_REPORT_URL_HANDLER_FACTORY parameter_diametroPunho parameter_valor3TesteCampo parameter_percMusculos parameter_REPORT_CONNECTION parameter_testeCampo parameter_rmlBraco parameter_pesoOsseo parameter_obsPostural parameter_pesoMuscular parameter_JASPER_REPORT parameter_REPORT_TIME_ZONE parameter_REPORT_MAX_COUNT parameter_showrml parameter_anterior parameter_circunferencia parameter_idade parameter_urlEsquerda parameter_logoPadraoRelatorio parameter_nomeAluno parameter_objetivosAluno parameter_urlFrente parameter_showvo2max parameter_obsFlexibilidade parameter_REPORT_CLASS_LOADER parameter_REPORT_VIRTUALIZER parameter_urlDireita $parameter_classificacaoFlexibilidade parameter_diametroJoelho parameter_showanamnese parameter_SUBREPORT_DIR parameter_gordura parameter_alcanceMaximo parameter_protocolo parameter_proximaAvaliacao parameter_parqJR parameter_dataAvaliacao parameter_alturaAtual parameter_REPORT_PARAMETERS_MAP 
parameter_vo2 parameter_REPORT_DATA_SOURCE parameter_urlCosta parameter_showperimetria parameter_pesoGordura parameter_contato parameter_anamneseJR parameter_REPORT_LOCALE parameter_recomendacoes parameter_showdobras parameter_showpostural  parameter_JASPER_REPORTS_CONTEXT parameter_totalDobras parameter_nomeEmpresa parameter_REPORT_FORMAT_FACTORY parameter_altura parameter_showparq parameter_perimetriaJR 
parameter_imc parameter_assinatura parameter_dataPeso4 parameter_parq parameter_dataPeso3 parameter_composicaoResultado parameter_REPORT_TEMPLATES parameter_dataPeso2 parameter_dataPeso1 parameter_percResiduos parameter_REPORT_SCRIPTLET parameter_imcResultado parameter_assimetriaQuadril parameter_pesoAtual parameter_showpesoaltura  parameter_REPORT_RESOURCE_BUNDLE parameter_limiar2 !parameter_circunferenciaResultado parameter_limiar1 parameter_SORT_FIELDS parameter_IS_IGNORE_PAGINATION parameter_FILTER parameter_ombrosAssimetricos parameter_valor1TesteCampo parameter_dobrasJR parameter_empresaEndereco parameter_anamneselista parameter_horaEmissao parameter_resultadoIMC parameter_showflexibilidade parameter_REPORT_CONTEXT parameter_sexo variable_PAGE_NUMBER 1Lnet/sf/jasperreports/engine/fill/JRFillVariable; variable_MASTER_CURRENT_PAGE variable_MASTER_TOTAL_PAGES variable_COLUMN_NUMBER variable_REPORT_COUNT variable_PAGE_COUNT variable_COLUMN_COUNT variable_REPORT_PAGE <init> ()V Code
  � � �	  �  	  �  	  �  	  � 	 	  � 
 	  �  	  �  	  � 
 	  �  	  �  	  �  	  �  	  �  	  �  	  �  	  �  	  �  	  �  	  �  	  �  	  �  	  �  	  �  	  �  	  �  	  �  	  �   	  � ! 	  � " 	  � # 	  � $ 	  � % 	  � & 	  � ' 	  � ( 	  � ) 	  � * 	  � + 	  � , 	  � - 	  � . 	  � / 	  � 0 	  � 1 	  � 2 	  � 3 	  � 4 	  � 5 	  � 6 	  � 7 	  � 8 	  � 9 	  � : 	  � ; 	  � < 	  � = 	  � > 	  � ? 	  � @ 	  � A 	  � B 	  C 	  D 	  E 	  F 	 	 G 	  H 	 
 I 	  J 	  K 	  L 	  M 	  N 	  O 	  P 	  Q 	  R 	 ! S 	 # T 	 % U 	 ' V 	 ) W 	 + X 	 - Y 	 / Z 	 1 [ 	 3 \ 	 5 ] 	 7 ^ 	 9 _ 	 ; ` 	 = a 	 ? b 	 A c 	 C d 	 E e 	 G f 	 I g 	 K h 	 M i 	 O j 	 Q k 	 S l 	 U m 	 W n 	 Y o 	 [ p 	 ] q 	 _ r 	 a s 	 c t 	 e u 	 g v 	 i w 	 k x y	 m z y	 o { y	 q | y	 s } y	 u ~ y	 w  y	 y � y LineNumberTable customizedInit 0(Ljava/util/Map;Ljava/util/Map;Ljava/util/Map;)V
 ~� 
initParams (Ljava/util/Map;)V
 ��� 
initFields
 ��� initVars� empresaNome��� 
java/util/Map�� get &(Ljava/lang/Object;)Ljava/lang/Object;� 0net/sf/jasperreports/engine/fill/JRFillParameter� showrecomendacoes� apresentarAssinatura� percGordura� comparativo� showcomparacoes� peso2� valor2TesteCampo� peso1� peso4� peso3� 	percOssos� 
rmlAbdomen� peso� visaoLateral� 	posterior� freqCardiaca� 	fotoAluno� 
showobjetivos� pesoResidual� pressaoArterial� REPORT_FILE_RESOLVER� empresaSite� 	avaliador� usuario� REPORT_URL_HANDLER_FACTORY� 
diametroPunho� valor3TesteCampo� percMusculos� REPORT_CONNECTION� 
testeCampo� rmlBraco� 	pesoOsseo� obsPostural� pesoMuscular� 
JASPER_REPORT� REPORT_TIME_ZONE� REPORT_MAX_COUNT� showrml� anterior� circunferencia� idade� urlEsquerda� logoPadraoRelatorio� 	nomeAluno� objetivosAluno� 	urlFrente� 
showvo2max� obsFlexibilidade� REPORT_CLASS_LOADER� REPORT_VIRTUALIZER� 
urlDireita� classificacaoFlexibilidade� diametroJoelho� showanamnese� 
SUBREPORT_DIR  gordura 
alcanceMaximo 	protocolo proximaAvaliacao parqJR
 
dataAvaliacao alturaAtual REPORT_PARAMETERS_MAP vo2 REPORT_DATA_SOURCE urlCosta showperimetria pesoGordura contato 
anamneseJR 
REPORT_LOCALE  
recomendacoes" 
showdobras$ showpostural& JASPER_REPORTS_CONTEXT( totalDobras* nomeEmpresa, REPORT_FORMAT_FACTORY. altura0 showparq2 perimetriaJR4 imc6 
assinatura8 	dataPeso4: parq< 	dataPeso3> composicaoResultado@ REPORT_TEMPLATESB 	dataPeso2D 	dataPeso1F percResiduosH REPORT_SCRIPTLETJ imcResultadoL assimetriaQuadrilN 	pesoAtualP showpesoalturaR REPORT_RESOURCE_BUNDLET limiar2V circunferenciaResultado
 XY� initParams1[ limiar1] SORT_FIELDS_ IS_IGNORE_PAGINATIONa FILTERc ombrosAssimetricose valor1TesteCampog dobrasJRi empresaEnderecok 
anamneselistam horaEmissaoo resultadoIMCq showflexibilidades REPORT_CONTEXTu sexow PAGE_NUMBERy /net/sf/jasperreports/engine/fill/JRFillVariable{ MASTER_CURRENT_PAGE} MASTER_TOTAL_PAGES 
COLUMN_NUMBER� REPORT_COUNT� 
PAGE_COUNT� COLUMN_COUNT� REPORT_PAGE evaluate (I)Ljava/lang/Object; 
Exceptions� java/lang/Throwable� UC:\PactoJ\Sistemas\treino-tronco\src\main\resources\br\com\pacto\relatorio\avaliacao\� java/lang/Integer
�� �� (I)V
x��� getValue ()Ljava/lang/Object;
���� intValue ()I
��� java/lang/String� java/lang/StringBuffer
���� valueOf &(Ljava/lang/Object;)Ljava/lang/String;
�� �� (Ljava/lang/String;)V� anamnese.jasper
���� append ,(Ljava/lang/String;)Ljava/lang/StringBuffer;
���� toString ()Ljava/lang/String;� java/lang/Boolean�  Usuário: � 	 - Data: �  - Página 
���� ,(Ljava/lang/Object;)Ljava/lang/StringBuffer;�  de  evaluateOld
x��� getOldValue evaluateEstimated
x��� getEstimatedValue 
SourceFile !     z                 	     
               
                                                                                                     !     "     #     $     %     &     '     (     )     *     +     ,     -     .     /     0     1     2     3     4     5     6     7     8     9     :     ;     <     =     >     ?     @     A     B     C     D     E     F     G     H     I     J     K     L     M     N     O     P     Q     R     S     T     U     V     W     X     Y     Z     [     \     ]     ^     _     `     a     b     c     d     e     f     g     h     i     j     k     l     m     n     o     p     q     r     s     t     u     v     w     x y    z y    { y    | y    } y    ~ y     y    � y   	  � �  �  k    g*� �*� �*� �*� �*� �*� �*� �*� �*� �*� �*� �*� �*� �*� �*� �*� �*� �*� �*� �*� �*� �*� �*� �*� �*� �*� �*� �*� �*� �*� �*� �*� �*� �*� �*� �*� �*� �*� �*� �*� �*� �*� �*� �*� �*� �*� �*� �*� �*� �*� �*� �*� �*� �*� �*� �*� �*� �*� �*� �*� �*� �*� �*� *�*�*�*�*�
*�*�*�*�*�*�*�*�*�*�*� *�"*�$*�&*�(*�**�,*�.*�0*�2*�4*�6*�8*�:*�<*�>*�@*�B*�D*�F*�H*�J*�L*�N*�P*�R*�T*�V*�X*�Z*�\*�^*�`*�b*�d*�f*�h*�j*�l*�n*�p*�r*�t*�v*�x�   z  � |      	          "  '   , ! 1 " 6 # ; $ @ % E & J ' O ( T ) Y * ^ + c , h - m . r / w 0 | 1 � 2 � 3 � 4 � 5 � 6 � 7 � 8 � 9 � : � ; � < � = � > � ? � @ � A � B � C � D � E � F � G � H � I � J � K L M
 N O P Q! R& S+ T0 U5 V: W? XD YI ZN [S \X ]] ^b _g `l aq bv c{ d� e� f� g� h� i� j� k� l� m� n� o� p� q� r� s� t� u� v� w� x� y� z� {� |� }� ~  � � � � �  �% �* �/ �4 �9 �> �C �H �M �R �W �\ �a �f  {|  �   4     *+�}*,��*-���   z       �  � 
 �  � �  �  �    F*+��� ��� �*+��� ��� �*+��� ��� �*+��� ��� �*+��� ��� �*+��� ��� �*+��� ��� �*+��� ��� �*+��� ��� �*+��� ��� �*+��� ��� �*+��� ��� �*+��� ��� �*+��� ��� �*+��� ��� �*+��� ��� �*+��� ��� �*+��� ��� �*+��� ��� �*+��� ��� �*+��� ��� �*+��� ��� �*+��� ��� �*+��� ��� �*+��� ��� �*+��� ��� �*+ù� ��� �*+Ź� ��� �*+ǹ� ��� �*+ɹ� ��� �*+˹� ��� �*+͹� ��� �*+Ϲ� ��� �*+ѹ� ��� �*+ӹ� ��� �*+չ� ��� �*+׹� ��� �*+ٹ� ��� �*+۹� ��� �*+ݹ� ��� �*+߹� ��� �*+�� ��� �*+�� ��� �*+�� ��� �*+�� ��� �*+�� ��� �*+�� ��� �*+�� ��� �*+�� ��� �*+�� ��� �*+�� ��� �*+��� ��� �*+��� ��� �*+��� ��� �*+��� ��� �*+��� ��� �*+��� ��� �*+�� ��� �*+�� ��� �*+�� ��� �*+�� ��� �*+	�� ��� *+�� ���*+
�� ���*+�� ���*+�� ���*+�� ���
*+�� ���*+�� ���*+�� ���*+�� ���*+�� ���*+�� ���*+!�� ���*+#�� ���*+%�� ���*+'�� ���*+)�� ��� *++�� ���"*+-�� ���$*+/�� ���&*+1�� ���(*+3�� ���**+5�� ���,*+7�� ���.*+9�� ���0*+;�� ���2*+=�� ���4*+?�� ���6*+A�� ���8*+C�� ���:*+E�� ���<*+G�� ���>*+I�� ���@*+K�� ���B*+M�� ���D*+O�� ���F*+Q�� ���H*+S�� ���J*+U�� ���L*+�W�   z  � f   �  �   � 0 � @ � P � ` � p � � � � � � � � � � � � � � � � �  � �  �0 �@ �P �` �p �� �� �� �� �� �� �� �� �  � �  �0 �@ �P �` �p �� �� �� �� �� �� �� �� �  � �  �0 �@ �P �` �p �� �� �� �� �� �� �� �� �  � �  �0 �@ �P �` �p �� �� �� �� �� �� �� �� �  � �  �0 �@ �P �` �p �������� 	
 0@
E Y�  �  1     �*+Z�� ���N*+\�� ���P*+^�� ���R*+`�� ���T*+b�� ���V*+d�� ���X*+f�� ���Z*+h�� ���\*+j�� ���^*+l�� ���`*+n�� ���b*+p�� ���d*+r�� ���f*+t�� ���h�   z   >       0 @ P ` p � � �  �! �" �# �$ ��  �         �   z      , ��  �   �     �*+v�� �x�j*+z�� �x�l*+|�� �x�n*+~�� �x�p*+��� �x�r*+��� �x�t*+��� �x�v*+��� �x�x�   z   & 	  4 5  6 07 @8 P9 `: p; �< �� �    � �  7    CM�  >          y   �   �   �   �   �   �   �   �   �   �      $  2  @  N  \  j  x  �  �  �  �  �  �  ��M����Y��M����Y��M����Y��M����Y��M����Y��M����Y��M�y��Y��M�m��Y��M�a��Z_��M�T*�j������`��Z_��M�9*� ܶ���M�+*� �����M�*�\����M�*� �����M�*� �����M� �*� ޶���M� �*� ض���M� �*�h����M� �*� ����M� �*� ���M� ���Y*� ��������������M� �*�0����M� �*� �����M� s*�,����M� e*� �����M� W��Y���*� ����������*�`���������*�x���������*�j��������M,�   z   � 8  D F |J �K �O �P �T �U �Y �Z �^ �_ �c �d �h �i �m �n �r �s �w �x �|}���$�'�2�5�@�C�N�Q�\�_�j�m�x�{�����������������������������A� �� �    � �  7    CM�  >          y   �   �   �   �   �   �   �   �   �   �      $  2  @  N  \  j  x  �  �  �  �  �  �  ��M����Y��M����Y��M����Y��M����Y��M����Y��M����Y��M�y��Y��M�m��Y��M�a��Z_��M�T*�j������`��Z_��M�9*� ܶ���M�+*� �����M�*�\����M�*� �����M�*� �����M� �*� ޶���M� �*� ض���M� �*�h����M� �*� ����M� �*� ���M� ���Y*� ��������������M� �*�0����M� �*� �����M� s*�,����M� e*� �����M� W��Y���*� ����������*�`���������*�x���������*�j��������M,�   z   � 8  � � |� �� �� �� �� �� �� �� �� �� �� �� � � � � � � � � �$ '$2%5)@*C.N/Q3\4_8j9m=x>{B�C�G�H�L�M�Q�R�V�W�[�\�`�a�eAm �� �    � �  7    CM�  >          y   �   �   �   �   �   �   �   �   �   �      $  2  @  N  \  j  x  �  �  �  �  �  �  ��M����Y��M����Y��M����Y��M����Y��M����Y��M����Y��M�y��Y��M�m��Y��M�a��Z_��M�T*�j������`��Z_��M�9*� ܶ���M�+*� �����M�*�\����M�*� �����M�*� �����M� �*� ޶���M� �*� ض���M� �*�h����M� �*� ����M� �*� ���M� ���Y*� ��������������M� �*�0����M� �*� �����M� s*�,����M� e*� �����M� W��Y���*� ����������*�`���������*�x���������*�j��������M,�   z   � 8  v x || �} �� �� �� �� �� �� �� �� �� �� �� �� �� �� �� �� �� �� ������$�'�2�5�@�C�N�Q�\�_�j�m�x�{�����������������������������A �    t _1744146172546_218184t 2net.sf.jasperreports.engine.design.JRJavacCompiler