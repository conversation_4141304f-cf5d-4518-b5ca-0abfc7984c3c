<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.5.1.final using JasperReports Library version 6.5.1  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="parq" pageWidth="595" pageHeight="842" columnWidth="555" leftMargin="20" rightMargin="20" topMargin="20" bottomMargin="20" uuid="acfe1f23-0c9b-4c60-baa1-f7c0ae928e2b">
    <property name="com.jaspersoft.studio.data.defaultdataadapter" value="Sample DB"/>
    <property name="com.jaspersoft.studio.report.description" value=""/>
    <parameter name="logoPadraoRelatorio" class="java.lang.String" isForPrompting="false">
        <parameterDescription><![CDATA[logoPadraoRelatorio]]></parameterDescription>
    </parameter>
    <parameter name="diametroJoelho" class="java.lang.String"/>
    <parameter name="alcanceMaximo" class="java.lang.String"/>
    <parameter name="nomeEmpresa" class="java.lang.String" isForPrompting="false"/>
    <parameter name="empresaNome" class="java.lang.String"/>
    <parameter name="pesoAtual" class="java.lang.String"/>
    <parameter name="empresaSite" class="java.lang.String"/>
    <parameter name="empresaEndereco" class="java.lang.String"/>
    <parameter name="fotoAluno" class="java.lang.String"/>
    <parameter name="nomeAluno" class="java.lang.String"/>
    <parameter name="idade" class="java.lang.String"/>
    <parameter name="avaliador" class="java.lang.String"/>
    <parameter name="SUBREPORT_DIR" class="java.lang.String">
        <defaultValueExpression><![CDATA["C:\\PactoJ\\Sistemas\\treino-tronco\\src\\main\\resources\\br\\com\\pacto\\relatorio\\avaliacao\\"]]></defaultValueExpression>
    </parameter>
    <parameter name="dataAvaliacao" class="java.lang.String"/>
    <parameter name="sexo" class="java.lang.String"/>
    <parameter name="contato" class="java.lang.String"/>
    <parameter name="proximaAvaliacao" class="java.lang.String"/>
    <parameter name="imc" class="java.lang.String"/>
    <parameter name="anamneselista" class="java.util.List" isForPrompting="false"/>
    <parameter name="anterior" class="java.lang.String"/>
    <parameter name="perimetriaJR" class="java.lang.Object"/>
    <parameter name="diametroPunho" class="java.lang.String"/>
    <parameter name="altura" class="java.lang.String"/>
    <parameter name="peso" class="java.lang.String"/>
    <parameter name="resultadoIMC" class="java.lang.String"/>
    <parameter name="gordura" class="java.lang.String"/>
    <parameter name="circunferencia" class="java.lang.String"/>
    <parameter name="circunferenciaResultado" class="java.lang.String"/>
    <parameter name="imcResultado" class="java.lang.String"/>
    <parameter name="usuario" class="java.lang.String"/>
    <parameter name="composicaoResultado" class="java.lang.String"/>
    <parameter name="horaEmissao" class="java.lang.String"/>
    <parameter name="classificacaoFlexibilidade" class="java.lang.String"/>
    <parameter name="percGordura" class="java.lang.String"/>
    <parameter name="percOssos" class="java.lang.String"/>
    <parameter name="percResiduos" class="java.lang.String"/>
    <parameter name="percMusculos" class="java.lang.String"/>
    <parameter name="recomendacoes" class="java.lang.String"/>
    <parameter name="objetivosAluno" class="java.lang.String"/>
    <parameter name="peso1" class="java.lang.String"/>
    <parameter name="alturaAtual" class="java.lang.String" isForPrompting="false"/>
    <parameter name="peso2" class="java.lang.String"/>
    <parameter name="posterior" class="java.lang.String"/>
    <parameter name="vo2" class="java.lang.String"/>
    <parameter name="pressaoArterial" class="java.lang.String"/>
    <parameter name="peso3" class="java.lang.String"/>
    <parameter name="freqCardiaca" class="java.lang.String" isForPrompting="false"/>
    <parameter name="peso4" class="java.lang.String"/>
    <parameter name="dataPeso1" class="java.lang.String"/>
    <parameter name="dataPeso2" class="java.lang.String"/>
    <parameter name="dataPeso3" class="java.lang.String"/>
    <parameter name="dataPeso4" class="java.lang.String"/>
    <parameter name="totalDobras" class="java.lang.String"/>
    <parameter name="protocolo" class="java.lang.String"/>
    <parameter name="anamneseJR" class="java.lang.Object" isForPrompting="false"/>
    <parameter name="rmlAbdomen" class="java.lang.String"/>
    <parameter name="ombrosAssimetricos" class="java.lang.String"/>
    <parameter name="assimetriaQuadril" class="java.lang.String"/>
    <parameter name="limiar1" class="java.lang.String"/>
    <parameter name="limiar2" class="java.lang.String"/>
    <parameter name="testeCampo" class="java.lang.String"/>
    <parameter name="valor2TesteCampo" class="java.lang.String"/>
    <parameter name="valor1TesteCampo" class="java.lang.String"/>
    <parameter name="parq" class="java.lang.String"/>
    <parameter name="parqJR" class="java.lang.Object"/>
    <parameter name="dobrasJR" class="java.lang.Object" isForPrompting="false"/>
    <parameter name="pesoGordura" class="java.lang.String"/>
    <parameter name="pesoOsseo" class="java.lang.String"/>
    <parameter name="pesoMuscular" class="java.lang.String"/>
    <parameter name="visaoLateral" class="java.lang.String"/>
    <parameter name="pesoResidual" class="java.lang.String"/>
    <parameter name="obsFlexibilidade" class="java.lang.String"/>
    <parameter name="obsPostural" class="java.lang.String"/>
    <parameter name="rmlBraco" class="java.lang.String"/>
    <parameter name="valor3TesteCampo" class="java.lang.String"/>
    <parameter name="urlFrente" class="java.lang.String"/>
    <parameter name="urlDireita" class="java.lang.String"/>
    <parameter name="urlEsquerda" class="java.lang.String"/>
    <parameter name="urlCosta" class="java.lang.String"/>
    <parameter name="showdobras" class="java.lang.Boolean"/>
    <parameter name="showperimetria" class="java.lang.Boolean"/>
    <parameter name="showflexibilidade" class="java.lang.Boolean"/>
    <parameter name="showpostural" class="java.lang.Boolean"/>
    <parameter name="showrml" class="java.lang.Boolean"/>
    <parameter name="showvo2max" class="java.lang.Boolean"/>
    <parameter name="showcomparacoes" class="java.lang.Boolean"/>
    <parameter name="showrecomendacoes" class="java.lang.Boolean"/>
    <parameter name="showanamnese" class="java.lang.Boolean"/>
    <parameter name="showparq" class="java.lang.Boolean"/>
    <parameter name="showpesoaltura" class="java.lang.Boolean"/>
    <parameter name="showobjetivos" class="java.lang.Boolean"/>
    <parameter name="comparativo" class="java.lang.Object"/>
    <parameter name="apresentarAssinatura" class="java.lang.Boolean"/>
    <parameter name="assinatura" class="java.lang.String"/>
    <queryString>
        <![CDATA[]]>
    </queryString>
    <variable name="REPORT_PAGE" class="java.lang.Integer" resetType="Page">
        <variableExpression><![CDATA[1]]></variableExpression>
        <initialValueExpression><![CDATA[$V{PAGE_NUMBER} + 1]]></initialValueExpression>
    </variable>
    <background>
        <band splitType="Stretch"/>
    </background>
    <pageHeader>
        <band height="47">
            <image scaleImage="RetainShape" hAlign="Left" onErrorType="Icon" evaluationTime="Page">
                <reportElement stretchType="RelativeToBandHeight" x="0" y="-11" width="100" height="45" uuid="3854af15-81f3-42c9-952e-ff0c19b19273"/>
                <box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0"/>
                <imageExpression><![CDATA[$P{logoPadraoRelatorio}]]></imageExpression>
            </image>
            <textField>
                <reportElement x="110" y="-11" width="340" height="15" uuid="4608a8c7-1007-4603-8886-9c71f47cc96e"/>
                <textFieldExpression><![CDATA[$P{empresaNome}]]></textFieldExpression>
            </textField>
            <textField>
                <reportElement x="110" y="4" width="340" height="15" uuid="52030124-f695-4247-8312-f9068c5d92c6"/>
                <textFieldExpression><![CDATA[$P{empresaEndereco}]]></textFieldExpression>
            </textField>
            <textField>
                <reportElement x="110" y="19" width="340" height="15" uuid="1229b89c-608a-4a81-bfd0-d16fe946ee7b"/>
                <textFieldExpression><![CDATA[$P{empresaSite}]]></textFieldExpression>
            </textField>
            <line>
                <reportElement x="103" y="-11" width="1" height="45" forecolor="#EDEDED" backcolor="#EDEDED" uuid="22ca44ef-6b97-4f74-928f-6eedafe1aef7"/>
            </line>
            <line>
                <reportElement x="0" y="35" width="550" height="1" uuid="fcab230f-a44e-488c-b694-fe161b9aaa70"/>
            </line>
        </band>
    </pageHeader>
    <detail>
        <band height="91">
            <image scaleImage="RetainShape" hAlign="Center" isUsingCache="false" onErrorType="Icon" evaluationTime="Report">
                <reportElement positionType="FixRelativeToBottom" stretchType="RelativeToTallestObject" x="9" y="-4" width="90" height="75" uuid="05141be1-c451-488a-86bb-fb3444dfa8ce"/>
                <imageExpression><![CDATA[$P{fotoAluno}]]></imageExpression>
            </image>
            <textField>
                <reportElement x="112" y="-5" width="438" height="25" uuid="3e845ceb-ea6a-44e5-b118-a1aafdb954ba"/>
                <textElement verticalAlignment="Middle">
                    <font size="16" isBold="true"/>
                </textElement>
                <textFieldExpression><![CDATA[$P{nomeAluno}]]></textFieldExpression>
            </textField>
            <textField evaluationTime="Page">
                <reportElement x="180" y="26" width="128" height="26" uuid="eae8723a-8dc1-4af6-86ec-2670823e9e54"/>
                <textElement>
                    <font fontName="Arial" size="12"/>
                </textElement>
                <textFieldExpression><![CDATA[$P{idade}]]></textFieldExpression>
            </textField>
            <staticText>
                <reportElement x="112" y="26" width="68" height="26" uuid="6cdb70a4-6d8b-49ac-ac0a-8b5185451601"/>
                <textElement>
                    <font size="12" isBold="true"/>
                </textElement>
                <text><![CDATA[Idade:]]></text>
            </staticText>
            <staticText>
                <reportElement x="340" y="26" width="68" height="26" uuid="87ac4589-118b-429f-88f6-190dad45e908"/>
                <textElement>
                    <font size="12" isBold="true"/>
                </textElement>
                <text><![CDATA[Sexo:]]></text>
            </staticText>
            <textField evaluationTime="Page">
                <reportElement x="408" y="26" width="140" height="26" uuid="aebfde3c-eec5-4ea3-9e3d-140c82059f4f"/>
                <textElement>
                    <font fontName="Arial" size="12"/>
                </textElement>
                <textFieldExpression><![CDATA[$P{sexo}]]></textFieldExpression>
            </textField>
            <staticText>
                <reportElement x="112" y="53" width="68" height="26" uuid="c6f5f341-3342-460a-aa5b-919208ec1d73"/>
                <textElement>
                    <font size="12" isBold="true"/>
                </textElement>
                <text><![CDATA[Data:]]></text>
            </staticText>
            <textField evaluationTime="Page">
                <reportElement x="180" y="53" width="128" height="26" uuid="372852b9-5bea-4da4-b699-3e5c82d34bc0"/>
                <textElement>
                    <font fontName="Arial" size="12"/>
                </textElement>
                <textFieldExpression><![CDATA[$P{dataAvaliacao}]]></textFieldExpression>
            </textField>
        </band>
        <band height="570">
            <rectangle>
                <reportElement x="0" y="-1" width="550" height="41" backcolor="#EDEDED" uuid="b8e71b65-0536-418a-8305-f73ea145e634"/>
                <graphicElement>
                    <pen lineWidth="0.0" lineColor="#EDEDED"/>
                </graphicElement>
            </rectangle>
            <staticText>
                <reportElement x="0" y="-1" width="550" height="41" uuid="e64ff06f-4db5-4fd5-8ab8-beae4a3b9d34"/>
                <box leftPadding="0">
                    <topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
                    <leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
                    <bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
                    <rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
                </box>
                <textElement textAlignment="Center" verticalAlignment="Middle">
                    <font size="14" isBold="true"/>
                </textElement>
                <text><![CDATA[Par-Q]]></text>
            </staticText>
            <subreport>
                <reportElement x="2" y="48" width="550" height="31" uuid="8ea17648-e0af-4d80-8140-9036d67bddf5"/>
                <dataSourceExpression><![CDATA[$P{parqJR}]]></dataSourceExpression>
                <subreportExpression><![CDATA[$P{SUBREPORT_DIR} + "anamnese.jasper"]]></subreportExpression>
            </subreport>
            <textField evaluationTime="Page">
                <reportElement positionType="Float" x="126" y="382" width="308" height="37" uuid="86fa8961-6db3-4bd2-a3a4-10741af6b179"/>
                <box>
                    <topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
                    <leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
                    <bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
                    <rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
                </box>
                <textElement textAlignment="Center" verticalAlignment="Middle">
                    <font size="20" isBold="true"/>
                </textElement>
                <textFieldExpression><![CDATA[$P{parq}]]></textFieldExpression>
            </textField>
            <staticText>
                <reportElement positionType="Float" x="126" y="358" width="308" height="25" uuid="80469067-99c3-4d39-b34b-edd44880304d"/>
                <box leftPadding="0">
                    <topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
                    <leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
                    <bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
                    <rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
                </box>
                <textElement textAlignment="Center" verticalAlignment="Middle">
                    <font size="14" isBold="false"/>
                </textElement>
                <text><![CDATA[Resultado:]]></text>
            </staticText>
            <image scaleImage="RetainShape" hAlign="Center" vAlign="Middle" isUsingCache="false" onErrorType="Icon" evaluationTime="Report">
                <reportElement positionType="Float" x="0" y="422" width="550" height="110" uuid="8d7ef53b-bf93-4229-be8d-46feaba42d6c">
                    <printWhenExpression><![CDATA[$P{apresentarAssinatura}]]></printWhenExpression>
                </reportElement>
                <imageExpression><![CDATA[$P{assinatura}]]></imageExpression>
            </image>
            <staticText>
                <reportElement positionType="Float" x="80" y="532" width="400" height="25" uuid="bbc3da9f-0351-4468-880b-b1873960bba6">
                    <printWhenExpression><![CDATA[$P{apresentarAssinatura}]]></printWhenExpression>
                </reportElement>
                <box leftPadding="0">
                    <topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
                    <leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
                    <bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
                    <rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
                </box>
                <textElement textAlignment="Center" verticalAlignment="Middle">
                    <font size="10" isBold="true"/>
                </textElement>
                <text><![CDATA[Assinatura do Avaliado]]></text>
            </staticText>
            <staticText>
                <reportElement positionType="Float" stretchType="ContainerBottom" x="2" y="80" width="550" height="206" uuid="c18fe1c3-585f-47ff-a0e2-02b36d0b5da8"/>
                <box leftPadding="0">
                    <topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
                    <leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
                    <bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
                    <rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
                </box>
                <textElement textAlignment="Center" verticalAlignment="Middle">
                    <font size="6" isBold="false"/>
                </textElement>
                <text><![CDATA[
LEI N° 20.630, DE 08 DE NOVEMBRO DE 2019.

OBRIGA, para a prática de qualquer atividade física e esportiva, o preenchimento do
documento que especifica e dá outras providências.

O GOVERNADOR DO ESTADO DE GOIÁS
A ASSEMBLEIA LEGISLATIVA DO ESTADO DE GOIÁS, nos termos do art. 10 da Constituição Estadual, decreta e eu sanciono a seguinte Lei:

Art. 1º É obrigatório, para a prática de qualquer atividade física e esportiva, em clubes, academias e estabelecimentos similares, o preenchimento, pelo interessado, do Questionário de Prontidão para Atividade Física constante do Anexo Único desta Lei.

Parágrafo único. Se o interessado for menor de idade, o Questionário de Prontidão para Atividade Física deverá ser preenchido e assinado pelo responsável legal, juntamente com sua autorização por escrito.

Art. 2º Somente aos que responderem positivamente a qualquer uma das perguntas do Questionário será exigida a apresentação de atestado médico de aptidão física.

Art. 3º Fica revogada a Lei nº 12.881, de 03 de junho de 1996.

Art. 4º Esta Lei entra em vigor na data de sua publicação.

Goiânia, 08 de novembro de 2019.

RONALDO RAMOS CAIADO
Governador
]]></text>
            </staticText>
            <staticText>
                <reportElement positionType="Float" x="2" y="287" width="550" height="50" uuid="e02e302a-4527-4686-92c3-ba309c392566"/>
                <box leftPadding="0">
                    <topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
                    <leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
                    <bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
                    <rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
                </box>
                <textElement textAlignment="Left" verticalAlignment="Middle">
                    <font size="10" isBold="false"/>
                </textElement>
                <text><![CDATA[Estou ciente de que é recomendável conversar com um médico antes de aumentar meu nível atual de atividade física, por ter respondido "sim" a uma ou mais perguntas do Questionário de Prontidão para Atividade Física (PAR-Q). Assumo plena responsabilidade por qualquer atividade física praticada sem o atendimento a essa recomendação.]]></text>
            </staticText>
        </band>
    </detail>
    <pageFooter>
        <band height="14">
            <textField evaluationTime="Auto">
                <reportElement x="0" y="0" width="550" height="14" uuid="0b203b3d-97c6-428c-9ef3-ac8216929396"/>
                <box>
                    <topPen lineWidth="1.0"/>
                </box>
                <textElement textAlignment="Left" verticalAlignment="Middle">
                    <font size="8" isBold="true"/>
                </textElement>
                <textFieldExpression><![CDATA[" Usuário: "+$P{usuario}+ " - Data: "+$P{horaEmissao} +" - Página " + $V{REPORT_PAGE} + " de " + $V{PAGE_NUMBER}]]></textFieldExpression>
            </textField>
        </band>
    </pageFooter>
</jasperReport>
