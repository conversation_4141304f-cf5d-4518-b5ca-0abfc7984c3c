app.name=@artifact.name@
app.config.debug=false
app.config.root-package=com.pacto.relatorioms
app.config.free-controllers=temp

discovery.url=http://localhost:8101
autenticacao.url=http://localhost:8100

server.servlet.encoding.charset=UTF-8
server.servlet.encoding.enabled=true
server.servlet.encoding.force=true
server.port=8126
server.servlet.context-path=/

secret.key.path=C:\\opt\\council\\elrond.txt
secret.key.zw.path=C:\\opt\\council\\elrond.txt
auth.secret.persona-path=C:\\opt\\council\\elrond.txt

spring.jpa.show-sql=false
spring.jpa.hibernate.ddl-auto=none
spring.jpa.properties.hibernate.format_sql=true
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.PostgreSQL93Dialect
spring.jpa.database-platform=org.hibernate.dialect.PostgreSQL93Dialect

spring.datasource.url=******************************************
spring.datasource.username=postgres
spring.datasource.password=pactodb
spring.datasource.oamd.driverClassName=org.postgresql.Driver
spring.datasource.oamd.leak-detection-threshold=10000
spring.datasource.oamd.minimum-idle=1
spring.datasource.oamd.idle-timeout=10000

spring.datasource.hikari.connection-timeout=20000
spring.datasource.hikari.maximum-pool-size=5

logging.level.org.springframework.beans=DEBUG

spring.main.lazy-initialization=true
spring.mvc.pathmatch.matching-strategy=ant-path-matcher

mock.empresa.chave=pacto
mock.usuario.cod=2
mock.usuario.username=PACTOBR

mock.database.nome=bdzillyonteste
mock.database.host=localhost
mock.database.porta=5432
mock.database.senha=pactodb
mock.database.usuario=postgres