package com.pacto.relatorioms.entities;

import javax.persistence.*;
import java.util.Date;

@Entity
public class Convite {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer codigo;
    private Date dia;
    private Integer plano;
    @ManyToOne
    @JoinColumn(name = "convidou", foreignKey = @ForeignKey(name = "fk_convite_convidou"))
    private Cliente convidou;
    @ManyToOne
    @JoinColumn(name = "convidado", foreignKey = @ForeignKey(name = "fk_convite_convidado"))
    private Cliente convidado;

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Date getDia() {
        return dia;
    }

    public void setDia(Date dia) {
        this.dia = dia;
    }

    public Integer getPlano() {
        return plano;
    }

    public void setPlano(Integer plano) {
        this.plano = plano;
    }

    public Cliente getConvidou() {
        return convidou;
    }

    public void setConvidou(Cliente convidou) {
        this.convidou = convidou;
    }

    public Cliente getConvidado() {
        return convidado;
    }

    public void setConvidado(Cliente convidado) {
        this.convidado = convidado;
    }
}
