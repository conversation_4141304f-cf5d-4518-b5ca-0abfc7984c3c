package com.pacto.relatorioms.entities;

import javax.persistence.Entity;
import javax.persistence.ForeignKey;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;

@Entity

public class Armario {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer codigo;
    private String descricao;
    private String grupo;


    @ManyToOne
    @JoinColumn(name="tamanhoarmario", foreignKey = @ForeignKey(name = "fk_armario_tamanhoarmario"))
    private TamanhoArmario tamanhoArmario;

    public Armario(String descricao, String grupo, TamanhoArmario tamanhoArmario) {
        this.descricao = descricao;
        this.grupo = alterarGrupoTipo(grupo);
        this.tamanhoArmario = tamanhoArmario;
    }

    public Armario() {

    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public String getGrupo() {
        return grupo;
    }

    public void setGrupo(String grupo) {
        this.grupo = grupo;
    }

    public TamanhoArmario getTamanhoArmario() {
        return tamanhoArmario;
    }

    public void setTamanhoArmario(TamanhoArmario tamanhoArmario) {
        this.tamanhoArmario = tamanhoArmario;
    }

    public String alterarGrupoTipo(String grupo){
        if(grupo.equals("F")){
            grupo = "Feminino";
        }
        else if(grupo.equals("M")){
            grupo = "Masculino";
        }
        else if(grupo.equals("U")){
            grupo = "Unissex";
        }
        return grupo;
    }

}
