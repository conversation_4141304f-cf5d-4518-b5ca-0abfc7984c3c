package com.pacto.relatorioms.entities;

import javax.persistence.*;
import java.sql.Timestamp;
import java.util.Set;

@Entity
public class MovProduto {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer codigo;
    private String descricao;
    private String situacao;
    private Boolean quitado;
    private Timestamp dataInicioVigencia;
    private Timestamp dataFinalVigencia;
    private Timestamp dataLancamento;
    private String mesReferencia;
    private Integer anoReferencia;
    private Double totalFinal;
    private Double precoUnitario;
    private Double valorFaturado;
    private Double valorDesconto;
    private Integer quantidade;

    @ManyToOne
    @JoinColumn(name="produto", foreignKey = @ForeignKey(name = "fk_movproduto_produto"))
    private Produto produto;

    @OneToMany(mappedBy = "movProduto", cascade = {CascadeType.REFRESH})
    private Set<MovProdutoParcela> movProdutoParcelas;

    public MovProduto() {
    }

    public MovProduto(Produto produto, Timestamp dataFinalVigencia) {
        this.produto = produto;
        this.dataFinalVigencia = dataFinalVigencia;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Timestamp getDataFinalVigencia() {
        return dataFinalVigencia;
    }

    public void setDataFinalVigencia(Timestamp dataFinalVigencia) {
        this.dataFinalVigencia = dataFinalVigencia;
    }

    public Produto getProduto() {
        return produto;
    }

    public void setProduto(Produto produto) {
        this.produto = produto;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public Set<MovProdutoParcela> getMovProdutoParcelas() {
        return movProdutoParcelas;
    }

    public void setMovProdutoParcelas(Set<MovProdutoParcela> movProdutoParcela) {
        this.movProdutoParcelas = movProdutoParcela;
    }

    public String getSituacao() {
        return situacao;
    }

    public void setSituacao(String situacao) {
        this.situacao = situacao;
    }

    public Boolean getQuitado() {
        return quitado;
    }

    public void setQuitado(Boolean quitado) {
        this.quitado = quitado;
    }

    public Timestamp getDataInicioVigencia() {
        return dataInicioVigencia;
    }

    public void setDataInicioVigencia(Timestamp dataInicioVigencia) {
        this.dataInicioVigencia = dataInicioVigencia;
    }

    public Timestamp getDataLancamento() {
        return dataLancamento;
    }

    public void setDataLancamento(Timestamp dataLancamento) {
        this.dataLancamento = dataLancamento;
    }

    public String getMesReferencia() {
        return mesReferencia;
    }

    public void setMesReferencia(String mesReferencia) {
        this.mesReferencia = mesReferencia;
    }

    public Integer getAnoReferencia() {
        return anoReferencia;
    }

    public void setAnoReferencia(Integer anoReferencia) {
        this.anoReferencia = anoReferencia;
    }

    public Double getTotalFinal() {
        return totalFinal;
    }

    public void setTotalFinal(Double totalFinal) {
        this.totalFinal = totalFinal;
    }

    public Double getPrecoUnitario() {
        return precoUnitario;
    }

    public void setPrecoUnitario(Double precoUnitario) {
        this.precoUnitario = precoUnitario;
    }

    public Double getValorFaturado() {
        return valorFaturado;
    }

    public void setValorFaturado(Double valorFaturado) {
        this.valorFaturado = valorFaturado;
    }

    public Double getValorDesconto() {
        return valorDesconto;
    }

    public void setValorDesconto(Double valorDesconto) {
        this.valorDesconto = valorDesconto;
    }

    public Integer getQuantidade() {
        return quantidade;
    }

    public void setQuantidade(Integer quantidade) {
        this.quantidade = quantidade;
    }
}
