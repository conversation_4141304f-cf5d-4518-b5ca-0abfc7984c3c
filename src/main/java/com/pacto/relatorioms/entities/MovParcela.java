package com.pacto.relatorioms.entities;

import javax.persistence.Entity;
import javax.persistence.ForeignKey;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import java.sql.Timestamp;

@Entity
public class MovParcela {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer codigo;
    private String situacao;
    private String descricao;
    private Double valorParcela;
    private Timestamp dataVencimento;

    @ManyToOne
    @JoinColumn(name="vendaavulsa", foreignKey = @ForeignKey(name = "fk_movparcela_vendaavulsa"))
    private VendaAvulsa vendaavulsa;

    @ManyToOne
    @JoinColumn(name="personal", foreignKey = @ForeignKey(name = "fk_movparcela_controle"))
    private ControleTaxaPersonal personal;

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public VendaAvulsa getVendaavulsa() {
        return vendaavulsa;
    }

    public void setVendaavulsa(VendaAvulsa vendaavulsa) {
        this.vendaavulsa = vendaavulsa;
    }

    public String getSituacao() {
        return situacao;
    }

    public void setSituacao(String situacao) {
        this.situacao = situacao;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public Timestamp getDataVencimento() {
        return dataVencimento;
    }

    public void setDataVencimento(Timestamp datavencimento) {
        this.dataVencimento = datavencimento;
    }

    public Double getValorParcela() {
        return valorParcela;
    }

    public void setValorParcela(Double valorParcela) {
        this.valorParcela = valorParcela;
    }

    public ControleTaxaPersonal getPersonal() {
        return personal;
    }

    public void setPersonal(ControleTaxaPersonal personal) {
        this.personal = personal;
    }
}

