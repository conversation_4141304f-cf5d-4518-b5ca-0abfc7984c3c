package com.pacto.relatorioms.entities;

import com.pacto.config.annotations.NomeEntidadeLog;
import com.pacto.config.annotations.RelationalField;
import com.pacto.relatorioms.entities.empresa.Empresa;

import javax.persistence.*;
import java.util.Set;

@Entity
@NomeEntidadeLog("Cliente")
public class Cliente {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer codigo;
    private String situacao;
    private String matricula;
    private Integer codigoMatricula;

    @OneToMany(mappedBy = "cliente", fetch = FetchType.LAZY, cascade = {CascadeType.PERSIST, CascadeType.MERGE})
    private Set<AluguelArmario> aluguelArmarios;

    @OneToMany(mappedBy = "cliente", cascade = {CascadeType.MERGE, CascadeType.PERSIST, CascadeType.REFRESH}, orphanRemoval = true)
    private Set<Vinculo> vinculos;

    @OneToOne
    @RelationalField
    @JoinColumn(name = "pessoa", foreignKey = @ForeignKey(name = "fk_cliente_pessoa"))
    private Pessoa pessoa;

    @ManyToOne
    @RelationalField
    @JoinColumn(name = "empresa", foreignKey = @ForeignKey(name = "fk_cliente_empresa"))
    private Empresa empresa;

    @OneToMany(mappedBy = "cliente", cascade = {CascadeType.MERGE, CascadeType.PERSIST, CascadeType.REFRESH}, orphanRemoval = true)
    private Set<QuestionarioCliente> questionarioClientes;

    @ManyToOne
    @RelationalField
    @JoinColumn(name = "categoria", foreignKey = @ForeignKey(name = "fk_cliente_categoria"))
    private Categoria categoria;

    public Cliente() {

    }

    public Cliente(String matricula, Pessoa pessoa, Empresa empresa, String situacao) {
        this.matricula = matricula;
        this.pessoa = pessoa;
        this.empresa = empresa;
        this.situacao = situacao;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getMatricula() {
        return matricula;
    }

    public void setMatricula(String matricula) {
        this.matricula = matricula;
    }

    public Set<AluguelArmario> getAluguelArmarios() {
        if (aluguelArmarios == null) {
            aluguelArmarios = new java.util.HashSet<>();
        }
        return aluguelArmarios;
    }

    public void setAluguelArmarios(Set<AluguelArmario> aluguelArmarios) {
        this.aluguelArmarios = aluguelArmarios;
    }

    public Pessoa getPessoa() {
        return pessoa;
    }

    public void setPessoa(Pessoa pessoa) {
        this.pessoa = pessoa;
    }

    public String getSituacao() {
        return situacao;
    }

    public void setSituacao(String situacao) {
        this.situacao = situacao;
    }


    public Empresa getEmpresa() {
        return empresa;
    }

    public void setEmpresa(Empresa empresa) {
        this.empresa = empresa;
    }

    public Integer getCodigoMatricula() {
        return codigoMatricula;
    }

    public void setCodigoMatricula(Integer codigoMatricula) {
        this.codigoMatricula = codigoMatricula;
    }


    public String getSituacaoApresentar() {

        if (situacao == null) {
            return "";
        }
        if (situacao.equals("VI")) {
            return "Visitante";
        }
        if (situacao.equals("PL")) {
            return "Visitante - Free Pass";
        }
        if (situacao.equals("AA")) {
            return "Visitante - Aula Avulsa";
        }
        if (situacao.equals("DI")) {
            return "Visitante - Diária";
        }
        if (situacao.equals("IN")) {
            return "Inativo";
        }
        if (situacao.equals("CA")) {
            return "Inativo - Cancelado";
        }
        if (situacao.equals("DE")) {
            return "Inativo - Desistente";
        }
        if (situacao.equals("AT")) {
            return "Ativo";
        }
        if (situacao.equals("NO")) {
            return "Ativo - Normai";
        }
        if (situacao.equals("TR")) {
            return "Trancado";
        }
        if (situacao.equals("TV")) {
            return "Trancado Vencido";
        }
        if (situacao.equals("AV")) {
            return "Ativo - A Vencer";
        }
        if (situacao.equals("VE")) {
            return "Inativo - Vencido";
        }
        if (situacao.equals("CR")) {
            return "Ativo - Férias";
        }
        if (situacao.equals("AE")) {
            return "Ativo - Atestado";
        }
        return situacao;
    }

    public Categoria getCategoria() {
        return categoria;
    }

    public void setCategoria(Categoria categoria) {
        this.categoria = categoria;
    }
}
