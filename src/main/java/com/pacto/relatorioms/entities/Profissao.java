package com.pacto.relatorioms.entities;

import com.pacto.config.annotations.NomeEntidadeLog;
import com.pacto.config.annotations.NotLogged;

import javax.persistence.*;

@Entity
@NomeEntidadeLog("Profissao")
public class Profissao {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    protected Integer codigo;
    protected String descricao;

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }
}
