package com.pacto.relatorioms.entities;

import com.pacto.config.annotations.NomeEntidadeLog;
import com.pacto.config.annotations.RelationalField;

import javax.persistence.*;
import java.sql.Date;

@Entity
@NomeEntidadeLog("QuestionarioCliente")
public class QuestionarioCliente {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer codigo;
    private Date data;
    private Date ultimaAtualizacao;
    private String observacao;
    private Integer evento;
    private Integer tipoBv;
    @ManyToOne
    @RelationalField
    @JoinColumn(name = "consultor", foreignKey = @ForeignKey(name = "fk_questionariocliente_consultor"))
    private Colaborador consultor;
    @ManyToOne
    @RelationalField
    @JoinColumn(name = "cliente", foreignKey = @ForeignKey(name = "fk_questionariocliente_cliente"))
    private Cliente cliente;

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Date getData() {
        return data;
    }

    public void setData(Date data) {
        this.data = data;
    }

    public Date getUltimaAtualizacao() {
        return ultimaAtualizacao;
    }

    public void setUltimaAtualizacao(Date ultimaAtualizacao) {
        this.ultimaAtualizacao = ultimaAtualizacao;
    }

    public String getObservacao() {
        return observacao;
    }

    public void setObservacao(String observacao) {
        this.observacao = observacao;
    }

    public Integer getEvento() {
        return evento;
    }

    public void setEvento(Integer evento) {
        this.evento = evento;
    }

    public Integer getTipoBv() {
        return tipoBv;
    }

    public void setTipoBv(Integer tipovc) {
        this.tipoBv = tipovc;
    }

    public Colaborador getConsultor() {
        return consultor;
    }

    public void setConsultor(Colaborador consultor) {
        this.consultor = consultor;
    }

    public Cliente getCliente() {
        return cliente;
    }

    public void setCliente(Cliente cliente) {
        this.cliente = cliente;
    }
}
