package com.pacto.relatorioms.entities;

import com.pacto.config.annotations.NomeEntidadeLog;
import com.pacto.config.annotations.NotLogged;
import com.pacto.relatorioms.entities.empresa.Empresa;

import javax.persistence.*;

@Entity
@NomeEntidadeLog("Endereco")
public class Endereco {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer codigo;
    private String endereco;
    private String complemento;
    private String numero;
    private String bairro;
    private String cep;
    @ManyToOne(cascade = {CascadeType.REFRESH})
    @JoinColumn(name = "pessoa", foreignKey = @ForeignKey(name = "fk_endereco_pessoa"))
    private Pessoa pessoa;

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getEndereco() {
        return endereco;
    }

    public void setEndereco(String endereco) {
        this.endereco = endereco;
    }

    public String getComplemento() {
        return complemento;
    }

    public void setComplemento(String complemento) {
        this.complemento = complemento;
    }

    public String getNumero() {
        return numero;
    }

    public void setNumero(String numero) {
        this.numero = numero;
    }

    public String getBairro() {
        return bairro;
    }

    public void setBairro(String bairro) {
        this.bairro = bairro;
    }

    public String getCep() {
        return cep;
    }

    public void setCep(String cep) {
        this.cep = cep;
    }

    public Pessoa getPessoa() {
        return pessoa;
    }

    public void setPessoa(Pessoa pessoa) {
        this.pessoa = pessoa;
    }
}
