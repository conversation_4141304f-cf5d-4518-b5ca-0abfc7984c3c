package com.pacto.relatorioms.entities;

import javax.persistence.*;

@Entity
public class MovProdutoParcela {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer codigo;
    private Double valorPago;
    private Integer reciboPagamento;
    private Integer movParcelaOriginalMultaJuros;

    @ManyToOne
    @JoinColumn(name="movParcela", foreignKey = @ForeignKey(name = "fk_movprodutoparcela_movparcela"))
    private MovParcela movParcela;

    @ManyToOne
    @JoinColumn(name="movProduto", foreignKey = @ForeignKey(name = "fk_movprodutoparcela_movproduto"))
    private MovProduto movProduto;

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Double getValorPago() {
        return valorPago;
    }

    public void setValorPago(Double valorPago) {
        this.valorPago = valorPago;
    }

    public Integer getReciboPagamento() {
        return reciboPagamento;
    }

    public void setReciboPagamento(Integer reciboPagamento) {
        this.reciboPagamento = reciboPagamento;
    }

    public Integer getMovParcelaOriginalMultaJuros() {
        return movParcelaOriginalMultaJuros;
    }

    public void setMovParcelaOriginalMultaJuros(Integer movParcelaOriginalMultaJuros) {
        this.movParcelaOriginalMultaJuros = movParcelaOriginalMultaJuros;
    }

    public MovParcela getMovParcela() {
        return movParcela;
    }

    public void setMovParcela(MovParcela movParcela) {
        this.movParcela = movParcela;
    }

    public MovProduto getMovProduto() {
        return movProduto;
    }

    public void setMovProduto(MovProduto movProduto) {
        this.movProduto = movProduto;
    }
}
