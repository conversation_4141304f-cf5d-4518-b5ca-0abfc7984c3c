package com.pacto.relatorioms.entities;

import com.pacto.config.annotations.RelationalField;
import com.pacto.relatorioms.entities.empresa.Empresa;

import javax.persistence.*;
import java.util.Date;

@Entity
public class LocalAcesso {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer codigo;
    private String descricao;
    @ManyToOne
    @RelationalField
    @JoinColumn(name = "empresa", foreignKey = @ForeignKey(ConstraintMode.NO_CONSTRAINT))
    private Empresa empresa;

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public Empresa getEmpresa() {
        return empresa;
    }

    public void setEmpresa(Empresa empresa) {
        this.empresa = empresa;
    }
}
