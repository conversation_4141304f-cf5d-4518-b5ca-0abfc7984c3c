package com.pacto.relatorioms.entities;

import com.pacto.config.annotations.NomeEntidadeLog;
import com.pacto.config.annotations.RelationalField;
import com.pacto.relatorioms.entities.empresa.Empresa;

import javax.persistence.*;
import java.util.Date;
import java.util.Set;

@Entity
@NomeEntidadeLog("ClienteMensagem")
public class ClienteMensagem {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer codigo;
    private String tipoMensagem;
    @ManyToOne
    @RelationalField
    @JoinColumn(name = "cliente", foreignKey = @ForeignKey(name = "fk_clientemensagem_cliente"))
    private Cliente cliente;

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getTipoMensagem() {
        return tipoMensagem;
    }

    public void setTipoMensagem(String tipoMensagem) {
        this.tipoMensagem = tipoMensagem;
    }

    public Cliente getCliente() {
        return cliente;
    }

    public void setCliente(Cliente cliente) {
        this.cliente = cliente;
    }
}
