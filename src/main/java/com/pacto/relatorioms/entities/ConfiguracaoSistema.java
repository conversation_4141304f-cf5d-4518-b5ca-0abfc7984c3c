package com.pacto.relatorioms.entities;

import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;

@Entity
public class ConfiguracaoSistema {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer codigo;
    private Boolean habilitarGestaoArmarios;
    private Integer vencimentoColaborador;


    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Boolean getHabilitarGestaoArmarios() {
        return habilitarGestaoArmarios;
    }

    public void setHabilitarGestaoArmarios(Boolean habilitarGestaoArmarios) {
        this.habilitarGestaoArmarios = habilitarGestaoArmarios;
    }

    public Integer getVencimentoColaborador() {
        return vencimentoColaborador;
    }

    public void setVencimentoColaborador(Integer vencimentoColaborador) {
        this.vencimentoColaborador = vencimentoColaborador;
    }
}
