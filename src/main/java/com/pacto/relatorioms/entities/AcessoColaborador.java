package com.pacto.relatorioms.entities;

import com.pacto.config.annotations.RelationalField;
import org.hibernate.annotations.NotFound;
import org.hibernate.annotations.NotFoundAction;

import javax.persistence.*;
import java.util.Date;

@Entity
public class AcessoColaborador {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer codigo;
    private String sentido;
    private Date dtHrEntrada;
    private Date dtHrSaida;
    private Integer meioIdentificacaoEntrada;
    private Integer meioIdentificacaoSaida;

    @RelationalField
    @ManyToOne
    @JoinColumn(name = "colaborador", foreignKey = @ForeignKey(ConstraintMode.NO_CONSTRAINT))
    @NotFound(action = NotFoundAction.IGNORE)
    private Colaborador colaborador;

    @RelationalField
    @ManyToOne
    @JoinColumn(name = "localAcesso", foreignKey = @ForeignKey(ConstraintMode.NO_CONSTRAINT))
    @NotFound(action = NotFoundAction.IGNORE)
    private LocalAcesso localAcesso;

    @RelationalField
    @ManyToOne
    @JoinColumn(name = "coletor", foreignKey = @ForeignKey(ConstraintMode.NO_CONSTRAINT))
    @NotFound(action = NotFoundAction.IGNORE)
    private Coletor coletor;

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }


    public String getSentido() {
        return sentido;
    }

    public void setSentido(String sentido) {
        this.sentido = sentido;
    }

    public Colaborador getColaborador() {
        return colaborador;
    }

    public void setColaborador(Colaborador colaborador) {
        this.colaborador = colaborador;
    }

    public LocalAcesso getLocalAcesso() {
        return localAcesso;
    }

    public void setLocalAcesso(LocalAcesso localAcesso) {
        this.localAcesso = localAcesso;
    }

    public Coletor getColetor() {
        return coletor;
    }

    public void setColetor(Coletor coletor) {
        this.coletor = coletor;
    }

    public Date getDtHrEntrada() {
        return dtHrEntrada;
    }

    public void setDtHrEntrada(Date dtHrEntrada) {
        this.dtHrEntrada = dtHrEntrada;
    }

    public Date getDtHrSaida() {
        return dtHrSaida;
    }

    public void setDtHrSaida(Date dtHrSaida) {
        this.dtHrSaida = dtHrSaida;
    }

    public Integer getMeioIdentificacaoEntrada() {
        return meioIdentificacaoEntrada;
    }

    public void setMeioIdentificacaoEntrada(Integer meioIdentificacaoEntrada) {
        this.meioIdentificacaoEntrada = meioIdentificacaoEntrada;
    }

    public Integer getMeioIdentificacaoSaida() {
        return meioIdentificacaoSaida;
    }

    public void setMeioIdentificacaoSaida(Integer meioIdentificacaoSaida) {
        this.meioIdentificacaoSaida = meioIdentificacaoSaida;
    }
}
