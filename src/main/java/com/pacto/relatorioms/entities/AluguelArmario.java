package com.pacto.relatorioms.entities;

import javax.persistence.*;
import java.time.LocalDate;
import java.util.Date;

@Entity
public class AluguelArmario {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer codigo;
    @Temporal(TemporalType.TIMESTAMP)
    private Date dataCadastro;
    @Temporal(TemporalType.TIMESTAMP)
    private Date dataInicio;
    @Temporal(TemporalType.TIMESTAMP)
    private Date fimOriginal;
    private Boolean contratoAssinado;
    private Integer relacionamentoRenovacao;

    @ManyToOne
    @JoinColumn(name="cliente", foreignKey = @ForeignKey(name = "fk_aluguelarmario_cliente"))
    private Cliente cliente;

    @ManyToOne
    @JoinColumn(name="movproduto", foreignKey = @ForeignKey(name = "fk_aluguelarmario_movproduto"))
    private MovProduto movproduto;

    @ManyToOne
    @JoinColumn(name="armario", foreignKey = @ForeignKey(name = "fk_aluguelarmario_armario"))
    private Armario armario;
    @ManyToOne
    @JoinColumn(name="vendaavulsa", foreignKey = @ForeignKey(name = "fk_armario_vendaavulsa"))
    private VendaAvulsa vendaavulsa;

    public AluguelArmario(
            Cliente cliente, Integer relacionamentoRenovacao , Date dataCadastro, Date dataInicio,
            Armario armario, MovProduto movproduto, Date fimOriginal,
            Boolean contratoAssinado
    ) {
        this.cliente = cliente;
        this.relacionamentoRenovacao = relacionamentoRenovacao;
        this.dataCadastro = dataCadastro;
        this.dataInicio = dataInicio;
        this.armario = armario;
        this.movproduto = movproduto;
        this.fimOriginal = fimOriginal;
        this.contratoAssinado = contratoAssinado;
    }

    public AluguelArmario() {
    }

    public Integer getRelacionamentoRenovacao() {
        return relacionamentoRenovacao;
    }

    public void setRelacionamentoRenovacao(Integer relacionamentoRenovacao) {
        this.relacionamentoRenovacao = relacionamentoRenovacao;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Date getDataCadastro() {
        return dataCadastro;
    }

    public void setDataCadastro(Date dataCadastro) {
        this.dataCadastro = dataCadastro;
    }

    public Date getDataInicio() {
        return dataInicio;
    }

    public void setDataInicio(Date dataInicio) {
        this.dataInicio = dataInicio;
    }

    public Date getFimOriginal() {
        return fimOriginal;
    }

    public void setFimOriginal(Date fimOriginal) {
        this.fimOriginal = fimOriginal;
    }

    public Boolean getContratoAssinado() {
        return contratoAssinado;
    }

    public void setContratoAssinado(Boolean contratoAssinado) {
        this.contratoAssinado = contratoAssinado;
    }

    public Cliente getCliente() {
        return cliente;
    }

    public void setCliente(Cliente cliente) {
        this.cliente = cliente;
    }

    public MovProduto getMovproduto() {
        return movproduto;
    }

    public void setMovproduto(MovProduto movproduto) {
        this.movproduto = movproduto;
    }

    public Armario getArmario() {
        return armario;
    }

    public void setArmario(Armario armario) {
        this.armario = armario;
    }

    public VendaAvulsa getVendaavulsa() {
        return vendaavulsa;
    }

    public void setVendaavulsa(VendaAvulsa vendaavulsa) {
        this.vendaavulsa = vendaavulsa;
    }
}
