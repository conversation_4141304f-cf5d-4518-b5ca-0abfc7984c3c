package com.pacto.relatorioms.entities;

import com.pacto.config.annotations.NotLogged;

import javax.persistence.*;
import java.util.Date;

@Entity

public class PeriodoAcessoCliente {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer codigo;
    @ManyToOne
    @JoinColumn(name = "pessoa", foreignKey = @ForeignKey(name = "fk_periodoacessocliente_pessoa"))
    private Pessoa pessoa;
    private Integer contrato;
    private Integer aulaAvulsaDiaria;
    private Integer contratoBaseadoRenovacao;
    private Date dataInicioAcesso;
    private Date dataFinalAcesso;
    private String tipoAcesso;
    private Integer responsavel;
    private Date dataLancamento;
    private String tokenGymPass;
    private String tokenGogood;
    private String tipoGymPass;
    private Integer reposicao;
    private Double valorGympass;

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Pessoa getPessoa() {
        return pessoa;
    }

    public void setPessoa(Pessoa pessoa) {
        this.pessoa = pessoa;
    }

    public Integer getContrato() {
        return contrato;
    }

    public void setContrato(Integer contrato) {
        this.contrato = contrato;
    }

    public Integer getAulaAvulsaDiaria() {
        return aulaAvulsaDiaria;
    }

    public void setAulaAvulsaDiaria(Integer aulaAvulsaDiaria) {
        this.aulaAvulsaDiaria = aulaAvulsaDiaria;
    }

    public Integer getContratoBaseadoRenovacao() {
        return contratoBaseadoRenovacao;
    }

    public void setContratoBaseadoRenovacao(Integer contratoBaseadoRenovacao) {
        this.contratoBaseadoRenovacao = contratoBaseadoRenovacao;
    }

    public Date getDataInicioAcesso() {
        return dataInicioAcesso;
    }

    public void setDataInicioAcesso(Date dataInicioAcesso) {
        this.dataInicioAcesso = dataInicioAcesso;
    }

    public Date getDataFinalAcesso() {
        return dataFinalAcesso;
    }

    public void setDataFinalAcesso(Date dataFinalAcesso) {
        this.dataFinalAcesso = dataFinalAcesso;
    }

    public String getTipoAcesso() {
        return tipoAcesso;
    }

    public void setTipoAcesso(String tipoAcesso) {
        this.tipoAcesso = tipoAcesso;
    }

    public Integer getResponsavel() {
        return responsavel;
    }

    public void setResponsavel(Integer responsavel) {
        this.responsavel = responsavel;
    }

    public Date getDataLancamento() {
        return dataLancamento;
    }

    public void setDataLancamento(Date dataLancamento) {
        this.dataLancamento = dataLancamento;
    }

    public String getTokenGymPass() {
        return tokenGymPass;
    }

    public void setTokenGymPass(String tokenGymPass) {
        this.tokenGymPass = tokenGymPass;
    }

    public String getTipoGymPass() {
        return tipoGymPass;
    }

    public void setTipoGymPass(String tipoGymPass) {
        this.tipoGymPass = tipoGymPass;
    }

    public Integer getReposicao() {
        return reposicao;
    }

    public void setReposicao(Integer reposicao) {
        this.reposicao = reposicao;
    }

    public Double getValorGympass() {
        return valorGympass;
    }

    public void setValorGympass(Double valorGympass) {
        this.valorGympass = valorGympass;
    }

    public String getTokenGogood() {
        return tokenGogood;
    }

    public void setTokenGogood(String tokenGogood) {
        this.tokenGogood = tokenGogood;
    }
}
