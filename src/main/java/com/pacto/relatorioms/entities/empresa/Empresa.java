package com.pacto.relatorioms.entities.empresa;

import com.pacto.config.annotations.NomeEntidadeLog;
import com.pacto.config.annotations.RelationalField;
import com.pacto.config.annotations.RelationalField;
import com.pacto.config.annotations.UseOnlyThisToLog;
import org.hibernate.annotations.NotFound;
import org.hibernate.annotations.NotFoundAction;
import com.pacto.relatorioms.entities.Telefone;
import org.hibernate.annotations.NotFound;
import org.hibernate.annotations.NotFoundAction;


import javax.persistence.*;
import java.util.Set;

@Entity
@NomeEntidadeLog("Empresa")
public class Empresa {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer codigo;
    @UseOnlyThisToLog
    private String nome;
    private boolean ativa;
    private String moeda;
    private String cnpj;
    private String telComercial1;
    private String telComercial2;
    private String telComercial3;
    private String endereco;
    private String numero;
    private String complemento;
    private String setor;
    private String cep;
    private String site;

    @RelationalField
    @ManyToOne
    @JoinColumn(name = "estado", foreignKey = @ForeignKey(ConstraintMode.NO_CONSTRAINT))
    @NotFound(action = NotFoundAction.IGNORE)
    private Estado estado;

    @RelationalField
    @ManyToOne
    @JoinColumn(name = "cidade", foreignKey = @ForeignKey(ConstraintMode.NO_CONSTRAINT))
    @NotFound(action = NotFoundAction.IGNORE)
    private Cidade cidade;

    public Empresa() {
    }

    public Empresa(Integer codigo){
        this.codigo = codigo;
    }

    public Empresa(String nome) {
        this.nome = nome;
    }

    public Empresa(Integer codigo, String nome, boolean ativa) {
        this.codigo = codigo;
        this.nome = nome;
        this.ativa = ativa;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public boolean isAtiva() {
        return ativa;
    }

    public void setAtiva(boolean ativa) {
        this.ativa = ativa;
    }

    public String getMoeda() {
        return moeda;
    }

    public void setMoeda(String moeda) {
        this.moeda = moeda;
    }

    public String getCnpj() {
        return cnpj;
    }

    public void setCnpj(String cnpj) {
        this.cnpj = cnpj;
    }

    public String getTelComercial1() {
        return telComercial1;
    }

    public void setTelComercial1(String telComercial1) {
        this.telComercial1 = telComercial1;
    }

    public String getTelComercial2() {
        return telComercial2;
    }

    public void setTelComercial2(String telComercial2) {
        this.telComercial2 = telComercial2;
    }

    public String getTelComercial3() {
        return telComercial3;
    }

    public void setTelComercial3(String telComercial3) {
        this.telComercial3 = telComercial3;
    }

    public String getEndereco() {
        return endereco;
    }

    public void setEndereco(String endereco) {
        this.endereco = endereco;
    }

    public String getNumero() {
        return numero;
    }

    public void setNumero(String numero) {
        this.numero = numero;
    }

    public String getComplemento() {
        return complemento;
    }

    public void setComplemento(String complemento) {
        this.complemento = complemento;
    }

    public String getSetor() {
        return setor;
    }

    public void setSetor(String setor) {
        this.setor = setor;
    }

    public String getCep() {
        return cep;
    }

    public void setCep(String cep) {
        this.cep = cep;
    }

    public String getSite() {
        return site;
    }

    public void setSite(String site) {
        this.site = site;
    }

    public Estado getEstado() {
        return estado;
    }

    public void setEstado(Estado estado) {
        this.estado = estado;
    }

    public Cidade getCidade() {
        return cidade;
    }

    public void setCidade(Cidade cidade) {
        this.cidade = cidade;
    }
}
