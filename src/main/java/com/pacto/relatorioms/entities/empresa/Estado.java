package com.pacto.relatorioms.entities.empresa;

import com.pacto.config.annotations.NomeEntidadeLog;
import com.pacto.config.annotations.NotLogged;
import com.pacto.config.annotations.UseOnlyThisToLog;
import com.pacto.relatorioms.entities.Pessoa;

import javax.persistence.*;

@Entity
@NomeEntidadeLog("Estado")
public class Estado {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer codigo;
    @UseOnlyThisToLog
    private String nome;
    private String sigla;
    @NotLogged
    @ManyToOne(cascade = {CascadeType.REFRESH})
    @JoinColumn(name = "pais", foreignKey = @ForeignKey(name = "fk_estado_pais"))
    private Pais pais;

    public Estado() {
    }

    public Estado(String sigla) {
        this.sigla = sigla;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public String getSigla() {
        return sigla;
    }

    public void setSigla(String sigla) {
        this.sigla = sigla;
    }

    public Pais getPais() {
        return pais;
    }

    public void setPais(Pais pais) {
        this.pais = pais;
    }
}
