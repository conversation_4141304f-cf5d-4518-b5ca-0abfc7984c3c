package com.pacto.relatorioms.entities;

import javax.persistence.*;
import java.util.Date;

@Entity
public class HistoricoVinculo {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer codigo;
    private Date dataRegistro;
    private String tipoHistoricoVinculo;
    private String tipoColaborador;
    private String origem;

    @ManyToOne
    @JoinColumn(name = "cliente", foreignKey = @ForeignKey(name = "fk_historicovinculo_cliente"))
    private Cliente cliente;

    @ManyToOne
    @JoinColumn(name = "colaborador", foreignKey = @ForeignKey(name = "fk_historicovinculo_colaborador"))
    private Colaborador colaborador;

    @ManyToOne
    @JoinColumn(name = "usuarioResponsavel", foreignKey = @ForeignKey(name = "fk_usuarioresponsavel_usuario"))
    private Usuario usuarioResponsavel;

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Date getDataRegistro() {
        return dataRegistro;
    }

    public void setDataRegistro(Date dataRegistro) {
        this.dataRegistro = dataRegistro;
    }

    public String getTipoHistoricoVinculo() {
        return tipoHistoricoVinculo;
    }

    public void setTipoHistoricoVinculo(String tipoHistoricoVinculo) {
        this.tipoHistoricoVinculo = tipoHistoricoVinculo;
    }

    public String getTipoColaborador() {
        return tipoColaborador;
    }

    public void setTipoColaborador(String tipoColaborador) {
        this.tipoColaborador = tipoColaborador;
    }

    public String getOrigem() {
        return origem;
    }

    public void setOrigem(String origem) {
        this.origem = origem;
    }

    public Cliente getCliente() {
        return cliente;
    }

    public void setCliente(Cliente cliente) {
        this.cliente = cliente;
    }

    public Colaborador getColaborador() {
        return colaborador;
    }

    public void setColaborador(Colaborador colaborador) {
        this.colaborador = colaborador;
    }

    public Usuario getUsuarioResponsavel() {
        return usuarioResponsavel;
    }

    public void setUsuarioResponsavel(Usuario usuarioResponsavel) {
        this.usuarioResponsavel = usuarioResponsavel;
    }
}
