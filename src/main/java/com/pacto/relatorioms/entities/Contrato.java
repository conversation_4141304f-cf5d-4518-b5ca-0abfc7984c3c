package com.pacto.relatorioms.entities;

import javax.persistence.Entity;
import javax.persistence.ForeignKey;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import java.sql.Timestamp;

@Entity
public class Contrato {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer codigo;

    private Timestamp vigenciaDe;
    private Timestamp vigenciaAteAjustada;

    @ManyToOne
    @JoinColumn(name="plano" , foreignKey = @ForeignKey(name = "fk_contrato_plano"))
    private Plano plano;

    @ManyToOne
    @JoinColumn(name="pessoa" , foreignKey = @ForeignKey(name = "fk_contrato_pessoa"))
    private Pessoa pessoa;

    public Contrato() {
    }

    public Contrato(Plano plano,Timestamp vigenciaDe, Timestamp vigenciaAteAjustada)  {
        this.plano = plano;
        this.vigenciaDe = vigenciaDe;
        this.vigenciaAteAjustada = vigenciaAteAjustada;

    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Timestamp getVigenciaDe() {
        return vigenciaDe;
    }

    public void setVigenciaDe(Timestamp vigenciaDe) {
        this.vigenciaDe = vigenciaDe;
    }

    public Timestamp getVigenciaAteAjustada() {
        return vigenciaAteAjustada;
    }

    public void setVigenciaAteAjustada(Timestamp vigenciaAteAjustada) {
        this.vigenciaAteAjustada = vigenciaAteAjustada;
    }

    public Plano getPlano() {
        return plano;
    }

    public void setPlano(Plano plano) {
        this.plano = plano;
    }

    public Pessoa getPessoa() {
        return pessoa;
    }

    public void setPessoa(Pessoa pessoa) {
        this.pessoa = pessoa;
    }
}
