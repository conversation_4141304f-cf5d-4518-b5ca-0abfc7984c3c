package com.pacto.relatorioms.entities;

import com.pacto.config.annotations.RelationalField;
import com.pacto.relatorioms.entities.empresa.Empresa;

import javax.persistence.ConstraintMode;
import javax.persistence.Entity;
import javax.persistence.ForeignKey;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import java.util.Date;

@Entity
public class SituacaoClienteSinteticodw {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer codigo;
    private Integer codigoContrato;
    private Integer codigoPessoa;
    private Integer codigoCliente;
    private Integer saldoCreditoTreino;
    private String situacao;
    private Integer matricula;

    //REFATORAR
    private String nomeCliente;
    private Date dataVigenciaAte;
    private String telefonesCliente;

    @ManyToOne
    @RelationalField
    @JoinColumn(name = "empresacliente", foreignKey = @ForeignKey(ConstraintMode.NO_CONSTRAINT))
    private Empresa empresa;

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Integer getCodigoContrato() {
        return codigoContrato;
    }

    public void setCodigoContrato(Integer codigoContrato) {
        this.codigoContrato = codigoContrato;
    }

    public Integer getCodigoPessoa() {
        return codigoPessoa;
    }

    public void setCodigoPessoa(Integer codigoPessoa) {
        this.codigoPessoa = codigoPessoa;
    }

    public Integer getCodigoCliente() {
        return codigoCliente;
    }

    public void setCodigoCliente(Integer codigoCliente) {
        this.codigoCliente = codigoCliente;
    }

    public Integer getSaldoCreditoTreino() { return saldoCreditoTreino; }

    public void setSaldoCreditoTreino(Integer saldoCreditoTreino) { this.saldoCreditoTreino = saldoCreditoTreino; }

    public String getSituacao() { return situacao; }

    public void setSituacao(String situacao) { this.situacao = situacao;  }

    public Integer getMatricula() { return matricula;  }

    public void setMatricula(Integer matricula) { this.matricula = matricula;  }


    public String getNomeCliente() {
        return nomeCliente;
    }

    public void setNomeCliente(String nomeCliente) {
        this.nomeCliente = nomeCliente;
    }

    public Date getDataVigenciaAte() { return dataVigenciaAte;   }

    public void setDataVigenciaAte(Date dataVigenciaAte) { this.dataVigenciaAte = dataVigenciaAte;  }

    public String getTelefonesCliente() {
        return telefonesCliente;
    }

    public void setTelefonesCliente(String telefonesCliente) {
        this.telefonesCliente = telefonesCliente;
    }

    public Empresa getEmpresa() {
        return empresa;
    }

    public void setEmpresa(Empresa empresa) {
        this.empresa = empresa;
    }


}
