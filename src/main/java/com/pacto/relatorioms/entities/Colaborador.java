package com.pacto.relatorioms.entities;

import com.pacto.config.annotations.NomeEntidadeLog;
import com.pacto.config.annotations.RelationalField;
import com.pacto.relatorioms.entities.empresa.Empresa;

import javax.persistence.*;

@Entity
@NomeEntidadeLog("Colaborador")
public class Colaborador {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer codigo;
    private String situacao;
    private Integer diaVencimento;

    @ManyToOne
    @RelationalField
    @JoinColumn(name = "empresa", foreignKey = @ForeignKey(name = "fk_colaborador_empresa"))
    private Empresa empresa;

    @OneToOne
    @RelationalField
    @JoinColumn(name = "pessoa", foreignKey = @ForeignKey(name = "fk_colaborador_pessoa"))
    private Pessoa pessoa;

    public Colaborador() {
    }

    public Colaborador(Empresa empresa) {
        this.empresa = empresa;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getSituacao() {
        return situacao;
    }

    public void setSituacao(String situacao) {
        this.situacao = situacao;
    }

    public Empresa getEmpresa() {
        return empresa;
    }

    public void setEmpresa(Empresa empresa) {
        this.empresa = empresa;
    }

    public Pessoa getPessoa() {
        return pessoa;
    }

    public void setPessoa(Pessoa pessoa) {
        this.pessoa = pessoa;
    }

    public Integer getDiaVencimento() {
        return diaVencimento;
    }

    public void setDiaVencimento(Integer diaVencimento) {
        this.diaVencimento = diaVencimento;
    }
}
