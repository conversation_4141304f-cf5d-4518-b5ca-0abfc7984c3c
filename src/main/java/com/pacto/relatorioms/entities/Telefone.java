package com.pacto.relatorioms.entities;

import com.pacto.config.annotations.NomeEntidadeLog;
import com.pacto.config.annotations.NotLogged;

import javax.persistence.*;

@Entity
@NomeEntidadeLog("Telefone")
public class Telefone {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer codigo;
    private String numero;
    private String tipoTelefone;
    private String descricao;

    @ManyToOne(cascade = {CascadeType.REFRESH})
    @JoinColumn(name="pessoa", foreignKey = @ForeignKey(name = "fk_telefone_pessoa"))
    private Pessoa pessoa;

    public Telefone(String numero) {
        this.numero = numero;
    }

    public Telefone() {
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getNumero() {
        return numero;
    }

    public void setNumero(String numero) {
        this.numero = numero;
    }

    public String getTipoTelefone() {
        return tipoTelefone;
    }

    public void setTipoTelefone(String tipoTelefone) {
        this.tipoTelefone = tipoTelefone;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public Pessoa getPessoa() {
        return pessoa;
    }

    public void setPessoa(Pessoa pessoa) {
        this.pessoa = pessoa;
    }
}
