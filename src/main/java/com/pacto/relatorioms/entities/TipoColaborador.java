package com.pacto.relatorioms.entities;

import com.pacto.config.annotations.NomeEntidadeLog;

import javax.persistence.*;

@Entity
@NomeEntidadeLog("TipoColaborador")
public class TipoColaborador {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer codigo;
    private String descricao;
    @ManyToOne(cascade = {CascadeType.REFRESH})
    @JoinColumn(name = "colaborador", foreignKey = @ForeignKey(name = "fk_tipocolaborador_colaborador"))
    private Colaborador colaborador;

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public Colaborador getColaborador() {
        return colaborador;
    }

    public void setColaborador(Colaborador colaborador) {
        this.colaborador = colaborador;
    }
}
