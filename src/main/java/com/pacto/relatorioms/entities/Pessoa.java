package com.pacto.relatorioms.entities;
import com.pacto.config.annotations.NomeEntidadeLog;
import com.pacto.config.annotations.RelationalField;
import com.pacto.relatorioms.entities.empresa.Cidade;
import com.pacto.relatorioms.entities.empresa.Estado;
import com.pacto.relatorioms.entities.empresa.Pais;

import javax.persistence.*;
import java.sql.Timestamp;
import java.util.*;

@Entity
@NomeEntidadeLog("Pessoa")
public class Pessoa {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer codigo = 0;
    private String nome = "";
    private String cfp = "";
    private String rg = "";
    private String fotokey = "";
    @Temporal(TemporalType.TIMESTAMP)
    private Date dataNasc;
    private String sexo = "";
    @Temporal(TemporalType.TIMESTAMP)
    private Date dataCadastro;
    private String estadoCivil = "";

    @OneToOne(mappedBy = "pessoa")
    private Cliente cliente;

    @OneToMany(mappedBy = "pessoa", cascade = {CascadeType.MERGE, CascadeType.PERSIST, CascadeType.REFRESH}, orphanRemoval = true)
    private Set<Email> emails;

    @OneToMany(mappedBy = "pessoa", cascade = {CascadeType.MERGE, CascadeType.PERSIST, CascadeType.REFRESH}, orphanRemoval = true)
    private Set<Telefone> telefones;

    @ManyToOne
    @RelationalField
    @JoinColumn(name = "estado", foreignKey = @ForeignKey(name = "fk_pessoa_estado"))
    private Estado estado;

    @ManyToOne
    @RelationalField
    @JoinColumn(name = "cidade", foreignKey = @ForeignKey(name = "fk_pessoa_cidade"))
    private Cidade cidade;

    @ManyToOne
    @RelationalField
    @JoinColumn(name = "pais", foreignKey = @ForeignKey(name = "fk_pessoa_pais"))
    private Pais pais;

    @OneToMany(mappedBy = "pessoa", cascade = {CascadeType.MERGE, CascadeType.PERSIST, CascadeType.REFRESH}, orphanRemoval = true)
    private Set<Endereco> enderecos;

    @ManyToOne
    @RelationalField
    @JoinColumn(name = "profissao", foreignKey = @ForeignKey(name = "fk_pessoa_profissao"))
    private Profissao profissao;

    public Pessoa() {
    }

    public Pessoa(String nome, Timestamp dataNasc, Email email , Telefone telefone) {
        this.nome = nome;
        this.dataNasc = dataNasc;
        getEmails().add(email);
        getTelefones().add(telefone);
    }



    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public Date getDataNasc() {
        return dataNasc;
    }

    public void setDataNasc(Timestamp dataNasc) {
        this.dataNasc = dataNasc;
    }

    public String getSexo() {
        return sexo;
    }

    public void setSexo(String sexo) {
        this.sexo = sexo;
    }

    public Date getDataCadastro() {
        return dataCadastro;
    }

    public void setDataCadastro(Date dataCadastro) {
        this.dataCadastro = dataCadastro;
    }

    public String getEstadoCivil() {
        return estadoCivil;
    }

    public void setEstadoCivil(String estadoCivil) {
        this.estadoCivil = estadoCivil;
    }

    public Cliente getCliente() {
        return cliente;
    }

    public void setCliente(Cliente cliente) {
        this.cliente = cliente;
    }

    public Set<Email> getEmails() {
        if (emails == null) {
            emails = new HashSet<>();
        }
        return emails;
    }

    public void setEmails(Set<Email> emails) {
        this.emails = emails;
    }

    public Set<Telefone> getTelefones() {
        if (telefones == null) {
            telefones = new HashSet<>();
        }
        return telefones;
    }

    public void setTelefones(Set<Telefone> telefones) {
        this.telefones = telefones;
    }

    public Estado getEstado() {
        return estado;
    }

    public void setEstado(Estado planoTipo) {
        this.estado = planoTipo;
    }

    public Cidade getCidade() {
        return cidade;
    }

    public void setCidade(Cidade cidade) {
        this.cidade = cidade;
    }

    public Pais getPais() {
        return pais;
    }

    public void setPais(Pais pais) {
        this.pais = pais;
    }

    public Set<Endereco> getEnderecos() {
        return enderecos;
    }

    public void setEnderecos(Set<Endereco> enderecos) {
        this.enderecos = enderecos;
    }

    public Profissao getProfissao() {
        return profissao;
    }

    public void setProfissao(Profissao profissao) {
        this.profissao = profissao;
    }

    public String getCfp() {
        return cfp;
    }

    public void setCfp(String cfp) {
        this.cfp = cfp;
    }

    public String getRg() {
        return rg;
    }

    public void setRg(String rg) {
        this.rg = rg;
    }

    public String getEmailApresentar() {
        if(getEmails().size() > 0) {
            List<Email> listEmail = new ArrayList<>();
            for (Email e : getEmails()) {
                Email email = new Email();
                email.setCodigo(e.getCodigo());
                email.setEmail(e.getEmail());
                listEmail.add(email);
            }
            listEmail.sort((e1, e2) -> e1.getCodigo() - e2.getCodigo());
            return listEmail.get(0).getEmail();
        }
        return "";
    }

    public String getTelefonesApresentar() {
        if(getTelefones().size() > 0) {
            List<Telefone> listTelefones = new ArrayList<>();
            for (Telefone t : getTelefones()) {
                Telefone telefone = new Telefone();
                telefone.setCodigo(t.getCodigo());
                telefone.setNumero(t.getNumero());
                listTelefones.add(telefone);
            }
            listTelefones.sort((t1, t2) -> t1.getCodigo() - t2.getCodigo());
            String telefonesApresentar = "";
            for(Telefone t: listTelefones){
                telefonesApresentar += " - "+t.getNumero();
            }
            return telefonesApresentar.replaceFirst(" - ","");
        }
        return "";
    }

    public String getFotokey() {
        return fotokey;
    }

    public void setFotokey(String fotokey) {
        this.fotokey = fotokey;
    }
}
