package com.pacto.relatorioms.entities;
import com.pacto.config.annotations.NomeEntidadeLog;
import com.pacto.config.annotations.NotLogged;

import javax.persistence.*;

@Entity
@NomeEntidadeLog("Email")
public class Email {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    protected Integer codigo;

    @NotLogged
    @ManyToOne
    @JoinColumn(name = "pessoa", foreignKey = @ForeignKey(name = "fk_email_pessoa"))
    private Pessoa pessoa;
    private String email;
    protected Boolean emailCorrespondencia;
    private Boolean bloqueadoBounce;
    private Boolean receberEmailNovidades;

    public Email(String email) {
        this.email = email;
    }

    public Email() {
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Pessoa getPessoa() {
        return pessoa;
    }

    public void setPessoa(Pessoa pessoa) {
        this.pessoa = pessoa;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public Boolean getEmailCorrespondencia() {
        return emailCorrespondencia;
    }

    public void setEmailCorrespondencia(Boolean emailCorrespondencia) {
        this.emailCorrespondencia = emailCorrespondencia;
    }

    public Boolean getBloqueadoBounce() {
        return bloqueadoBounce;
    }

    public void setBloqueadoBounce(Boolean bloqueadoBounce) {
        this.bloqueadoBounce = bloqueadoBounce;
    }

    public Boolean getReceberEmailNovidades() {
        return receberEmailNovidades;
    }

    public void setReceberEmailNovidades(Boolean receberEmailNovidades) {
        this.receberEmailNovidades = receberEmailNovidades;
    }

}
