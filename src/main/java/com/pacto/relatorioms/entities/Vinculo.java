package com.pacto.relatorioms.entities;

import com.pacto.config.annotations.NomeEntidadeLog;
import com.pacto.config.annotations.NotLogged;

import javax.persistence.*;

@Entity
@NomeEntidadeLog("Vinculo")
public class Vinculo {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer codigo;
    private String tipoVinculo;
    @NotLogged
    @ManyToOne(cascade = {CascadeType.REFRESH})
    @JoinColumn(name = "cliente", foreignKey = @ForeignKey(name = "fk_vinculo_cliente"))
    private Cliente cliente;
    @NotLogged
    @ManyToOne(cascade = {CascadeType.REFRESH})
    @JoinColumn(name = "colaborador", foreignKey = @ForeignKey(name = "fk_vinculo_colaborador"))
    private Colaborador colaborador;

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getTipoVinculo() {
        return tipoVinculo;
    }

    public void setTipoVinculo(String tipovinculo) {
        this.tipoVinculo = tipovinculo;
    }

    public Cliente getCliente() {
        return cliente;
    }

    public void setCliente(Cliente cliente) {
        this.cliente = cliente;
    }

    public Colaborador getColaborador() {
        return colaborador;
    }

    public void setColaborador(Colaborador colaborador) {
        this.colaborador = colaborador;
    }
}
