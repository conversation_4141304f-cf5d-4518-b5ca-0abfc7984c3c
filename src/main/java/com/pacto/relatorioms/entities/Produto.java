package com.pacto.relatorioms.entities;

import javax.persistence.*;

@Entity
public class Produto {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer codigo;
    private String descricao;
    private Integer nrdiasVigencia;
    private Double valorFinal;

    @Column(name = "tipoproduto")
    private String tipoProduto;


    public Produto(Integer codigo, String descricao, Integer nrdiasVigencia, String tipoProduto) {
        this.codigo = codigo;
        this.descricao = descricao;
        this.nrdiasVigencia = nrdiasVigencia;
        this.tipoProduto = tipoProduto;
    }

    public Produto() {
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public Integer getNrdiasVigencia() {
        return nrdiasVigencia;
    }

    public void setNrdiasVigencia(Integer nrdiasVigencia) {
        this.nrdiasVigencia = nrdiasVigencia;
    }

    public Double getValorFinal() {
        return valorFinal;
    }

    public void setValorFinal(Double valorfinal) {
        this.valorFinal = valorfinal;
    }

    public String getTipoProduto() {
        return tipoProduto;
    }

    public void setTipoProduto(String tipoProduto) {
        this.tipoProduto = tipoProduto;
    }
}
