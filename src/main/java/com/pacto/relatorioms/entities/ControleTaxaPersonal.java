package com.pacto.relatorioms.entities;

import javax.persistence.CascadeType;
import javax.persistence.Entity;
import javax.persistence.ForeignKey;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.OneToMany;
import java.util.Date;
import java.util.Set;

@Entity
public class ControleTaxaPersonal {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer codigo;
    private Integer empresa;
    private Date dataRegistro;
    private Date dataInicioVigenciaPlano;
    private Date dataFimVigenciaPlano;
    private Boolean cancelado;
    private Date dataCancelamento;
    private Integer responsavelCancelamento;

    @ManyToOne(cascade = {CascadeType.REFRESH})
    @JoinColumn(name = "personal", foreignKey = @ForeignKey(name = "fk_controletaxapersonal_colaborador"))
    private Colaborador personal;

    @ManyToOne(cascade = {CascadeType.REFRESH})
    @JoinColumn(name = "plano", foreignKey = @ForeignKey(name = "controletaxapersonal_plano_codigo_fk"))
    private Plano plano;

    @ManyToOne(cascade = {CascadeType.REFRESH})
    @JoinColumn(name = "responsavel", foreignKey = @ForeignKey(name = "fk_controletaxapersonal_usuario"))
    private Usuario responsavel;

    @OneToMany(mappedBy = "controle", cascade = {CascadeType.REFRESH})
    private Set<ItemTaxaPersonal> itens;

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Integer getEmpresa() {
        return empresa;
    }

    public void setEmpresa(Integer empresa) {
        this.empresa = empresa;
    }

    public Date getDataRegistro() {
        return dataRegistro;
    }

    public void setDataRegistro(Date dataRegistro) {
        this.dataRegistro = dataRegistro;
    }

    public Date getDataInicioVigenciaPlano() {
        return dataInicioVigenciaPlano;
    }

    public void setDataInicioVigenciaPlano(Date dataInicioVigenciaPlano) {
        this.dataInicioVigenciaPlano = dataInicioVigenciaPlano;
    }

    public Date getDataFimVigenciaPlano() {
        return dataFimVigenciaPlano;
    }

    public void setDataFimVigenciaPlano(Date dataFimVigenciaPlano) {
        this.dataFimVigenciaPlano = dataFimVigenciaPlano;
    }

    public Boolean getCancelado() {
        return cancelado;
    }

    public void setCancelado(Boolean cancelado) {
        this.cancelado = cancelado;
    }

    public Date getDataCancelamento() {
        return dataCancelamento;
    }

    public void setDataCancelamento(Date dataCancelamento) {
        this.dataCancelamento = dataCancelamento;
    }

    public Integer getResponsavelCancelamento() {
        return responsavelCancelamento;
    }

    public void setResponsavelCancelamento(Integer responsavelCancelamento) {
        this.responsavelCancelamento = responsavelCancelamento;
    }

    public Colaborador getPersonal() {
        return personal;
    }

    public void setPersonal(Colaborador personal) {
        this.personal = personal;
    }

    public Plano getPlano() {
        return plano;
    }

    public void setPlano(Plano plano) {
        this.plano = plano;
    }

    public Usuario getResponsavel() {
        return responsavel;
    }

    public void setResponsavel(Usuario responsavel) {
        this.responsavel = responsavel;
    }

    public Set<ItemTaxaPersonal> getItens() {
        return itens;
    }

    public void setItens(Set<ItemTaxaPersonal> itens) {
        this.itens = itens;
    }
}
