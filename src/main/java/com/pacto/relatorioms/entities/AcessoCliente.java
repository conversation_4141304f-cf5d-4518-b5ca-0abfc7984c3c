package com.pacto.relatorioms.entities;

import javax.persistence.*;
import java.util.Date;
import com.pacto.config.annotations.RelationalField;
import org.hibernate.annotations.NotFound;
import org.hibernate.annotations.NotFoundAction;


@Entity
public class AcessoCliente {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer codigo;
    private String sentido;
    private Date dtHrEntrada;
    private Date dtHrSaida;
    private Integer meioIdentificacaoEntrada;
    private Integer meioIdentificacaoSaida;
    private Integer tipoAcesso;
    private Date dataRegistro;
    private String ticket;
    private String situacao;

    @RelationalField
    @ManyToOne
    @JoinColumn(name = "cliente", foreignKey = @ForeignKey(ConstraintMode.NO_CONSTRAINT))
    @NotFound(action = NotFoundAction.IGNORE)
    private Cliente cliente;


    @RelationalField
    @ManyToOne
    @JoinColumn(name = "localAcesso", foreignKey = @ForeignKey(ConstraintMode.NO_CONSTRAINT))
    @NotFound(action = NotFoundAction.IGNORE)
    private LocalAcesso localAcesso;

    @RelationalField
    @ManyToOne
    @JoinColumn(name = "coletor", foreignKey = @ForeignKey(ConstraintMode.NO_CONSTRAINT))
    @NotFound(action = NotFoundAction.IGNORE)
    private Coletor coletor;

    @RelationalField
    @ManyToOne
    @JoinColumn(name = "usuario", foreignKey = @ForeignKey(ConstraintMode.NO_CONSTRAINT))
    @NotFound(action = NotFoundAction.IGNORE)
    private Usuario usuario;

    private String nomeCodEmpresaAcessou;
    private String nomeCodEmpresaOrigem;
    private String nomeCpfEmailClienteOrigem;

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Cliente getCliente() {
        return cliente;
    }

    public void setCliente(Cliente cliente) {
        this.cliente = cliente;
    }

    public Date getDtHrEntrada() {
        return dtHrEntrada;
    }

    public void setDtHrEntrada(Date dtHrEntrada) {
        this.dtHrEntrada = dtHrEntrada;
    }

    public String getSentido() {
        return sentido;
    }

    public void setSentido(String sentido) {
        this.sentido = sentido;
    }

    public Date getDtHrSaida() {
        return dtHrSaida;
    }

    public void setDtHrSaida(Date dtHrSaida) {
        this.dtHrSaida = dtHrSaida;
    }

    public Integer getMeioIdentificacaoEntrada() {
        return meioIdentificacaoEntrada;
    }

    public void setMeioIdentificacaoEntrada(Integer meioIdentificacaoEntrada) {
        this.meioIdentificacaoEntrada = meioIdentificacaoEntrada;
    }

    public Integer getMeioIdentificacaoSaida() {
        return meioIdentificacaoSaida;
    }

    public void setMeioIdentificacaoSaida(Integer meioIdentificacaoSaida) {
        this.meioIdentificacaoSaida = meioIdentificacaoSaida;
    }

    public Integer getTipoAcesso() {
        return tipoAcesso;
    }

    public void setTipoAcesso(Integer tipoAcesso) {
        this.tipoAcesso = tipoAcesso;
    }

    public Date getDataRegistro() {
        return dataRegistro;
    }

    public void setDataRegistro(Date dataRegistro) {
        this.dataRegistro = dataRegistro;
    }

    public String getTicket() {
        return ticket;
    }

    public void setTicket(String ticket) {
        this.ticket = ticket;
    }

    public LocalAcesso getLocalAcesso() {
        return localAcesso;
    }

    public void setLocalAcesso(LocalAcesso localAcesso) {
        this.localAcesso = localAcesso;
    }

    public Coletor getColetor() {
        return coletor;
    }

    public void setColetor(Coletor coletor) {
        this.coletor = coletor;
    }

    public Usuario getUsuario() {
        return usuario;
    }

    public void setUsuario(Usuario usuario) {
        this.usuario = usuario;
    }

    public String getSituacao() {
        return situacao;
    }

    public void setSituacao(String situacao) {
        this.situacao = situacao;
    }

    public String getNomeCodEmpresaAcessou() {
        return nomeCodEmpresaAcessou;
    }

    public void setNomeCodEmpresaAcessou(String nomeCodEmpresaAcessou) {
        this.nomeCodEmpresaAcessou = nomeCodEmpresaAcessou;
    }

    public String getNomeCodEmpresaOrigem() {
        return nomeCodEmpresaOrigem;
    }

    public void setNomeCodEmpresaOrigem(String nomeCodEmpresaOrigem) {
        this.nomeCodEmpresaOrigem = nomeCodEmpresaOrigem;
    }

    public String getNomeCpfEmailClienteOrigem() {
        return nomeCpfEmailClienteOrigem;
    }

    public void setNomeCpfEmailClienteOrigem(String nomeCpfEmailClienteOrigem) {
        this.nomeCpfEmailClienteOrigem = nomeCpfEmailClienteOrigem;
    }
}
