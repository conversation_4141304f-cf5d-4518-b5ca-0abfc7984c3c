package com.pacto.relatorioms.entities;

import com.pacto.config.annotations.NomeEntidadeLog;

import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;

@Entity
@NomeEntidadeLog("Categoria")
public class Categoria {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer codigo;
    private Integer nrConvitePermitido;
    private String tipoCategoria;
    private String nome;
    private Integer tipoCategoriaClube;
    private Integer tipoBloqueioInadimplencia;
    private String nomeExterno;

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Integer getNrConvitePermitido() {
        return nrConvitePermitido;
    }

    public void setNrConvitePermitido(Integer nrconvitepermitido) {
        this.nrConvitePermitido = nrconvitepermitido;
    }

    public String getTipoCategoria() {
        return tipoCategoria;
    }

    public void setTipoCategoria(String tipocategoria) {
        this.tipoCategoria = tipocategoria;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public Integer getTipoCategoriaClube() {
        return tipoCategoriaClube;
    }

    public void setTipoCategoriaClube(Integer tipocategoriaclube) {
        this.tipoCategoriaClube = tipocategoriaclube;
    }

    public Integer getTipoBloqueioInadimplencia() {
        return tipoBloqueioInadimplencia;
    }

    public void setTipoBloqueioInadimplencia(Integer tipobloqueioinadimplencia) {
        this.tipoBloqueioInadimplencia = tipobloqueioinadimplencia;
    }

    public String getNomeExterno() {
        return nomeExterno;
    }

    public void setNomeExterno(String nomeexterno) {
        this.nomeExterno = nomeexterno;
    }
}
