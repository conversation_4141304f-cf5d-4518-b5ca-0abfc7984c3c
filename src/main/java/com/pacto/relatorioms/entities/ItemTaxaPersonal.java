package com.pacto.relatorioms.entities;

import javax.persistence.*;

@Entity
public class ItemTaxaPersonal {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer codigo;
    private Double descontoEspecifico;

    @ManyToOne(cascade = {CascadeType.REFRESH})
    @JoinColumn(name = "controle", foreignKey = @ForeignKey(name = "fk_itemtaxapersonal_controle"))
    private ControleTaxaPersonal controle;

    @ManyToOne(cascade = {CascadeType.REFRESH})
    @JoinColumn(name = "aluno", foreignKey = @ForeignKey(name = "fk_itemtaxapersonal_cliente"))
    private Cliente aluno;

    @ManyToOne(cascade = {CascadeType.REFRESH})
    @JoinColumn(name = "produto", foreignKey = @ForeignKey(name = "fk_itemtaxapersonal_produto"))
    private Produto produto;

    @ManyToOne(cascade = {CascadeType.REFRESH})
    @JoinColumn(name = "desconto", foreignKey = @ForeignKey(name = "fk_itemtaxapersonal_desconto"))
    private Desconto desconto;

    @ManyToOne(cascade = {CascadeType.REFRESH})
    @JoinColumn(name = "movProduto", foreignKey = @ForeignKey(name = "fk_itemtaxapersonal_movproduto"))
    private MovProduto movProduto;

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Double getDescontoEspecifico() {
        return descontoEspecifico;
    }

    public void setDescontoEspecifico(Double descontoEspecifico) {
        this.descontoEspecifico = descontoEspecifico;
    }

    public ControleTaxaPersonal getControle() {
        return controle;
    }

    public void setControle(ControleTaxaPersonal controle) {
        this.controle = controle;
    }

    public Cliente getAluno() {
        return aluno;
    }

    public void setAluno(Cliente aluno) {
        this.aluno = aluno;
    }

    public Produto getProduto() {
        return produto;
    }

    public void setProduto(Produto produto) {
        this.produto = produto;
    }

    public Desconto getDesconto() {
        return desconto;
    }

    public void setDesconto(Desconto desconto) {
        this.desconto = desconto;
    }

    public MovProduto getMovProduto() {
        return movProduto;
    }

    public void setMovProduto(MovProduto movProduto) {
        this.movProduto = movProduto;
    }
}
