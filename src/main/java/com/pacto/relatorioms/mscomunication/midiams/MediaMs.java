package com.pacto.relatorioms.mscomunication.midiams;


import com.pacto.config.security.interfaces.RequestService;
import com.pacto.config.utils.HttpServico;
import com.pacto.config.utils.JSONMapper;
import com.pacto.relatorioms.dto.base.ClientDiscoveryDataDTO;
import com.pacto.relatorioms.mscomunication.midiams.dto.MidiaDTO;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import java.util.Base64;

@Component
public class MediaMs {

    private final RestTemplate restTemplate;
    private final HttpServico httpServico;
    private final RequestService requestService;
    @Value("${discovery.url}")
    private String urlDiscovery;


    public MediaMs(HttpServico httpServico, RequestService requestService) {
        this.httpServico = httpServico;
        this.restTemplate = new RestTemplate();
        this.requestService = requestService;
    }

    public byte[] downloadByteArray(MidiaDTO midiaDTO) throws Exception {
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<?> httpEntity = new HttpEntity<>(midiaDTO, httpHeaders);

        ResponseEntity<String> response = restTemplate.exchange(
                getUrlMidiaMs("/media/download-byte"),
                HttpMethod.POST,
                httpEntity,
                String.class
        );
        return Base64.getDecoder().decode(new JSONObject(response.getBody()).optJSONObject("content").optString("bytes"));
    }


    private ClientDiscoveryDataDTO getDiscoveryData() throws Exception {
        ResponseEntity<String> resposta = httpServico.doJson(
                urlDiscovery + "/find/" + requestService.getUsuarioAtual().getChave(),
                null, HttpMethod.GET, requestService.getToken()
        );
        return JSONMapper.getObject(
                new JSONObject(resposta.getBody()).getJSONObject("content"), ClientDiscoveryDataDTO.class
        );
    }

    private String getUrlMidiaMs(String uri) throws Exception {
        String midiaMsUrl = getDiscoveryData().getServiceUrls().getMidiaMsUrl();
        UriComponentsBuilder builder = UriComponentsBuilder.fromHttpUrl(midiaMsUrl + uri);
        return builder.toUriString();
    }
}
