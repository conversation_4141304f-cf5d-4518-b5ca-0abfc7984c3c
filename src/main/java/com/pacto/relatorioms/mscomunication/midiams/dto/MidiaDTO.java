package com.pacto.relatorioms.mscomunication.midiams.dto;

public class MidiaDTO {

    private String chave;
    private String tipo;
    private String identificador;
    private String extensao;
    private byte[] data;
    private String arquivo;
    private String nomeArquivo;
    private Boolean encriptarIdentificador;

    public String getChave() {
        return chave;
    }

    public void setChave(String chave) {
        this.chave = chave;
    }

    public String getTipo() {
        return tipo;
    }

    public void setTipo(String tipo) {
        this.tipo = tipo;
    }

    public String getIdentificador() {
        return identificador;
    }

    public void setIdentificador(String identificador) {
        this.identificador = identificador;
    }

    public String getExtensao() {
        return extensao;
    }

    public void setExtensao(String extensao) {
        this.extensao = extensao;
    }

    public byte[] getData() {
        return data;
    }

    public void setData(byte[] data) {
        this.data = data;
    }

    public Boolean getEncriptarIdentificador() {
        return encriptarIdentificador;
    }

    public void setEncriptarIdentificador(Boolean encriptarIdentificador) {
        this.encriptarIdentificador = encriptarIdentificador;
    }

    public String getArquivo() {
        return arquivo;
    }

    public void setArquivo(String arquivo) {
        this.arquivo = arquivo;
    }

    public String getNomeArquivo() {
        return nomeArquivo;
    }

    public void setNomeArquivo(String nomeArquivo) {
        this.nomeArquivo = nomeArquivo;
    }
}
