package com.pacto.relatorioms.utils;

import net.sf.jasperreports.engine.*;
import net.sf.jasperreports.engine.data.JRBeanArrayDataSource;
import net.sf.jasperreports.engine.export.JRPdfExporter;
import net.sf.jasperreports.engine.export.JRXlsExporterParameter;
import net.sf.jasperreports.engine.export.ooxml.JRXlsxExporter;
import net.sf.jasperreports.engine.util.JRLoader;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import java.io.*;
import java.text.DateFormat;
import java.util.*;

public class VisualizadorRelatorio {

    public String exportarRelatorio(String key, HttpServletRequest request, Map<String, Object> params) throws Exception {
        String nomeDesignIReport = params.get("nomeDesignIReport").toString();
        String nomeRelatotio = params.get("nomeRelatorio").toString();
        if (params.get("logoPadraoRelatorio") == null) {
            params.put("logoPadraoRelatorio",  getInputStreamFromResource("relatorio/imgs/logoPadraoRelatorio.jpg"));
        }
        JasperPrint print = gerarRelatorioJasperPrintObjeto(request, nomeDesignIReport, params, "pt");
        String retorno = "";
        if (params.get("tipoRelatorio").equals("PDF")) {
            retorno = visualizarRelatorioPDF(key, request, print, nomeRelatotio);
        } else if (params.get("tipoRelatorio").equals("XLS")) {
            retorno = visualizarRelatorioEXCEL(key, request, print, nomeRelatotio);
        }
        return retorno;
    }

    public String exportarPDFSemLista(String key, HttpServletRequest request, Map<String, Object> params) throws Exception {
        String nomeDesignIReport = params.get("nomeDesignIReport").toString();
        String nomeRelatotio = params.get("nomeRelatorio").toString();
        if (params.get("logoPadraoRelatorio") == null) {
            params.put("logoPadraoRelatorio",  getInputStreamFromResource("relatorio/imgs/logoPadraoRelatorio.jpg"));
        }
        JasperPrint print = gerarRelatorioJasper(request, nomeDesignIReport, params, "pt");
        return visualizarRelatorioPDF(key, request, print, nomeRelatotio);
    }

    public JasperPrint gerarRelatorioJasperPrintObjeto(HttpServletRequest request,
                                                       String nomeDesignIReport,
                                                       Map<String, Object> params, String language) throws Exception {

        JRDataSource jr = new JRBeanArrayDataSource(((List) params.get("listaObjetos")).toArray(), false);
        InputStream arquivoIReport = getInputStreamFromResource(nomeDesignIReport.replace("\\", "/"));
        JasperReport jasperReport = (JasperReport) JRLoader.loadObject(arquivoIReport);
        jasperReport.setProperty("net.sf.jasperreports.awt.ignore.missing.font", "true");
        jasperReport.setProperty("net.sf.jasperreports.default.pdf.font.name", "SansSerif");
        params.put("REPORT_TIME_ZONE", TimeZone.getTimeZone("GMT-03:00"));
        return JasperFillManager.fillReport(jasperReport, params, jr);
    }

    public JasperPrint gerarRelatorioJasper(HttpServletRequest request,
                                                       String nomeDesignIReport,
                                                       Map<String, Object> params, String language) throws Exception {

        JRDataSource jr = new JREmptyDataSource();
        InputStream arquivoIReport = getInputStreamFromResource(nomeDesignIReport.replace("\\", "/"));
        JasperReport jasperReport = (JasperReport) JRLoader.loadObject(arquivoIReport);
        jasperReport.setProperty("net.sf.jasperreports.awt.ignore.missing.font", "true");
        jasperReport.setProperty("net.sf.jasperreports.default.pdf.font.name", "SansSerif");
        params.put("REPORT_TIME_ZONE", TimeZone.getTimeZone("GMT-03:00"));
        return JasperFillManager.fillReport(jasperReport, params, jr);
    }

    protected String visualizarRelatorioPDF(final String key,
                                            HttpServletRequest request,
                                            JasperPrint print,
                                            String nomeRelatorio) throws ServletException, IOException, Exception {
        String nomePDF = nomeRelatorio
                + "-" + key
                + "-" + String.valueOf(new Date().getTime()) // TODO adicionar a classe Caledario utilizada no projeto do zillyon\treino
                + ".pdf";
        File pdfFolder = new File(obterCaminhoWebAplicacao(request) + File.separator + "temp");
        pdfFolder.mkdirs();
        File pdfFile = new File(pdfFolder.getPath() + File.separator + nomePDF);
        if (pdfFile.exists()) {
            try {
                pdfFile.delete();
            } catch (Exception e) {
                DateFormat formatador = DateFormat.getDateInstance(DateFormat.MEDIUM);
                String dataStr = formatador.format(new Date());
                nomePDF = nomePDF + dataStr + ".pdf";
                String nomeRelPDF = "relatorios" + File.separator + nomePDF;
                pdfFile = new File(obterCaminhoWebAplicacao(request) + File.separator + nomeRelPDF);
            }

        }

        JRPdfExporter jrpdfexporter = new JRPdfExporter();
        jrpdfexporter.setParameter(JRExporterParameter.JASPER_PRINT, print);
        jrpdfexporter.setParameter(JRExporterParameter.OUTPUT_FILE, pdfFile);
        //debug qual empresa gerando o relatório
        System.out.println("EXPORTANDO PDF: " + pdfFile.getAbsolutePath());
        String urlAplicacao = request.getRequestURI();
        urlAplicacao = urlAplicacao.substring(0, urlAplicacao.lastIndexOf("/"));
        jrpdfexporter.exportReport();
        return nomePDF;
    }

    public String obterCaminhoBaseAplicacao(HttpServletRequest request) throws Exception {
        return new File(request.getSession().getServletContext().getRealPath("WEB-INF" + File.separator + "classes")).getAbsolutePath();
    }

    public String obterCaminhoWebAplicacao(HttpServletRequest request) throws Exception {
        return new File(request.getSession().getServletContext().getRealPath("")).getAbsolutePath();
    }

    public InputStream getInputStreamFromResource(String pathResource) throws IOException {
        Resource resourceImage = new ClassPathResource(pathResource);
        return resourceImage.getInputStream();
    }

    public InputStream getImagem() throws Exception {
        Resource resource = new ClassPathResource("relatorio/imgs/logoPadraoRelatorio.jpg");
        File imagem = resource.getFile();
//        File imagem = new File(ResourceUtils.getURL("classpath:relatorio/imgs/logoPadraoRelatorio.jpg").getPath());
        ByteArrayOutputStream arrayOutputStream = new ByteArrayOutputStream();
        byte buffer[] = new byte[4096];
        int bytesRead = 0;
        FileInputStream fi = new FileInputStream(imagem.getAbsolutePath());
        while ((bytesRead = fi.read(buffer)) != -1) {
            arrayOutputStream.write(buffer, 0, bytesRead);
        }
        byte[] a = (arrayOutputStream.toByteArray());
        InputStream fs = new ByteArrayInputStream(a);
        arrayOutputStream.close();
        fi.close();
        return fs;
    }

    protected String visualizarRelatorioEXCEL(String key, HttpServletRequest request, JasperPrint print, String nomeRelatorio) throws ServletException,
            IOException, Exception {

        String nomeXLS = nomeRelatorio
                + "-" + key
                + "-" + String.valueOf(new Date().getTime()) // TODO adicionar a classe Caledario utilizada no projeto do zillyon\treino
                + ".xls";
        File xlsFolder = new File(obterCaminhoWebAplicacao(request) + File.separator + "temp");
        xlsFolder.mkdirs();
        File xlsFile = new File(xlsFolder.getPath() + File.separator + nomeXLS);
        if (xlsFile.exists()) {
            try {
                xlsFile.delete();
            } catch (Exception e) {
                DateFormat formatador = DateFormat.getDateInstance(DateFormat.MEDIUM);
                String dataStr = formatador.format(new Date().getTime()); // TODO adicionar a classe Caledario utilizada no projeto do zillyon\treino
                nomeXLS = nomeXLS + dataStr + ".xls";
                String nomeRelPDF = "relatorios" + File.separator + nomeXLS;
                xlsFile = new File(obterCaminhoWebAplicacao(request) + File.separator + nomeRelPDF);
            }

        }

        JRXlsxExporter exporter = new JRXlsxExporter();
        exporter.setParameter(JRExporterParameter.JASPER_PRINT, print);
        exporter.setParameter(JRExporterParameter.OUTPUT_FILE_NAME, xlsFile.toString());
        exporter.setParameter(JRXlsExporterParameter.IS_ONE_PAGE_PER_SHEET, Boolean.FALSE);
        exporter.setParameter(JRXlsExporterParameter.IS_REMOVE_EMPTY_SPACE_BETWEEN_ROWS, Boolean.TRUE);
        exporter.setParameter(JRXlsExporterParameter.IS_REMOVE_EMPTY_SPACE_BETWEEN_COLUMNS, Boolean.TRUE);
        exporter.setParameter(JRXlsExporterParameter.IS_DETECT_CELL_TYPE, Boolean.TRUE);
        exporter.setParameter(JRXlsExporterParameter.IS_COLLAPSE_ROW_SPAN, Boolean.TRUE);

        exporter.exportReport();

        return nomeXLS;
    }
}
