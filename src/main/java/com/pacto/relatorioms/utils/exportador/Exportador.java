package com.pacto.relatorioms.utils.exportador;

import com.pacto.relatorioms.utils.exportador.processador.IProcessador;
import com.pacto.relatorioms.utils.exportador.processador.ProcessadorExcel;

import java.io.File;

/**
 * Realiza a exportação de um relatório.
 * Created by johnys on 11/10/2016.
 */
public class Exportador {

    /**
     * Realiza a exportação de um relatório para excel.
     * @param relatorio
     * @return
     * @throws Exception
     */
    public static void exportarExcel(RelatorioBuilder relatorio, File file) throws  Exception{
        IProcessador processador = new ProcessadorExcel();
        processador.processar(relatorio, file);
    }

}
