package com.pacto.relatorioms.utils.exportador;

import com.pacto.config.utils.UteisValidacao;
import com.pacto.relatorioms.utils.exportador.formatadores.Formatador;
import com.pacto.relatorioms.utils.exportador.reflexao.LeitorPropriedade;

/**
 * Classe que representa uma coluna do relatório.
 * Created by johny<PERSON> on 11/10/2016.
 */
public class Coluna {

    /**
     * Nome da coluna a ser demonstrada no relatório.
     */
    private String nome;

    /**
     * Campo do objeto que será exibido no relatório.
     */
    private String campoObjeto;

    private LeitorPropriedade leitor;

    /**
     * Formatador do campoObjeto caso necessário.
     */
    private Formatador<?> formatador;

    public Coluna(String nome, String campoObjeto) {
        setNome(nome);
        setCampoObjeto(campoObjeto);
        if (!UteisValidacao.emptyString(campoObjeto))
            this.leitor = new LeitorPropriedade(campoObjeto);
    }

    public Coluna(String nome, String campoObjeto, Formatador<?> formatador){
        this(nome, campoObjeto);
        setFormatador(formatador);
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public String getCampoObjeto() {
        return campoObjeto;
    }

    public void setCampoObjeto(String campoObjeto) {
        this.campoObjeto = campoObjeto;
    }

    public Formatador<?> getFormatador() {
        return formatador;
    }

    public void setFormatador(Formatador<?> formatador) {
        this.formatador = formatador;
    }

    public LeitorPropriedade getLeitor() {
        return leitor;
    }
}
