package com.pacto.relatorioms.utils;

import java.util.Calendar;
import java.util.Date;

public class UteisRelatorioMs {

    public static String getIntervaloHorasEntreDatas(Date dataHoraInicial, Date dataHoraFinal) throws Exception {
        StringBuilder ret = new StringBuilder();
        // se nao existe as datas retorna string vazia
        if (dataHoraInicial == null || dataHoraFinal == null) {
            return ret.toString();
        }
        // calcula os minutos entre essas datas e transforma para o padrão hh:mi
        Long mins = UteisRelatorioMs.minutosEntreDatas(dataHoraInicial, dataHoraFinal);
        Long horas = mins / 60;
        Long minutos = mins % 60;

        String horasString = "";
        if (horas < 10) {
            horasString = "0" + horas.toString();
        } else {
            horasString = horas.toString();
        }
        String minutosString = "";
        if (minutos < 10) {
            minutosString = "0" + minutos.toString();
        } else {
            minutosString = minutos.toString();
        }
        ret.append(horasString);
        ret.append(":");
        ret.append(minutosString);
        return ret.toString();
    }

    public static long minutosEntreDatas(Date dataInicial, Date dataFinal) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(dataFinal);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);

        long mins = calendar.getTime().getTime() / (1000 * 60);

        calendar = Calendar.getInstance();
        calendar.setTime(dataInicial);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);

        long minsFinal = calendar.getTime().getTime() / (1000 * 60);

        return mins - minsFinal;
//		return mins - (calendar.getTime().getTime() / (1000 * 60));
    }

}