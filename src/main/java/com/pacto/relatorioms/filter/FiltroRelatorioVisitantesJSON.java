package com.pacto.relatorioms.filter;

import com.pacto.config.utils.Calendario;
import com.pacto.config.utils.Uteis;
import org.json.JSONException;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class FiltroRelatorioVisitantesJSON {
    private Integer empresa;
    private Date dataInicioBv;
    private Date dataFimBv;
    private String bvCompIncomp;
    private List<Integer> consultores;

    public FiltroRelatorioVisitantesJSON(JSONObject filters) throws JSONException {
        if (filters != null) {
            setEmpresa(filters.optInt("empresa"));
            long dataInicioBvJson = filters.optLong("datainiciobv");
            if(filters.optLong("datainiciobv") != 0) {
                setDataInicioBv(Uteis.getDataJDBC(Calendario.primeiraHoraDia(new Date(dataInicioBvJson))));
            }
            long dataFimBvJson = filters.optLong("datafimbv");
            if(filters.optLong("datafimbv") != 0) {
                setDataFimBv(Uteis.getDataJDBC(Calendario.ultimaHoraDia(new Date(dataFimBvJson))));
            }
            setBvCompIncomp(filters.optString("situacaoBv"));
            String consultores = filters.optString("consultores");
            if(!consultores.isEmpty()) {
                List<Integer> consultoresList = new ArrayList<>();
                for(String c: consultores.split(",")) {
                    consultoresList.add(Integer.parseInt(c));
                }
                setConsultores(consultoresList);
            }
        }
    }

    public Integer getEmpresa() {
        return empresa;
    }

    public void setEmpresa(Integer empresa) {
        this.empresa = empresa;
    }

    public Date getDataInicioBv() {
        return dataInicioBv;
    }

    public void setDataInicioBv(Date dataInicioBv) {
        this.dataInicioBv = dataInicioBv;
    }

    public Date getDataFimBv() {
        return dataFimBv;
    }

    public void setDataFimBv(Date dataFimBv) {
        this.dataFimBv = dataFimBv;
    }

    public String getBvCompIncomp() {
        return bvCompIncomp;
    }

    public void setBvCompIncomp(String bvCompIncomp) {
        this.bvCompIncomp = bvCompIncomp;
    }

    public List<Integer> getConsultores() {
        if(this.consultores == null) {
            this.consultores = new ArrayList<>();
        }
        return consultores;
    }

    public void setConsultores(List<Integer> consultores) {
        this.consultores = consultores;
    }
}

