package com.pacto.relatorioms.filter;

import org.json.JSONException;
import org.json.JSONObject;

import java.util.Locale;

public class FiltroRelatorioSaldoCreditoJSON {
    private Integer empresa;
    private Integer valorInicialCreditos;
    private Integer valorFinalCreditos;
    private boolean ativo;
    private boolean inativo;
    private String quickSearchValue;


    public FiltroRelatorioSaldoCreditoJSON(JSONObject filters) throws JSONException {
        if (filters != null) {
            setEmpresa(filters.optInt("empresa"));
            setValorInicialCreditos(filters.optInt("valorInicialCreditos"));
            setValorFinalCreditos(filters.optInt("valorFinalCreditos"));
            setAtivo(filters.optBoolean("ativo"));
            setInativo(filters.optBoolean("inativo"));
            setQuickSearchValue(filters.optString("quicksearchValue").toUpperCase());
            }
    }

    public Integer getEmpresa() {
        return empresa;
    }

    public void setEmpresa(Integer empresa) {
        this.empresa = empresa;
    }

    public Integer getValorInicialCreditos() {
        return valorInicialCreditos;
    }

    public void setValorInicialCreditos(Integer valorInicialCreditos) {
        this.valorInicialCreditos = valorInicialCreditos;
    }

    public Integer getValorFinalCreditos() {
        return valorFinalCreditos;
    }

    public void setValorFinalCreditos(Integer valorFinalCreditos) {
        this.valorFinalCreditos = valorFinalCreditos;
    }

    public boolean isAtivo() {
        return ativo;
    }

    public void setAtivo(boolean ativo) {
        this.ativo = ativo;
    }

    public boolean isInativo() {
        return inativo;
    }

    public void setInativo(boolean inativo) {
        this.inativo = inativo;
    }

    public String getQuickSearchValue() {
        return quickSearchValue;
    }

    public void setQuickSearchValue(String quickSearchValue) {
        this.quickSearchValue = quickSearchValue;
    }
}

