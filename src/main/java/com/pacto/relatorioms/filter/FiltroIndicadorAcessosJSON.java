package com.pacto.relatorioms.filter;

import org.json.JSONException;
import org.json.JSONObject;

import java.util.Date;

public class FiltroIndicadorAcessosJSON {

    private Integer empresa;
    private String filtrarPor;
    private Date periodoInicial;
    private Date periodoFinal;
    private boolean exibirAcessosBloqueados;

    public FiltroIndicadorAcessosJSON (JSONObject filters) throws JSONException {
        if (filters != null) {
            this.empresa = filters.optInt("empresa");
            this.filtrarPor = filters.optString("busca");
            if(filters.optLong("periodoInicial") != 0) {
                this.periodoInicial = new Date(filters.optLong("periodoInicial"));
            }
            if(filters.optLong("periodoFinal") != 0) {
                this.periodoFinal = new Date(filters.optLong("periodoFinal"));
            }
            this.exibirAcessosBloqueados = filters.optBoolean("exibirAcessosBloqueados");
        }
    }

    public String getFiltrarPor() {
        return filtrarPor;
    }

    public void setFiltrarPor(String filtrarPor) {
        this.filtrarPor = filtrarPor;
    }

    public Date getPeriodoInicial() {
        return periodoInicial;
    }

    public void setPeriodoInicial(Date periodoInicial) {
        this.periodoInicial = periodoInicial;
    }

    public Date getPeriodoFinal() {
        return periodoFinal;
    }

    public void setPeriodoFinal(Date periodoFinal) {
        this.periodoFinal = periodoFinal;
    }

    public Integer getEmpresa() {
        return empresa;
    }

    public void setEmpresa(Integer empresa) {
        this.empresa = empresa;
    }

    public boolean isExibirAcessosBloqueados() {
        return exibirAcessosBloqueados;
    }

    public void setExibirAcessosBloqueados(boolean exibirAcessosBloqueados) {
        this.exibirAcessosBloqueados = exibirAcessosBloqueados;
    }
}
