package com.pacto.relatorioms.filter;


import org.springframework.stereotype.Component;

import java.sql.Timestamp;

@Component
public class ListaAcessos {
    private Timestamp periodoLocacaoDe;
    private Timestamp periodoLocacaoAte;

    private Timestamp periodoRenovacaoDe;
    private Timestamp periodoRenovacaoAte;

    private Timestamp periodoVencimentoDe;
    private Timestamp periodoVencimentoAte;

    private Integer planoLocacao;
    private Integer tamanhoArmarioSelecionado;
    private String tipoArmarioSelecionado;
    private Boolean contratoAssinado;
    private String numeroArmario;
    private Boolean somenteParcelasAtrasadas;
    private Boolean habilitadoGestaoArmarios;

    public Timestamp getPeriodoLocacaoDe() {
        return periodoLocacaoDe;
    }

    public void setPeriodoLocacaoDe(Timestamp periodoLocacaoDe) {
        this.periodoLocacaoDe = periodoLocacaoDe;
    }

    public Timestamp getPeriodoLocacaoAte() {
        return periodoLocacaoAte;
    }

    public void setPeriodoLocacaoAte(Timestamp periodoLocacaoAte) {
        this.periodoLocacaoAte = periodoLocacaoAte;
    }

    public Timestamp getPeriodoRenovacaoDe() {
        return periodoRenovacaoDe;
    }

    public void setPeriodoRenovacaoDe(Timestamp periodoRenovacaoDe) {
        this.periodoRenovacaoDe = periodoRenovacaoDe;
    }

    public Timestamp getPeriodoRenovacaoAte() {
        return periodoRenovacaoAte;
    }

    public void setPeriodoRenovacaoAte(Timestamp periodoRenovacaoAte) {
        this.periodoRenovacaoAte = periodoRenovacaoAte;
    }

    public Timestamp getPeriodoVencimentoDe() {
        return periodoVencimentoDe;
    }

    public void setPeriodoVencimentoDe(Timestamp periodoVencimentoDe) {
        this.periodoVencimentoDe = periodoVencimentoDe;
    }

    public Timestamp getPeriodoVencimentoAte() {
        return periodoVencimentoAte;
    }

    public void setPeriodoVencimentoAte(Timestamp periodoVencimentoAte) {
        this.periodoVencimentoAte = periodoVencimentoAte;
    }

    public Integer getPlanoLocacao() {
        return planoLocacao;
    }

    public void setPlanoLocacao(Integer planoLocacao) {
        this.planoLocacao = planoLocacao;
    }

    public Integer getTamanhoArmarioSelecionado() {
        return tamanhoArmarioSelecionado;
    }

    public void setTamanhoArmarioSelecionado(Integer tamanhoArmarioSelecionado) {
        this.tamanhoArmarioSelecionado = tamanhoArmarioSelecionado;
    }

    public String getTipoArmarioSelecionado() {
        return tipoArmarioSelecionado;
    }

    public void setTipoArmarioSelecionado(String tipoArmarioSelecionado) {
        this.tipoArmarioSelecionado = tipoArmarioSelecionado;
    }

    public Boolean getContratoAssinado() {
        return contratoAssinado;
    }

    public void setContratoAssinado(Boolean contratoAssinado) {
        this.contratoAssinado = contratoAssinado;
    }

    public String getNumeroArmario() {
        return numeroArmario;
    }

    public void setNumeroArmario(String numeroArmario) {
        this.numeroArmario = numeroArmario;
    }

    public Boolean getSomenteParcelasAtrasadas() {
        return somenteParcelasAtrasadas;
    }

    public void setSomenteParcelasAtrasadas(Boolean somenteParcelasAtrasadas) {
        this.somenteParcelasAtrasadas = somenteParcelasAtrasadas;
    }

    public Boolean getHabilitadoGestaoArmarios() {
        return habilitadoGestaoArmarios;
    }

    public void setHabilitadoGestaoArmarios(Boolean habilitadoGestaoArmarios) {
        this.habilitadoGestaoArmarios = habilitadoGestaoArmarios;
    }
}
