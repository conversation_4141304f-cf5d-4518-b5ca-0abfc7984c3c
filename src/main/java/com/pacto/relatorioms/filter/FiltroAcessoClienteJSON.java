package com.pacto.relatorioms.filter;

import org.json.JSONException;
import org.json.JSONObject;

import java.util.Date;

public class FiltroAcessoClienteJSON {
    private Integer empresa;
    private Date periodoPesquisaInicial;
    private Date periodoPesquisaFinal;
    private String faixaHorariaInicial;
    private String faixaHorariaFinal;
    private String tipoListaAcesso; // CL || CO
    private Integer cliente;
    private Integer colaborador;
    private Integer plano;
    private Integer modalidade;
    private Integer professor<PERSON><PERSON><PERSON>;
    private Integer professor<PERSON><PERSON><PERSON><PERSON>;
    private Integer professor<PERSON><PERSON>ino;
    private Integer grupo;
    private String ordenadoPor;
    private Boolean exibirSomentePrimeiroAcessoPorDia;
    private String tipoArquivoExportar; // XLSX | PDF

    public FiltroAcessoClienteJSON(JSONObject filters) throws JSONException {
        if (filters != null) {
            this.empresa = filters.optInt("empresa");
            if(filters.optLong("periodoPesquisaInicial") != 0) {
                this.periodoPesquisaInicial = new Date(filters.optLong("periodoPesquisaInicial"));
            }
            if(filters.optLong("periodoPesquisaFinal") != 0) {
                this.periodoPesquisaFinal = new Date(filters.optLong("periodoPesquisaFinal"));
            }
            this.faixaHorariaInicial = filters.optString("faixaHorariaInicial");
            this.faixaHorariaFinal = filters.optString("faixaHorariaFinal");
            this.tipoListaAcesso = filters.optString("tipoListaAcesso");
            this.cliente = filters.optInt("cliente");
            this.colaborador = filters.optInt("colaborador");
            this.plano = filters.optInt("plano");
            this.modalidade = filters.optInt("modalidade");
            this.professorTurma = filters.optInt("professorTurma");
            this.professorVinculo = filters.optInt("professorVinculo");
            this.professorTreino = filters.optInt("professorTreino");
            this.grupo = filters.optInt("grupo");
            this.ordenadoPor = filters.optString("ordenadoPor");
            this.exibirSomentePrimeiroAcessoPorDia = filters.optBoolean("exibirSomentePrimeiroAcessoPorDia");
            this.tipoArquivoExportar = filters.optString("tipoArquivoExportar");
        }
    }

    public Integer getEmpresa() {
        return empresa;
    }

    public void setEmpresa(Integer empresa) {
        this.empresa = empresa;
    }

    public Date getPeriodoPesquisaInicial() {
        return periodoPesquisaInicial;
    }

    public void setPeriodoPesquisaInicial(Date periodoPesquisaInicial) {
        this.periodoPesquisaInicial = periodoPesquisaInicial;
    }

    public Date getPeriodoPesquisaFinal() {
        return periodoPesquisaFinal;
    }

    public void setPeriodoPesquisaFinal(Date periodoPesquisaFinal) {
        this.periodoPesquisaFinal = periodoPesquisaFinal;
    }

    public String getFaixaHorariaInicial() {
        return faixaHorariaInicial;
    }

    public void setFaixaHorariaInicial(String faixaHorariaInicial) {
        this.faixaHorariaInicial = faixaHorariaInicial;
    }

    public String getFaixaHorariaFinal() {
        return faixaHorariaFinal;
    }

    public void setFaixaHorariaFinal(String faixaHorariaFinal) {
        this.faixaHorariaFinal = faixaHorariaFinal;
    }

    public Integer getCliente() {
        return cliente;
    }

    public void setCliente(Integer cliente) {
        this.cliente = cliente;
    }

    public Integer getColaborador() {
        return colaborador;
    }

    public void setColaborador(Integer colaborador) {
        this.colaborador = colaborador;
    }

    public Integer getPlano() {
        return plano;
    }

    public void setPlano(Integer plano) {
        this.plano = plano;
    }

    public Integer getModalidade() {
        return modalidade;
    }

    public void setModalidade(Integer modalidade) {
        this.modalidade = modalidade;
    }

    public Integer getProfessorTurma() {
        return professorTurma;
    }

    public void setProfessorTurma(Integer professorTurma) {
        this.professorTurma = professorTurma;
    }

    public Integer getProfessorVinculo() {
        return professorVinculo;
    }

    public void setProfessorVinculo(Integer professorVinculo) {
        this.professorVinculo = professorVinculo;
    }

    public Integer getProfessorTreino() {
        return professorTreino;
    }

    public void setProfessorTreino(Integer professorTreino) {
        this.professorTreino = professorTreino;
    }

    public Integer getGrupo() {
        return grupo;
    }

    public void setGrupo(Integer grupo) {
        this.grupo = grupo;
    }

    public String getOrdenadoPor() {
        return ordenadoPor;
    }

    public void setOrdenadoPor(String ordenadoPor) {
        this.ordenadoPor = ordenadoPor;
    }

    public Boolean getExibirSomentePrimeiroAcessoPorDia() {
        return exibirSomentePrimeiroAcessoPorDia;
    }

    public void setExibirSomentePrimeiroAcessoPorDia(Boolean exibirSomentePrimeiroAcessoPorDia) {
        this.exibirSomentePrimeiroAcessoPorDia = exibirSomentePrimeiroAcessoPorDia;
    }

    public String getTipoListaAcesso() {
        return tipoListaAcesso;
    }

    public void setTipoListaAcesso(String tipoListaAcesso) {
        this.tipoListaAcesso = tipoListaAcesso;
    }

    public String getTipoArquivoExportar() {
        return tipoArquivoExportar;
    }

    public void setTipoArquivoExportar(String tipoArquivoExportar) {
        this.tipoArquivoExportar = tipoArquivoExportar;
    }
}

