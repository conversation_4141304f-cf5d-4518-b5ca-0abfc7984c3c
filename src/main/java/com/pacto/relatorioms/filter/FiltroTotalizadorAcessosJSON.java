package com.pacto.relatorioms.filter;

import org.json.JSONException;
import org.json.JSONObject;

import java.util.Date;

public class FiltroTotalizadorAcessosJSON {
    private Integer empresa;
    private Date periodoInicial;
    private Date periodoFinal;
    private String sexo;
    private Integer faixaEtariaInicial;
    private Integer faixaEtariaFinal;
    private Integer plano;
    private Integer modalidade;
    private Integer cliente;
    private Integer colaborador;
    private String frequencia;
    private Boolean agruparPorPessoa;
    private String agrupamento;
    private Boolean considerarPrimeiroAcessoDia;
    private String grafico;
    private boolean somenteTotalAcessos;
    private boolean exibirAcessosBloqueados;
    private boolean share;
    private Boolean apresentarAcessosNessaUnidade;

    public FiltroTotalizadorAcessosJSON(JSONObject filters) throws JSONException {
        if (filters != null) {
            this.empresa = filters.optInt("empresa");
            if(filters.optLong("periodoInicial") != 0) {
                this.periodoInicial = new Date(filters.optLong("periodoInicial"));
            }
            if(filters.optLong("periodoFinal") != 0) {
                this.periodoFinal = new Date(filters.optLong("periodoFinal"));
            }
            this.sexo = filters.optString("sexo").equals("T") ? "" : filters.optString("sexo");
            this.faixaEtariaInicial = filters.optInt("faixaEtariaInicial");
            this.faixaEtariaFinal = filters.optInt("faixaEtariaFinal");
            this.cliente = filters.optInt("cliente");
            this.colaborador = filters.optInt("colaborador");
            this.plano = filters.optInt("plano");
            this.modalidade = filters.optInt("modalidade");
            this.frequencia = filters.optString("frequencia");
            this.agruparPorPessoa = filters.optBoolean("agruparPorPessoa");
            this.considerarPrimeiroAcessoDia = filters.optBoolean("considerarPrimeiroAcessoDia");
            this.grafico = filters.optString("grafico");
            this.agrupamento = filters.optString("agrupamento");
            this.somenteTotalAcessos = filters.optBoolean("somenteTotalAcessos");
            this.exibirAcessosBloqueados = filters.optBoolean("exibirAcessosBloqueados");
            this.share = filters.optBoolean("share");
            this.apresentarAcessosNessaUnidade = filters.optBoolean("apresentarAcessosNessaUnidade");
        }
    }

    public Integer getEmpresa() {
        return empresa;
    }

    public void setEmpresa(Integer empresa) {
        this.empresa = empresa;
    }

    public Date getPeriodoInicial() {
        return periodoInicial;
    }

    public void setPeriodoInicial(Date periodoInicial) {
        this.periodoInicial = periodoInicial;
    }

    public Date getPeriodoFinal() {
        return periodoFinal;
    }

    public void setPeriodoFinal(Date periodoFinal) {
        this.periodoFinal = periodoFinal;
    }

    public Integer getFaixaEtariaInicial() {
        return faixaEtariaInicial;
    }

    public void setFaixaEtariaInicial(Integer faixaEtariaInicial) {
        this.faixaEtariaInicial = faixaEtariaInicial;
    }

    public Integer getFaixaEtariaFinal() {
        return faixaEtariaFinal;
    }

    public void setFaixaEtariaFinal(Integer faixaEtariaFinal) {
        this.faixaEtariaFinal = faixaEtariaFinal;
    }

    public Integer getPlano() {
        return plano;
    }

    public void setPlano(Integer plano) {
        this.plano = plano;
    }

    public Integer getModalidade() {
        return modalidade;
    }

    public void setModalidade(Integer modalidade) {
        this.modalidade = modalidade;
    }

    public Integer getCliente() {
        return cliente;
    }

    public void setCliente(Integer cliente) {
        this.cliente = cliente;
    }

    public Integer getColaborador() {
        return colaborador;
    }

    public void setColaborador(Integer colaborador) {
        this.colaborador = colaborador;
    }

    public String getFrequencia() {
        return frequencia;
    }

    public void setFrequencia(String frequencia) {
        this.frequencia = frequencia;
    }

    public Boolean getAgruparPorPessoa() {
        return agruparPorPessoa;
    }

    public void setAgruparPorPessoa(Boolean agruparPorPessoa) {
        this.agruparPorPessoa = agruparPorPessoa;
    }

    public Boolean getConsiderarPrimeiroAcessoDia() {
        return considerarPrimeiroAcessoDia;
    }

    public void setConsiderarPrimeiroAcessoDia(Boolean considerarPrimeiroAcessoDia) {
        this.considerarPrimeiroAcessoDia = considerarPrimeiroAcessoDia;
    }

    public String getGrafico() {
        return grafico;
    }

    public void setGrafico(String grafico) {
        this.grafico = grafico;
    }

    public String getSexo() {
        return sexo;
    }

    public void setSexo(String sexo) {
        this.sexo = sexo;
    }

    public String getAgrupamento() {
        return agrupamento;
    }

    public void setAgrupamento(String agrupamento) {
        this.agrupamento = agrupamento;
    }

    public boolean isSomenteTotalAcessos() {
        return somenteTotalAcessos;
    }

    public void setSomenteTotalAcessos(boolean somenteTotalAcessos) {
        this.somenteTotalAcessos = somenteTotalAcessos;
    }

    public boolean isExibirAcessosBloqueados() {
        return exibirAcessosBloqueados;
    }

    public void setExibirAcessosBloqueados(boolean exibirAcessosBloqueados) {
        this.exibirAcessosBloqueados = exibirAcessosBloqueados;
    }

    public boolean isShare() {
        return share;
    }

    public void setShare(boolean share) {
        this.share = share;
    }

    public Boolean getApresentarAcessosNessaUnidade() {
        return apresentarAcessosNessaUnidade;
    }

    public void setApresentarAcessosNessaUnidade(Boolean apresentarAcessosNessaUnidade) {
        this.apresentarAcessosNessaUnidade = apresentarAcessosNessaUnidade;
    }
}

