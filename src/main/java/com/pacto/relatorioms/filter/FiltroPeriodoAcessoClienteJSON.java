package com.pacto.relatorioms.filter;

import org.json.JSONException;
import org.json.JSONObject;

public class FiltroPeriodoAcessoClienteJSON {
    private String parametro;

    public FiltroPeriodoAcessoClienteJSON(JSONObject filters) throws JSONException {
        if (filters != null) {
            this.parametro = filters.optString("quicksearchValue").toUpperCase();
        }
    }

    public String getParametro() {
        return parametro;
    }

    public void setParametro(String parametro) {
        this.parametro = parametro;
    }
}

