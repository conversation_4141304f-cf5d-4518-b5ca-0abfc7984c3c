package com.pacto.relatorioms.filter;


import org.springframework.stereotype.Component;

import java.sql.Timestamp;

@Component
public class RelatorioGympassFilter {
    private Timestamp periodoPesquisaInicial;
    private Timestamp periodoPesquisaFinal;

    public Timestamp getPeriodoPesquisaInicial() {
        return periodoPesquisaInicial;
    }

    public void setPeriodoPesquisaInicial(Timestamp periodoPesquisaInicial) {
        this.periodoPesquisaInicial = periodoPesquisaInicial;
    }

    public Timestamp getPeriodoPesquisaFinal() {
        return periodoPesquisaFinal;
    }

    public void setPeriodoPesquisaFinal(Timestamp periodoPesquisaFinal) {
        this.periodoPesquisaFinal = periodoPesquisaFinal;
    }
}
