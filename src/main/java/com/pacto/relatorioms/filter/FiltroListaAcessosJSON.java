package com.pacto.relatorioms.filter;

import com.pacto.config.utils.Uteis;
import org.json.JSONException;
import org.json.JSONObject;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Date;

public class FiltroListaAcessosJSON {
    private Integer empresa;
    private Date periodoPesquisaInicial;
    private Date periodoPesquisaFinal;
    private String faixaHorariaInicial;
    private String faixaHorariaFinal;
    private String tipoListaAcesso; // CL || CO
    private Integer cliente;
    private Integer colaborador;
    private Integer plano;
    private Integer modalidade;
    private Integer professor<PERSON><PERSON><PERSON>;
    private Integer professor<PERSON><PERSON><PERSON><PERSON>;
    private Integer professor<PERSON><PERSON><PERSON>;
    private Integer grupo;
    private String ordenadoPor;
    private Boolean exibirSomentePrimeiroAcessoPorDia;
    private Boolean exibirAcessosBloqueados;
    private Boolean exibirMaisDetalhesCliente;
    private Boolean apresentarAcessosNessaUnidade;
    private String tipoArquivoExportar; // XLSX | PDF
    private Boolean ultimoContratoCliente = true;

    public FiltroListaAcessosJSON(JSONObject filters) throws JSONException {
        if (filters != null) {
            this.empresa = filters.optInt("empresa");
            if(filters.optLong("periodoPesquisaInicial") != 0) {
                this.periodoPesquisaInicial = new Date(filters.optLong("periodoPesquisaInicial"));
            }
            if(filters.optLong("periodoPesquisaFinal") != 0) {
                this.periodoPesquisaFinal = new Date(filters.optLong("periodoPesquisaFinal"));
            }
            this.faixaHorariaInicial = filters.optString("faixaHorariaInicial").isEmpty() ? "00:00" : filters.optString("faixaHorariaInicial");
            this.faixaHorariaFinal = filters.optString("faixaHorariaFinal").isEmpty() ? "23:59" : filters.optString("faixaHorariaFinal");
            this.tipoListaAcesso = filters.optString("tipoListaAcesso");
            this.cliente = filters.optInt("cliente");
            this.colaborador = filters.optInt("colaborador");
            this.plano = filters.optInt("plano");
            this.modalidade = filters.optInt("modalidade");
            this.professorTurma = filters.optInt("professorTurma");
            this.professorVinculo = filters.optInt("professorVinculo");
            this.professorTreino = filters.optInt("professorTreino");
            this.grupo = filters.optInt("grupo");
            this.ordenadoPor = filters.optString("ordenadoPor");
            this.exibirSomentePrimeiroAcessoPorDia = filters.optBoolean("exibirSomentePrimeiroAcessoPorDia");
            this.exibirAcessosBloqueados = filters.optBoolean("exibirAcessosBloqueados");
            this.exibirMaisDetalhesCliente = filters.optBoolean("exibirMaisDetalhesCliente");
            this.tipoArquivoExportar = filters.optString("tipoArquivoExportar");
            this.apresentarAcessosNessaUnidade = filters.optBoolean("apresentarAcessosNessaUnidade");
            if (filters.has("ultimoContratoCliente")) {
                this.ultimoContratoCliente = filters.optBoolean("ultimoContratoCliente");
            }
        }
    }

    public Integer getEmpresa() {
        return empresa;
    }

    public void setEmpresa(Integer empresa) {
        this.empresa = empresa;
    }

    public Date getPeriodoPesquisaInicial() {
        return periodoPesquisaInicial;
    }

    public void setPeriodoPesquisaInicial(Date periodoPesquisaInicial) {
        this.periodoPesquisaInicial = periodoPesquisaInicial;
    }

    public Date getPeriodoPesquisaFinal() {
        return periodoPesquisaFinal;
    }

    public void setPeriodoPesquisaFinal(Date periodoPesquisaFinal) {
        this.periodoPesquisaFinal = periodoPesquisaFinal;
    }

    public String getFaixaHorariaInicial() {
        return faixaHorariaInicial;
    }

    public void setFaixaHorariaInicial(String faixaHorariaInicial) {
        this.faixaHorariaInicial = faixaHorariaInicial;
    }

    public Integer getFaixaHorariaInicialEmMinutos() {
        Integer qtdMinutos = 0;
        try {
            if (this.faixaHorariaInicial.split(":").length > 0) {
                qtdMinutos = Integer.parseInt(this.faixaHorariaInicial.split(":")[0]) * 60; // horas em minutos
                qtdMinutos += Integer.parseInt(this.faixaHorariaInicial.split(":")[1]); // minutos
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return qtdMinutos;
    }

    public String getFaixaHorariaFinal() {
        return faixaHorariaFinal;
    }

    public void setFaixaHorariaFinal(String faixaHorariaFinal) {
        this.faixaHorariaFinal = faixaHorariaFinal;
    }

    public Integer getFaixaHorariaFinalEmMinutos() {
        Integer qtdMinutos = 1440;
        try {
            if (this.faixaHorariaFinal.split(":").length > 0) {
                qtdMinutos = Integer.parseInt(this.faixaHorariaFinal.split(":")[0]) * 60; // horas em minutos
                qtdMinutos += Integer.parseInt(this.faixaHorariaFinal.split(":")[1]); // minutos
            } else {
                qtdMinutos = 1440; // 24 horas em minutos
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return qtdMinutos;
    }

    public Integer getCliente() {
        return cliente;
    }

    public void setCliente(Integer cliente) {
        this.cliente = cliente;
    }

    public Integer getColaborador() {
        return colaborador;
    }

    public void setColaborador(Integer colaborador) {
        this.colaborador = colaborador;
    }

    public Integer getPlano() {
        return plano;
    }

    public void setPlano(Integer plano) {
        this.plano = plano;
    }

    public Integer getModalidade() {
        return modalidade;
    }

    public void setModalidade(Integer modalidade) {
        this.modalidade = modalidade;
    }

    public Integer getProfessorTurma() {
        return professorTurma;
    }

    public void setProfessorTurma(Integer professorTurma) {
        this.professorTurma = professorTurma;
    }

    public Integer getProfessorVinculo() {
        return professorVinculo;
    }

    public void setProfessorVinculo(Integer professorVinculo) {
        this.professorVinculo = professorVinculo;
    }

    public Integer getProfessorTreino() {
        return professorTreino;
    }

    public void setProfessorTreino(Integer professorTreino) {
        this.professorTreino = professorTreino;
    }

    public Integer getGrupo() {
        return grupo;
    }

    public void setGrupo(Integer grupo) {
        this.grupo = grupo;
    }

    public String getOrdenadoPor() {
        return ordenadoPor;
    }

    public void setOrdenadoPor(String ordenadoPor) {
        this.ordenadoPor = ordenadoPor;
    }

    public Boolean getExibirSomentePrimeiroAcessoPorDia() {
        return exibirSomentePrimeiroAcessoPorDia;
    }

    public void setExibirSomentePrimeiroAcessoPorDia(Boolean exibirSomentePrimeiroAcessoPorDia) {
        this.exibirSomentePrimeiroAcessoPorDia = exibirSomentePrimeiroAcessoPorDia;
    }

    public Boolean getExibirAcessosBloqueados() {
        return exibirAcessosBloqueados;
    }

    public void setExibirAcessosBloqueados(Boolean exibirAcessosBloqueados) {
        this.exibirAcessosBloqueados = exibirAcessosBloqueados;
    }

    public Boolean getExibirMaisDetalhesCliente() {
        return exibirMaisDetalhesCliente;
    }

    public void setExibirMaisDetalhesCliente(Boolean exibirMaisDetalhesCliente) {
        this.exibirMaisDetalhesCliente = exibirMaisDetalhesCliente;
    }

    public String getTipoListaAcesso() {
        return tipoListaAcesso;
    }

    public void setTipoListaAcesso(String tipoListaAcesso) {
        this.tipoListaAcesso = tipoListaAcesso;
    }

    public String getTipoArquivoExportar() {
        return tipoArquivoExportar;
    }

    public void setTipoArquivoExportar(String tipoArquivoExportar) {
        this.tipoArquivoExportar = tipoArquivoExportar;
    }

    public Boolean getApresentarAcessosNessaUnidade() {
        return apresentarAcessosNessaUnidade;
    }

    public void setApresentarAcessosNessaUnidade(Boolean apresentarAcessosNessaUnidade) {
        this.apresentarAcessosNessaUnidade = apresentarAcessosNessaUnidade;
    }

    public Boolean getUltimoContratoCliente() {
        if (ultimoContratoCliente == null) {
            ultimoContratoCliente = true;
        }
        return ultimoContratoCliente;
    }

    public void setUltimoContratoCliente(Boolean ultimoContratoCliente) {
        this.ultimoContratoCliente = ultimoContratoCliente;
    }
}

