package com.pacto.relatorioms.filter;

import org.json.JSONException;
import org.json.JSONObject;

import java.util.Date;

public class FiltroRelatorioDynamicsSesiJSON {
    private Integer empresa;
    private Date periodoPesquisa;

    public FiltroRelatorioDynamicsSesiJSON(JSONObject filters) throws JSONException {
        if (filters != null) {
            setEmpresa(filters.optInt("empresa"));

            if(filters.optLong("periodoPesquisa") != 0) {
                setPeriodoPesquisa(new Date(filters.optLong("periodoPesquisa")));
            }
        }
    }

    public Integer getEmpresa() {
        return empresa;
    }

    public void setEmpresa(Integer empresa) {
        this.empresa = empresa;
    }

    public Date getPeriodoPesquisa() {
        return periodoPesquisa;
    }

    public void setPeriodoPesquisa(Date periodoPesquisa) {
        this.periodoPesquisa = periodoPesquisa;
    }
}
