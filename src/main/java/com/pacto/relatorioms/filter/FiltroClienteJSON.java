package com.pacto.relatorioms.filter;

import org.json.JSONException;
import org.json.JSONObject;

public class FiltroClienteJSON {
    private String parametro;
    private Integer codigoEmpresa;

    public FiltroClienteJSON(JSONObject filters) throws JSONException {
        if (filters != null) {
            this.parametro = filters.optString("quicksearchValue").toUpperCase();
            this.codigoEmpresa = filters.optInt("empresa");
        }
    }

    public String getParametro() {
        return parametro;
    }

    public void setParametro(String parametro) {
        this.parametro = parametro;
    }

    public Integer getCodigoEmpresa() {
        return codigoEmpresa;
    }

    public void setCodigoEmpresa(Integer codigoEmpresa) {
        this.codigoEmpresa = codigoEmpresa;
    }
}

