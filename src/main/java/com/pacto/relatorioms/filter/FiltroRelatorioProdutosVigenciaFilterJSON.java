package com.pacto.relatorioms.filter;

import org.json.JSONObject;

import java.sql.Date;

public class FiltroRelatorioProdutosVigenciaFilterJSON {

    private String parametro;

    private Integer empresa;

    private Integer consultor;

    private Integer responsavel;

    private Integer produto;

    private String tipoProduto;

    private Date periodoCadastroDe;

    private Boolean clienteSemProduto;

    private Date periodoLançamentoDe;
    private Date periodoLançamentoAte;

    private Date periodoVencimentoDe;
    private Date periodoVencimentoAte;

    private Boolean aVencer;
    private Boolean ativo;

    private Boolean cancelado;

    private Boolean desistente;
    private Boolean inativo;

    private Boolean trancado;

    private Boolean vencido;
    private Boolean visitante;


    public FiltroRelatorioProdutosVigenciaFilterJSON(JSONObject filters) {
        if (filters != null) {
            this.parametro = filters.optString("quicksearchValue").toUpperCase();

            if (filters.optInt("empresa") != 0 && filters.optString("empresa") != null) {
                setEmpresa(filters.optInt("empresa"));
            }
            if (filters.optString("tipoProduto") != null && !filters.optString("tipoProduto").isEmpty()) {
                setTipoProduto(filters.getString("tipoProduto"));
            }
            if (filters.optInt("consultor") != 0) {
                setConsultor(filters.getInt("consultor"));
            }
            if (filters.optInt("responsavel") != 0) {
                setResponsavel(filters.getInt("responsavel"));
            }
            if (filters.optLong("periodoCadastroDe") != 0) {
                setPeriodoCadastroDe(new Date(filters.optLong("periodoCadastroDe")));
            }
            if(filters.optInt("produto") != 0 && filters.optString("produto") != null){
                setProduto(filters.getInt("produto"));
            }
            if (filters.optLong("periodoLancamentoDe") != 0) {
                setPeriodoLançamentoDe(new Date(filters.optLong("periodoLancamentoDe")));
            }
            if (filters.optLong("periodoLancamentoAte") != 0) {
                setPeriodoLançamentoAte(new Date(filters.optLong("periodoLancamentoAte")));
            }
            if (filters.optLong("periodoVencimentoDe") != 0) {
                setPeriodoVencimentoDe(new Date(filters.optLong("periodoVencimentoDe")));
            }
            if (filters.optLong("periodoVencimentoAte") != 0) {
                setPeriodoVencimentoAte(new Date(filters.optLong("periodoVencimentoAte")));
            }
            setClienteSemProduto(filters.optBoolean("clienteSemProduto"));
            setaVencer(filters.optBoolean("aVencer"));
            setAtivo(filters.optBoolean("ativo"));
            setCancelado(filters.optBoolean("cancelado"));
            setDesistente(filters.optBoolean("desistente"));
            setInativo(filters.optBoolean("inativo"));
            setTrancado(filters.optBoolean("trancado"));
            setVencido(filters.optBoolean("vencido"));
            setVisitante(filters.optBoolean("visitante"));
        }
    }

    public String getParametro() {
        return parametro;
    }

    public Integer getEmpresa() {
        return empresa;
    }

    public void setEmpresa(Integer empresa) {
        this.empresa = empresa;
    }

    public String getTipoProduto() {
        return tipoProduto;
    }

    public void setTipoProduto(String tipoProduto) {
        this.tipoProduto = tipoProduto;
    }

    public Integer getConsultor() {
        return consultor;
    }

    public void setConsultor(Integer consultor) {
        this.consultor = consultor;
    }

    public Integer getResponsavel() {
        return responsavel;
    }

    public void setResponsavel(Integer responsavel) {
        this.responsavel = responsavel;
    }

    public Integer getProduto() {
        return produto;
    }

    public void setProduto(Integer produto) {
        this.produto = produto;
    }

    public void setParametro(String parametro) {
        this.parametro = parametro;
    }

    public Date getPeriodoCadastroDe() {
        return periodoCadastroDe;
    }

    public void setPeriodoCadastroDe(Date periodoCadastroDe) {
        this.periodoCadastroDe = periodoCadastroDe;
    }

    public Boolean getClienteSemProduto() {
        return clienteSemProduto;
    }

    public void setClienteSemProduto(Boolean clienteSemProduto) {
        this.clienteSemProduto = clienteSemProduto;
    }

    public Date getPeriodoLançamentoDe() {
        return periodoLançamentoDe;
    }

    public void setPeriodoLançamentoDe(Date periodoLançamentoDe) {
        this.periodoLançamentoDe = periodoLançamentoDe;
    }

    public Date getPeriodoLançamentoAte() {
        return periodoLançamentoAte;
    }

    public void setPeriodoLançamentoAte(Date periodoLançamentoAte) {
        this.periodoLançamentoAte = periodoLançamentoAte;
    }

    public Date getPeriodoVencimentoDe() {
        return periodoVencimentoDe;
    }

    public void setPeriodoVencimentoDe(Date periodoVencimentoDe) {
        this.periodoVencimentoDe = periodoVencimentoDe;
    }

    public Date getPeriodoVencimentoAte() {
        return periodoVencimentoAte;
    }

    public void setPeriodoVencimentoAte(Date periodoVencimentoAte) {
        this.periodoVencimentoAte = periodoVencimentoAte;
    }

    public Boolean getaVencer() {
        return aVencer;
    }

    public void setaVencer(Boolean aVencer) {
        this.aVencer = aVencer;
    }

    public Boolean getAtivo() {
        return ativo;
    }

    public void setAtivo(Boolean ativo) {
        this.ativo = ativo;
    }

    public Boolean getCancelado() {
        return cancelado;
    }

    public void setCancelado(Boolean cancelado) {
        this.cancelado = cancelado;
    }

    public Boolean getDesistente() {
        return desistente;
    }

    public void setDesistente(Boolean desistente) {
        this.desistente = desistente;
    }

    public Boolean getInativo() {
        return inativo;
    }

    public void setInativo(Boolean inativo) {
        this.inativo = inativo;
    }

    public Boolean getTrancado() {
        return trancado;
    }

    public void setTrancado(Boolean trancado) {
        this.trancado = trancado;
    }

    public Boolean getVencido() {
        return vencido;
    }

    public void setVencido(Boolean vencido) {
        this.vencido = vencido;
    }

    public Boolean getVisitante() {
        return visitante;
    }

    public void setVisitante(Boolean visitante) {
        this.visitante = visitante;
    }
}
