package com.pacto.relatorioms.filter;

import org.json.JSONObject;

import java.sql.Date;

public class RelatorioArmarioFiltroJSON {
    private String parametro;
    
    private Date periodoLocacaoDe;
    private Date periodoLocacaoAte;

    private Date periodoRenovacaoDe;
    private Date periodoRenovacaoAte;

    private Date periodoVencimentoDe;
    private Date periodoVencimentoAte;

    private Integer planoLocacao;
    private Integer tamanhoArmarioSelecionado;
    private String tipoArmarioSelecionado;
    private String contratoAssinadoSTR;
    private Boolean contratoAssinado;
    private String numeroArmario;
    private Boolean somenteParcelasAtrasadas;
    private Boolean habilitadoGestaoArmarios;



    public RelatorioArmarioFiltroJSON(JSONObject filters) {
        if(filters != null) {
            this.parametro = filters.optString("quicksearchValue").toUpperCase();
            if (filters.optLong("periodoLocacaoDe") != 0) {
                setPeriodoLocacaoDe( new Date(filters.optLong("periodoLocacaoDe")));
            }
            if (filters.optLong("periodoLocacaoAte") != 0) {
                setPeriodoLocacaoAte( new Date(filters.optLong("periodoLocacaoAte")));
            }
            if (filters.optLong("periodoRenovacaoDe") != 0) {
                setPeriodoRenovacaoDe( new Date(filters.optLong("periodoRenovacaoDe")));
            }
            if (filters.optLong("periodoRenovacaoAte") != 0) {
                setPeriodoRenovacaoAte( new Date(filters.optLong("periodoRenovacaoAte")));
            }
            if (filters.optLong("periodoVencimentoDe") != 0) {
                setPeriodoVencimentoDe( new Date(filters.optLong("periodoVencimentoDe")));
            }
            if (filters.optLong("periodoVencimentoAte") != 0) {
                setPeriodoVencimentoAte( new Date(filters.optLong("periodoVencimentoAte")));
            }
            setPlanoLocacao(filters.optInt("planoLocacao"));
            setTamanhoArmarioSelecionado(filters.optInt("tamanhoArmarioSelecionado"));
            setTipoArmarioSelecionado(filters.optString("tipoArmarioSelecionado"));
            setContratoAssinado(convertContratosAssinadosToBoolean(filters.optString("contratoAssinadoSTR")));
            setNumeroArmario(filters.optString("numeroArmario"));
            setSomenteParcelasAtrasadas(filters.optBoolean("somenteParcelasAtrasadas"));
            setHabilitadoGestaoArmarios(filters.optBoolean("habilitadoGestaoArmarios"));
        }
    }

    public Boolean convertContratosAssinadosToBoolean(String contratoAssinadoSTR) {
        if (contratoAssinadoSTR.equals("S")) {
            return true;
        } else if (contratoAssinadoSTR.equals("N")) {
           return false;
        } else {
            return null;
        }
    }

    public String getParametro() {
        return parametro;
    }

    public void setParametro(String parametro) {
        this.parametro = parametro;
    }

    public Date getPeriodoLocacaoDe() {
        return periodoLocacaoDe;
    }

    public void setPeriodoLocacaoDe(Date periodoLocacaoDe) {
        this.periodoLocacaoDe = periodoLocacaoDe;
    }

    public Date getPeriodoLocacaoAte() {
        return periodoLocacaoAte;
    }

    public void setPeriodoLocacaoAte(Date periodoLocacaoAte) {
        this.periodoLocacaoAte = periodoLocacaoAte;
    }

    public Date getPeriodoRenovacaoDe() {
        return periodoRenovacaoDe;
    }

    public void setPeriodoRenovacaoDe(Date periodoRenovacaoDe) {
        this.periodoRenovacaoDe = periodoRenovacaoDe;
    }

    public Date getPeriodoRenovacaoAte() {
        return periodoRenovacaoAte;
    }

    public void setPeriodoRenovacaoAte(Date periodoRenovacaoAte) {
        this.periodoRenovacaoAte = periodoRenovacaoAte;
    }

    public Date getPeriodoVencimentoDe() {
        return periodoVencimentoDe;
    }

    public void setPeriodoVencimentoDe(Date periodoVencimentoDe) {
        this.periodoVencimentoDe = periodoVencimentoDe;
    }

    public Date getPeriodoVencimentoAte() {
        return periodoVencimentoAte;
    }

    public void setPeriodoVencimentoAte(Date periodoVencimentoAte) {
        this.periodoVencimentoAte = periodoVencimentoAte;
    }

    public Integer getPlanoLocacao() {
        return planoLocacao;
    }

    public void setPlanoLocacao(Integer planoLocacao) {
        this.planoLocacao = planoLocacao;
    }

    public Integer getTamanhoArmarioSelecionado() {
        return tamanhoArmarioSelecionado;
    }

    public void setTamanhoArmarioSelecionado(Integer tamanhoArmarioSelecionado) {
        this.tamanhoArmarioSelecionado = tamanhoArmarioSelecionado;
    }

    public String getTipoArmarioSelecionado() {
        return tipoArmarioSelecionado;
    }

    public void setTipoArmarioSelecionado(String tipoArmarioSelecionado) {
        this.tipoArmarioSelecionado = tipoArmarioSelecionado;
    }

    public Boolean getContratoAssinado() {
        return contratoAssinado;
    }

    public void setContratoAssinado(Boolean contratoAssinado) {
        this.contratoAssinado = contratoAssinado;
    }

    public String getNumeroArmario() {
        return numeroArmario;
    }

    public void setNumeroArmario(String numeroArmario) {
        this.numeroArmario = numeroArmario;
    }

    public Boolean getSomenteParcelasAtrasadas() {
        return somenteParcelasAtrasadas;
    }

    public void setSomenteParcelasAtrasadas(Boolean somenteParcelasAtrasadas) {
        this.somenteParcelasAtrasadas = somenteParcelasAtrasadas;
    }

    public Boolean getHabilitadoGestaoArmarios() {
        return habilitadoGestaoArmarios;
    }

    public void setHabilitadoGestaoArmarios(Boolean habilitadoGestaoArmarios) {
        this.habilitadoGestaoArmarios = habilitadoGestaoArmarios;
    }

    public String getContratoAssinadoSTR() {
        return contratoAssinadoSTR;
    }

    public void setContratoAssinadoSTR(String contratoAssinadoSTR) {
        this.contratoAssinadoSTR = contratoAssinadoSTR;
    }
}
