package com.pacto.relatorioms.filter;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;
import java.util.ArrayList;
import java.util.List;

public class FiltroColaboradorJSON {
    private String parametroBusca;
    private Integer empresa;
    private List<String> tipoColaborador = new ArrayList<String>();

    public FiltroColaboradorJSON(JSONObject filters) throws JSONException {
        if (filters != null) {
            this.parametroBusca = filters.optString("searchTerm").toUpperCase();
            this.empresa = filters.optInt("empresa");
            if (filters.optJSONArray("tipoColaborador") != null) {
                filters.optJSONArray("tipoColaborador").forEach(
                        (tipos) -> {
                            this.tipoColaborador.add(tipos.toString());
                        }
                );
            }
        }
    }

    public String getParametroBusca() {
        return parametroBusca;
    }

    public void setParametroBusca(String parametroBusca) {
        this.parametroBusca = parametroBusca;
    }

    public Integer getEmpresa() {
        return empresa;
    }

    public void setEmpresa(Integer empresa) {
        this.empresa = empresa;
    }

    public List<String> getTipoColaborador() {
        if (tipoColaborador == null) {
            tipoColaborador = new ArrayList<String>();
        }
        return tipoColaborador;
    }

    public void setTipoColaborador(List<String> tipoColaborador) {
        this.tipoColaborador = tipoColaborador;
    }
}

