package com.pacto.relatorioms.filter;

import org.json.JSONException;
import org.json.JSONObject;

import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

public class FiltroRelatorioPersonalJSON {
    private Integer empresa;
    private Date mesReferencia;
    private Boolean negociado;
    private Boolean pago;
    private Boolean vencido;
    private Boolean livre;
    private List<Integer> personais = new ArrayList<>();

    public FiltroRelatorioPersonalJSON(JSONObject filters) throws JSONException {
        if (filters != null) {
            setEmpresa(filters.optInt("empresa"));
            if(filters.optLong("mesReferencia") != 0) {
                setMesReferencia(new Date(filters.optLong("mesReferencia")));
            }
            setNegociado(filters.optBoolean("negociado"));
            setPago(filters.optBoolean("pago"));
            setVencido(filters.optBoolean("vencido"));
            setLivre(filters.optBoolean("livre"));
            if(!filters.optString("personais").isEmpty()) {
                for(String p: filters.optString("personais").split(",")) {
                    getPersonais().add(Integer.parseInt(p));
                }
            }
        }
    }

    public Integer getEmpresa() {
        return empresa;
    }

    public void setEmpresa(Integer empresa) {
        this.empresa = empresa;
    }

    public Date getMesReferencia() {
        return mesReferencia;
    }

    public void setMesReferencia(Date mesReferencia) {
        this.mesReferencia = mesReferencia;
    }

    public Boolean getNegociado() {
        return negociado;
    }

    public void setNegociado(Boolean negociado) {
        this.negociado = negociado;
    }

    public Boolean getPago() {
        return pago;
    }

    public void setPago(Boolean pago) {
        this.pago = pago;
    }

    public Boolean getVencido() {
        return vencido;
    }

    public void setVencido(Boolean vencido) {
        this.vencido = vencido;
    }

    public Boolean getLivre() {
        return livre;
    }

    public void setLivre(Boolean livre) {
        this.livre = livre;
    }

    public List<Integer> getPersonais() {
        return personais;
    }

    public void setPersonais(List<Integer> personais) {
        this.personais = personais;
    }

    public String getMesReferenciaStr() {
        if(this.mesReferencia != null) {
            LocalDate localDate = this.mesReferencia.toInstant()
                    .atZone(ZoneId.systemDefault())
                    .toLocalDate();
            String mes = localDate.getMonth().getValue() < 10 ? "0" + localDate.getMonth().getValue() : localDate.getMonth().getValue()+"";
            return mes + "/" + localDate.getYear();
        } else {
            return "";
        }
    }

    public boolean filtraPorSituacao() {
        // Se todas as situacoes estiverem marcadas ou se todas estiverem desmarcadas, não será necessário filtrar
        Boolean todasEstaoDesmacadas = !this.getNegociado() && !this.getPago() && !this.getVencido() && !this.getLivre();
        Boolean todasEstaoMarcadas = this.getNegociado() && this.getPago() && this.getVencido() && this.getLivre();
        return !todasEstaoDesmacadas && !todasEstaoDesmacadas;
    }
}

