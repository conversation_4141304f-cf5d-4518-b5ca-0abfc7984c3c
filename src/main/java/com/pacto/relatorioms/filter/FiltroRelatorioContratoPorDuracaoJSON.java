package com.pacto.relatorioms.filter;

import com.pacto.relatorioms.enums.SituacaoClienteEnum;
import org.json.JSONException;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class FiltroRelatorioContratoPorDuracaoJSON {
    private Integer empresa;
    private Date data;
    private List<SituacaoClienteEnum> situacoes;
    private Boolean semBolsa;
    private Boolean contabilizarPlanosAutoRenovaveis;
    private Boolean share;
    private String descricao;
    private String quickSearchValue;
    private Integer planoTipo;

    public FiltroRelatorioContratoPorDuracaoJSON(JSONObject filters) throws JSONException {
        if (filters != null) {
            this.empresa = filters.optInt("empresa");
            this.semBolsa = filters.optBoolean("semBolsa");
            this.share = filters.optBoolean("share");
            this.contabilizarPlanosAutoRenovaveis = filters.optBoolean("contabilizarPlanosAutoRenovaveis");
            if(filters.optLong("data") != 0) {
                this.data = new Date(filters.optLong("data"));
            }
            this.situacoes = new ArrayList<>();
            filters.optJSONArray("situacoes").forEach(s -> {
                situacoes.add(SituacaoClienteEnum.getSituacaoCliente(s.toString()));
            });
            this.descricao = filters.optString("descricao");
            this.quickSearchValue = filters.optString("quicksearchValue");
            this.planoTipo = filters.optInt("planoTipo");
        }
    }

    public Integer getEmpresa() {
        return empresa;
    }

    public void setEmpresa(Integer empresa) {
        this.empresa = empresa;
    }

    public Date getData() {
        return data;
    }

    public void setData(Date data) {
        this.data = data;
    }

    public List<SituacaoClienteEnum> getSituacoes() {
        return situacoes;
    }

    public void setSituacoes(List<SituacaoClienteEnum> situacoes) {
        this.situacoes = situacoes;
    }

    public Boolean getSemBolsa() {
        return semBolsa;
    }

    public void setSemBolsa(Boolean semBolsa) {
        this.semBolsa = semBolsa;
    }

    public Boolean getContabilizarPlanosAutoRenovaveis() {
        return contabilizarPlanosAutoRenovaveis;
    }

    public void setContabilizarPlanosAutoRenovaveis(Boolean contabilizarPlanosAutoRenovaveis) {
        this.contabilizarPlanosAutoRenovaveis = contabilizarPlanosAutoRenovaveis;
    }

    public Boolean getShare() {
        return share;
    }

    public void setShare(Boolean share) {
        this.share = share;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public String getQuickSearchValue() {
        return quickSearchValue;
    }

    public void setQuickSearchValue(String quickSearchValue) {
        this.quickSearchValue = quickSearchValue;
    }

    public Integer getPlanoTipo() {
        return planoTipo;
    }

    public void setPlanoTipo(Integer planoTipo) {
        this.planoTipo = planoTipo;
    }
}

