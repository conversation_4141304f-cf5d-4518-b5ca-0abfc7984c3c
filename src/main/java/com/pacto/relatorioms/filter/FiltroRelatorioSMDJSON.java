package com.pacto.relatorioms.filter;

import org.json.JSONException;
import org.json.JSONObject;

import java.util.Date;

public class FiltroRelatorioSMDJSON {
    private Integer empresa;
    private Date periodoPesquisaInicial;
    private Date periodoPesquisaFinal;

    public FiltroRelatorioSMDJSON(JSONObject filters) throws JSONException {
        if (filters != null) {
            setEmpresa(filters.optInt("empresa"));
            if(filters.optLong("periodoPesquisaInicial") != 0) {
                setPeriodoPesquisaInicial(new Date(filters.optLong("periodoPesquisaInicial")));
            }
            if(filters.optLong("periodoPesquisaFinal") != 0) {
                setPeriodoPesquisaFinal(new Date(filters.optLong("periodoPesquisaFinal")));
            }
        }
    }

    public Integer getEmpresa() {
        return empresa;
    }

    public void setEmpresa(Integer empresa) {
        this.empresa = empresa;
    }

    public Date getPeriodoPesquisaInicial() {
        return periodoPesquisaInicial;
    }

    public void setPeriodoPesquisaInicial(Date periodoPesquisaInicial) {
        this.periodoPesquisaInicial = periodoPesquisaInicial;
    }

    public Date getPeriodoPesquisaFinal() {
        return periodoPesquisaFinal;
    }

    public void setPeriodoPesquisaFinal(Date periodoPesquisaFinal) {
        this.periodoPesquisaFinal = periodoPesquisaFinal;
    }
}
