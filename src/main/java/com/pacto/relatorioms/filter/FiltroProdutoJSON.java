package com.pacto.relatorioms.filter;

import org.json.JSONException;
import org.json.JSONObject;

public class FiltroProdutoJSON {
    private String parametroBusca;
    private Integer empresa;
    private String tipoProduto;

    public FiltroProdutoJSON(JSONObject filters) throws JSONException {
        if (filters != null) {
            this.parametroBusca = filters.optString("searchTerm").toUpperCase();
            this.empresa = filters.optInt("empresa");
            if (filters.optString("tipoProduto") != null) {
                setTipoProduto(filters.getString("tipoProduto"));;
            }
        }
    }

    public String getParametroBusca() {
        return parametroBusca;
    }

    public void setParametroBusca(String parametroBusca) {
        this.parametroBusca = parametroBusca;
    }

    public Integer getEmpresa() {
        return empresa;
    }

    public void setEmpresa(Integer empresa) {
        this.empresa = empresa;
    }

    public String getTipoProduto() {
        return tipoProduto;
    }

    public void setTipoProduto(String tipoProduto) {
        this.tipoProduto = tipoProduto;
    }
}
