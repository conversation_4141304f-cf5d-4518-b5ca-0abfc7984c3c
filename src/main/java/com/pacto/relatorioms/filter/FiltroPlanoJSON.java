package com.pacto.relatorioms.filter;

import org.json.JSONException;
import org.json.JSONObject;

public class FiltroPlanoJSON {
    private String parametro;
    private Integer empresa;

    public FiltroPlanoJSON(JSONObject filters) throws JSONException {
        if (filters != null) {
            this.parametro = filters.optString("quicksearchValue").toUpperCase();
            this.empresa = filters.optInt("empresa");
        }
    }

    public String getParametro() {
        return parametro;
    }

    public void setParametro(String parametro) {
        this.parametro = parametro;
    }

    public Integer getEmpresa() {
        return empresa;
    }

    public void setEmpresa(Integer empresa) {
        this.empresa = empresa;
    }
}

