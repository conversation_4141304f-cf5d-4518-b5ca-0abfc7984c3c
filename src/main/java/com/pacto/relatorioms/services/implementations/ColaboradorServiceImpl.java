package com.pacto.relatorioms.services.implementations;

import com.pacto.relatorioms.adapters.ColaboradorAdapter;
import com.pacto.relatorioms.dao.interfaces.ColaboradorDao;
import com.pacto.relatorioms.dto.ColaboradorDTO;
import com.pacto.config.dto.PaginadorDTO;
import com.pacto.relatorioms.entities.Colaborador;
import com.pacto.config.exceptions.ServiceException;
import com.pacto.relatorioms.filter.FiltroColaboradorJSON;
import com.pacto.config.security.interfaces.RequestService;
import com.pacto.relatorioms.services.interfaces.ColaboradorService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;


@Service
public class ColaboradorServiceImpl implements ColaboradorService {

    @Autowired
    private ColaboradorDao colaboradorDao;
    @Autowired
    private ColaboradorAdapter colaboradorAdapter;

    private final RequestService requestService;

    public ColaboradorServiceImpl(RequestService requestService) {
        this.requestService = requestService;
    }


    @Override
    public List<ColaboradorDTO> findAll(FiltroColaboradorJSON filtroJSON, PaginadorDTO paginadorDTO) throws ServiceException {
        try {
            List<Colaborador> colaboradores = colaboradorDao.findAll(filtroJSON, paginadorDTO);
            List<ColaboradorDTO> colaboradoresDtos = colaboradorAdapter.toDtos(colaboradores);
            return colaboradoresDtos;
        } catch (Exception e) {
            e.printStackTrace();
            throw new ServiceException(e);
        }
    }

    @Override
    public List<ColaboradorDTO> findAllByEmpresaId(Integer empresaId, FiltroColaboradorJSON filtros) throws ServiceException {
        try {
            List<Colaborador> colaboradores = colaboradorDao.findAllByEmpresaId(empresaId, filtros);
            List<ColaboradorDTO> colaboradoresDtos = colaboradorAdapter.toDtos(colaboradores);
            return colaboradoresDtos;
        } catch (Exception e) {
            e.printStackTrace();
            throw new ServiceException(e);
        }
    }

    @Override
    public List<ColaboradorDTO> findAll(FiltroColaboradorJSON filtros) throws ServiceException {
        try {
            List<Colaborador> colaboradores = colaboradorDao.findAll(filtros);
            List<ColaboradorDTO> colaboradoresDtos = colaboradorAdapter.toDtos(colaboradores);
            return colaboradoresDtos;
        } catch (Exception e) {
            e.printStackTrace();
            throw new ServiceException(e);
        }
    }

}
