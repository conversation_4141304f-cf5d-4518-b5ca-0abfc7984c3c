package com.pacto.relatorioms.services.implementations;

import com.pacto.config.security.interfaces.RequestService;
import com.pacto.config.utils.Uteis;
import com.pacto.relatorioms.dao.interfaces.RelatorioSMDDao;
import com.pacto.config.exceptions.ServiceException;
import com.pacto.relatorioms.filter.FiltroRelatorioSMDJSON;
import com.pacto.relatorioms.filter.RelatorioSMDFilter;
import com.pacto.relatorioms.services.interfaces.RelatorioSMDService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import java.io.BufferedWriter;
import java.io.File;
import java.io.FileWriter;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.List;

@Service
public class RelatorioSMDImpl implements RelatorioSMDService {

    @Autowired
    private RelatorioSMDDao relatorioSMDDao;
    @Autowired
    private RequestService requestService;

    public RelatorioSMDImpl(RequestService requestService) {
        this.requestService = requestService;
    }

    @Override
    public String gerarArquivoTexto(FiltroRelatorioSMDJSON filtroJSON, HttpServletRequest request) throws ServiceException {
        try {
            RelatorioSMDFilter relatorioSMDFilter = new RelatorioSMDFilter();
            List<String> relatorioSMDDTOS = relatorioSMDDao.gerarLinhasDoArquivoTexto(filtroJSON, relatorioSMDFilter);

            File arquivo = criarArquivo(filtroJSON.getPeriodoPesquisaInicial(), filtroJSON.getPeriodoPesquisaFinal(), request);

            AdicionarLinhasDeTextoNoArquivo(relatorioSMDDTOS, arquivo.getPath());

            return arquivo.getName();
        } catch (Exception e) {
            e.printStackTrace();
            throw new ServiceException(e);
        }
    }

    private File criarArquivo(Date dataInicial, Date dataFinal, HttpServletRequest request) throws Exception {

        String nome = "SMD_"
                + ExtrairDataFormatada(dataInicial)
                + "_ATE_"
                + ExtrairDataFormatada(dataFinal)
                + ".txt";

        File txtFolder = new File(Uteis.obterCaminhoWebAplicacao(request) + File.separator + "temp");
        txtFolder.mkdirs();
        File arquivo = new File(txtFolder.getPath() + File.separator + nome);

        if (arquivo.exists()) {
            arquivo.delete();
        }

        arquivo.createNewFile();

        return arquivo;
    }

    private String ExtrairDataFormatada(Date data){

        LocalDate localDate = data.toInstant()
                .atZone(ZoneId.systemDefault())
                .toLocalDate();
        return localDate.format(DateTimeFormatter.ISO_LOCAL_DATE); // "YYYY-MM-DD"
    }

    private void AdicionarLinhasDeTextoNoArquivo(List<String> linhas, String caminhoArquivo) {

        try (BufferedWriter writer = new BufferedWriter(new FileWriter(caminhoArquivo, true))) {
            for (String linha : linhas) {
                writer.write(linha);
                writer.newLine();
            }
        } catch (Exception e) {
        }
    }
}
