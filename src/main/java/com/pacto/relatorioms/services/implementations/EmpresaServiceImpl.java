package com.pacto.relatorioms.services.implementations;

import com.pacto.relatorioms.adapters.EmpresaAdapter;
import com.pacto.relatorioms.dao.interfaces.EmpresaDao;
import com.pacto.relatorioms.dto.EmpresaDTO;
import com.pacto.relatorioms.entities.empresa.Empresa;
import com.pacto.relatorioms.filter.FiltroEmpresaJSON;
import com.pacto.config.security.interfaces.RequestService;
import com.pacto.relatorioms.services.interfaces.EmpresaService;
import org.springframework.stereotype.Service;

import java.util.*;

@Service
public class EmpresaServiceImpl implements EmpresaService {

    private final EmpresaDao empresaDao;
    private final EmpresaAdapter empresaAdapter;
    private final RequestService requestService;

    public EmpresaServiceImpl(EmpresaDao empresaDao, EmpresaAdapter empresaAdapter, RequestService requestService) {
        this.empresaDao = empresaDao;
        this.empresaAdapter = empresaAdapter;
        this.requestService = requestService;
    }

    @Override
    public List<EmpresaDTO> findAllActives(FiltroEmpresaJSON filtros) throws Exception {
        List<Empresa> empresas = empresaDao.findAllActives(filtros);
        List<EmpresaDTO> empresaDTOS = empresaAdapter.toDtos(empresas);
        return empresaDTOS;
    }

    @Override
    public EmpresaDTO findById(Integer id) throws Exception {
        Empresa empresa = empresaDao.findById(id);
        return empresaAdapter.toDto(empresa);
    }
}
