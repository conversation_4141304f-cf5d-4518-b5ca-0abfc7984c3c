package com.pacto.relatorioms.services.implementations;

import com.pacto.relatorioms.adapters.PeriodoAcessoClienteAdapter;
import com.pacto.relatorioms.dao.interfaces.PeriodoAcessoClienteDao;
import com.pacto.relatorioms.dto.PeriodoAcessoClienteDTO;
import com.pacto.config.dto.PaginadorDTO;
import com.pacto.relatorioms.entities.PeriodoAcessoCliente;
import com.pacto.config.security.interfaces.RequestService;
import com.pacto.relatorioms.services.interfaces.PeriodoAcessoClienteService;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class PeriodoAcessoClienteServiceImpl implements PeriodoAcessoClienteService {

    private final RequestService requestService;
    private final PeriodoAcessoClienteDao paDao;
    private final PeriodoAcessoClienteAdapter paAdapter;

    public PeriodoAcessoClienteServiceImpl(RequestService requestService, PeriodoAcessoClienteDao paDao, PeriodoAcessoClienteAdapter paAdapter) {
        this.requestService = requestService;
        this.paDao = paDao;
        this.paAdapter = paAdapter;
    }

    @Override
    public List<PeriodoAcessoClienteDTO> findAllByMatriculaRelGympass(Integer matricula, PaginadorDTO paginadorDTO) throws Exception {
        List<PeriodoAcessoCliente> pa = paDao.findAllByMatriculaRelGympass(matricula, paginadorDTO);
        List<PeriodoAcessoClienteDTO> paDTOs = paAdapter.toDtos(pa);
        return paDTOs;
    }

    @Override
    public List<PeriodoAcessoClienteDTO> findAllByMatriculaRelGogood(Integer matricula, PaginadorDTO paginadorDTO) throws Exception {
        List<PeriodoAcessoCliente> pa = paDao.findAllByMatriculaRelGogood(matricula, paginadorDTO);
        List<PeriodoAcessoClienteDTO> paDTOs = paAdapter.toDtos(pa);
        return paDTOs;
    }

}
