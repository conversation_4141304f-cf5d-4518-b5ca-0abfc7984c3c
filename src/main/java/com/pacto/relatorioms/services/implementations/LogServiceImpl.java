package com.pacto.relatorioms.services.implementations;

import com.pacto.relatorioms.adapters.LogAdapter;
import com.pacto.config.annotations.NomeEntidadeLog;
import com.pacto.config.annotations.NotLogged;
import com.pacto.config.annotations.UseOnlyThisToLog;
import com.pacto.relatorioms.dao.interfaces.LogDao;
import com.pacto.relatorioms.entities.Log;
import com.pacto.config.security.interfaces.RequestService;
import com.pacto.relatorioms.services.interfaces.LogService;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import javax.persistence.*;
import java.lang.reflect.Field;
import java.lang.reflect.ParameterizedType;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

@Service
public class LogServiceImpl implements LogService {

    private final LogDao logDao;
    private final LogAdapter logAdapter;
    private final RequestService requestService;
    private List<Log> logs;
    private String chavePrimaria = "";
    private String operacao = "";
    private Field[] fieldsMarkedUseOnlyThisToLog = new Field[]{};

    public LogServiceImpl(LogDao logDao, LogAdapter logAdapter, RequestService requestService) {
        this.logs = new ArrayList<>();
        this.logDao = logDao;
        this.logAdapter = logAdapter;
        this.requestService = requestService;
    }

    public void incluirLogInclusaoAlteracao(Object objectAlterado, Object objectAnterior, String nomeEntidade, String nomeEntidadeDescricao) throws Exception {
        try {
            this.gerarLogInclusaoAlteracao(objectAlterado, objectAnterior, nomeEntidade, nomeEntidadeDescricao);
            this.saveLogs();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void incluirLogExcluscao(Object objectAlterado, String nomeEntidade, String nomeEntidadeDescricao) throws Exception {
        try {
            this.gerarLogInclusaoAlteracao(objectAlterado, null, nomeEntidade, nomeEntidadeDescricao);
            this.saveLogs("EXCLUSÃO");
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void gerarLogInclusaoAlteracao(Object objetoAlterado, Object objetoAnterior, String nomeEntidade, String nomeEntidadeDescricao) throws Exception {
        gerarLogInclusaoAlteracao(objetoAlterado, objetoAnterior, nomeEntidade, nomeEntidadeDescricao, "", "");
    }

    private void gerarLogInclusaoAlteracao(Object objetoAlterado, Object objetoAnterior, String nomeEntidade, String nomeEntidadeDescricao, String chavePrimariaEntidadeSubordinada, String nomeCampo) throws Exception {
        gerarLogInclusaoAlteracao(objetoAlterado, objetoAnterior, null, nomeEntidade, nomeEntidadeDescricao, chavePrimariaEntidadeSubordinada, nomeCampo);
    }

    private void gerarLogInclusaoAlteracao(Object objetoAlterado, Object objetoAnterior, String nomeEntidadeMae, String nomeEntidade, String nomeEntidadeDescricao, String chavePrimariaEntidadeSubordinada, String nomeCampo) throws Exception {

        if (objetoAlterado == null)
            throw new Exception("Objetos alterado não pode ser nulo!");
        if (objetoAnterior == null)
            objetoAnterior = objetoAlterado.getClass().newInstance();

        if (!objetoAlterado.getClass().getSimpleName().equals(objetoAnterior.getClass().getSimpleName()))
            throw new Exception("Objetos de classes diferentes!");

        if (!objetoAlterado.getClass().isAnnotationPresent(Entity.class))
            throw new Exception("Objeto alterado não é uma entidade!");

        if (!objetoAnterior.getClass().isAnnotationPresent(Entity.class))
            throw new Exception("Objeto anterior não é uma entidade!");

        if (nomeEntidadeMae == null) nomeEntidadeMae = nomeEntidade;

        Field[] fields = fieldsMarkedUseOnlyThisToLog.length > 0 ? fieldsMarkedUseOnlyThisToLog : objetoAlterado.getClass().getDeclaredFields();
        fieldsMarkedUseOnlyThisToLog = new Field[]{};

        if (chavePrimaria.isEmpty()) {
            chavePrimaria = getChavePrimariaObjeto(objetoAlterado, objetoAnterior);
        }
        Log log = new Log();
        for (Field field : fields) {
            if (field.isAnnotationPresent(NotLogged.class)) {
                continue;
            }
            if (field.isAnnotationPresent(Id.class)) {
                continue;
            }
            log = new Log();
            field.setAccessible(true);
            if (
                    (field.isAnnotationPresent(ManyToMany.class) || field.isAnnotationPresent(OneToMany.class))
            ) {
                gerarLogLista((Collection<?>) field.get(objetoAnterior), (Collection<?>) field.get(objetoAlterado), nomeEntidadeMae, getNomeEntidade(nomeEntidadeMae, field), field.getName());
            } else if (field.isAnnotationPresent(OneToOne.class) || field.isAnnotationPresent(ManyToOne.class)) {
                if (field.get(objetoAlterado) == null && field.get(objetoAnterior) == null) {
                    continue;
                }
                gerarLogObject(field.get(objetoAnterior), field.get(objetoAlterado), nomeEntidadeMae, getNomeEntidade(nomeEntidadeMae, field), field.getName());
            } else {
                log.setChavePrimaria(chavePrimaria);
                log.setOperacao(operacao);
                if (field.isAnnotationPresent(Transient.class)) {
                    continue;
                }
                if (field.get(objetoAlterado) == null || field.get(objetoAlterado).equals("")) {
                    continue;
                }
                if (!field.get(objetoAlterado).equals(field.get(objetoAnterior)) || operacao.equals("EXCLUSÃO")) {
                    log.setNomeEntidade(nomeEntidade);
                    log.setNomeEntidadeDescricao(nomeEntidadeMae + " - " + nomeEntidadeDescricao);
                    log.setDataAlteracao(new Date());
                    log.setResponsavelAlteracao(requestService.getUsuarioAtual().getUsername());
                    if (!nomeCampo.isEmpty()) {
                        log.setNomeCampo(nomeCampo);
                    } else {
                        log.setNomeCampo(field.getName().toLowerCase());
                    }
                    log.setChavePrimariaEntidadeSubordinada(chavePrimariaEntidadeSubordinada);
                    if (field.get(objetoAnterior) == null) {
                        log.setValorCampoAnterior("");
                    } else {
                        if (field.get(objetoAnterior) instanceof Double) {
                            log.setValorCampoAnterior(BigDecimal.valueOf((Double) field.get(objetoAnterior)).setScale(2, RoundingMode.HALF_UP).toString());
                        } else if (field.get(objetoAnterior) instanceof Date) {
                            SimpleDateFormat formato = new SimpleDateFormat("dd/MM/yyyy");
                            String data = formato.format(field.get(objetoAnterior));
                            log.setValorCampoAnterior(data);
                        } else {
                            log.setValorCampoAnterior(field.get(objetoAnterior).toString());
                        }
                    }
                    if (field.get(objetoAlterado) == null) {
                        log.setValorCampoAlterado("");
                    } else {
                        if (field.get(objetoAlterado) instanceof Double) {
                            log.setValorCampoAlterado(BigDecimal.valueOf((Double) field.get(objetoAlterado)).setScale(2, RoundingMode.HALF_UP).toString());
                        } else if (field.get(objetoAlterado) instanceof Date) {
                            SimpleDateFormat formato = new SimpleDateFormat("dd/MM/yyyy");
                            String data = formato.format(field.get(objetoAlterado));
                            log.setValorCampoAlterado(data);
                        } else {
                            log.setValorCampoAlterado(field.get(objetoAlterado).toString());
                        }
                    }
                    logs.add(log);
                }
            }
            field.setAccessible(false);
        }
    }

    private void gerarLogObject(Object anterior, Object alterado, String nomeEntidadeMae, String nomeEntidadeDescricao, String nomeCampo) throws IllegalAccessException, InstantiationException, ClassNotFoundException {

        if (anterior == null) {
            anterior = alterado.getClass().newInstance();
        }

        Log log = new Log();
        log.setChavePrimaria(chavePrimaria);
        log.setOperacao(operacao);
        log.setNomeEntidade(nomeEntidadeMae);
        log.setNomeEntidadeDescricao(nomeEntidadeDescricao);
        log.setDataAlteracao(new Date());
        log.setResponsavelAlteracao(requestService.getUsuarioAtual().getUsername());
        log.setNomeCampo(nomeCampo);
        Field[] fields = getFieldsMarkedUseOnlyThisToLog(anterior).length > 1 ?
                getFieldsMarkedUseOnlyThisToLog(anterior) : getFIeldsNotMarkedWithNotLogged(anterior);
        StringBuilder valorCampoAlterado = new StringBuilder(log.getValorCampoAnterior() != null ? log.getValorCampoAnterior() : "");
        StringBuilder valorCampoAlteradoAux = new StringBuilder(log.getValorCampoAnterior() != null ? log.getValorCampoAnterior() : "");
        StringBuilder valorCampoAnterior = new StringBuilder(log.getValorCampoAnterior() != null ? log.getValorCampoAnterior() : "");
        StringBuilder valorCampoAnteriorAux = new StringBuilder(log.getValorCampoAnterior() != null ? log.getValorCampoAnterior() : "");
        for (Field field : fields) {
            field.setAccessible(true);
            if (field.isAnnotationPresent(Id.class)) {
                log.setChavePrimariaEntidadeSubordinada(field.get(alterado).toString());
                if (field.get(alterado) != null) {
                    valorCampoAlteradoAux.append(field.getName()).append(": ").append(field.get(alterado)).append(", ");
                }
                if (field.get(anterior) != null) {
                    valorCampoAnteriorAux.append(field.getName()).append(": ").append(field.get(anterior)).append(", ");
                }
                continue;
            }
            if (field.get(anterior) == null && field.get(alterado) == null) {
                field.setAccessible(false);
                continue;
            }
            if (!Objects.equals(field.get(alterado), field.get(anterior)) || operacao.equals("EXCLUSÃO")) {
                if ((field.isAnnotationPresent(ManyToMany.class) || field.isAnnotationPresent(OneToMany.class)) && !operacao.equals("INCLUSÃO")) {
                    gerarLogLista((Collection<?>) field.get(anterior), (Collection<?>) field.get(alterado),
                            nomeEntidadeMae, getNomeEntidade(nomeEntidadeMae, field), field.getName());
                } else {
                    if (field.get(anterior) == null) {
                        log.setValorCampoAnterior("");
                    } else {
                        valorCampoAnterior.append(log.getValorCampoAnterior() != null ? log.getValorCampoAnterior() : "");
                        valorCampoAnterior.append(makeAppend(field, anterior, alterado));
                    }
                    if (field.get(alterado) == null) {
                        log.setValorCampoAlterado("");
                    } else {
                        valorCampoAlterado.append(log.getValorCampoAlterado() != null ? log.getValorCampoAlterado() : "");
                        valorCampoAlterado.append(makeAppend(field, alterado, anterior));
                    }
                }
            }
            field.setAccessible(false);
        }

        valorCampoAnterior = new StringBuilder(valorCampoAnterior.toString().trim());
        if (valorCampoAnterior.length() > 0) {
            valorCampoAnterior.deleteCharAt(valorCampoAnterior.length() - 1);
            valorCampoAnteriorAux.append(valorCampoAnterior);
            log.setValorCampoAnterior(valorCampoAnteriorAux.toString());
        }

        valorCampoAlterado = new StringBuilder(valorCampoAlterado.toString().trim());
        if (valorCampoAlterado.length() > 0) {
            valorCampoAlterado.deleteCharAt(valorCampoAlterado.length() - 1);
            valorCampoAlteradoAux.append(valorCampoAlterado);
            log.setValorCampoAlterado(valorCampoAlteradoAux.toString());
        }

        if ((log.getValorCampoAlterado() != null && !log.getValorCampoAlterado().isEmpty())
                || (log.getValorCampoAnterior() != null && !log.getValorCampoAnterior().isEmpty())) {
            logs.add(log);
        }
    }

    private void gerarLogLista(Collection<?> anteriores, Collection<?> alterados, String nomeEntidadeMae, String nomeEntidadeDescricao, String nomeCampo) {
        alterados = alterados == null ? new ArrayList<>() :
                sortByCodigo(alterados);
        anteriores = anteriores == null ? new ArrayList<>() :
                sortByCodigo(anteriores);
        if (alterados.equals(anteriores) || (alterados.isEmpty() && anteriores.isEmpty())) {
            return;
        }
        AtomicReference<StringBuilder> sbValorAnterior = new AtomicReference<>(new StringBuilder("["));
        AtomicReference<StringBuilder> sbValorAlterado = new AtomicReference<>(new StringBuilder("["));
        Log log = new Log();
        log.setNomeCampo(nomeCampo);
        log.setChavePrimaria(chavePrimaria);
        if (operacao.equals("INCLUSÃO") || operacao.equals("EXCLUSÃO")) {
            log.setOperacao(operacao);
        } else {
            if (anteriores.size() > alterados.size()) {
                log.setOperacao("EXCLUSÃO em " + getNomeDescritivo(nomeEntidadeDescricao));
            } else if (anteriores.size() < alterados.size()) {
                log.setOperacao("INCLUSÃO em " + getNomeDescritivo(nomeEntidadeDescricao));
            } else {
                log.setOperacao("ALTERAÇÃO em " + getNomeDescritivo(nomeEntidadeDescricao));
            }
        }
        log.setNomeEntidade(nomeEntidadeMae);
        log.setNomeEntidadeDescricao(nomeEntidadeDescricao);
        log.setDataAlteracao(new Date());
        log.setResponsavelAlteracao(requestService.getUsuarioAtual().getUsername());
        if (!anteriores.isEmpty()) {
            List<?> finalAlterados = new ArrayList<>(alterados);
            anteriores.forEach(
                    anterior -> {
                        Field[] fieldsAnterior = getFieldsMarkedUseOnlyThisToLog(anterior).length > 1 ?
                                getFieldsMarkedUseOnlyThisToLog(anterior) : getFIeldsNotMarkedWithNotLogged(anterior);
                        try {
                            Object objectCompare = null;
                            List<?> filtered = finalAlterados.stream().filter(o -> getId(o).equals(getId(anterior))).collect(Collectors.toList());
                            if (filtered.size() == 1) {
                                objectCompare = filtered.get(0);
                            }
                            sbValorAnterior.get().append(populateSbList(fieldsAnterior, anterior, objectCompare));
                        } catch (IllegalAccessException e) {
                            e.printStackTrace();
                        }
                    }
            );
        }
        if (!alterados.isEmpty()) {
            List<?> finalAnteriores = new ArrayList<>(anteriores);
            alterados.forEach(
                    alterado -> {
                        Field[] fieldsAlterado = getFieldsMarkedUseOnlyThisToLog(alterado).length > 1 ?
                                getFieldsMarkedUseOnlyThisToLog(alterado) : getFIeldsNotMarkedWithNotLogged(alterado);
                        try {
                            Object objectCompare = null;
                            List<?> filtered = finalAnteriores.stream().filter(o -> getId(o).equals(getId(alterado))).collect(Collectors.toList());
                            if (filtered.size() == 1) {
                                objectCompare = filtered.get(0);
                            }
                            sbValorAlterado.get().append(populateSbList(fieldsAlterado, alterado, objectCompare));
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                    }
            );
        }
        if (!alterados.isEmpty()) {
            if (sbValorAlterado.toString().length() > 1) {
                sbValorAlterado.set(new StringBuilder(sbValorAlterado.toString().trim()));
                sbValorAlterado.get().deleteCharAt(sbValorAlterado.get().length() - 1);
                sbValorAlterado.get().append("]");
            } else {
                sbValorAlterado.set(new StringBuilder());
            }
        } else {
            sbValorAlterado.set(new StringBuilder());
        }
        if (!anteriores.isEmpty()) {
            if (sbValorAnterior.toString().length() > 1) {
                sbValorAnterior.set(new StringBuilder(sbValorAnterior.toString().trim()));
                sbValorAnterior.get().deleteCharAt(sbValorAnterior.get().length() - 1);
                sbValorAnterior.get().append("]");
            } else {
                sbValorAnterior.set(new StringBuilder());
            }
        } else {
            sbValorAnterior.set(new StringBuilder());
        }
        log.setValorCampoAlterado(sbValorAlterado.get().toString());
        log.setValorCampoAnterior(sbValorAnterior.get().toString());
        if (!sbValorAlterado.toString().isEmpty() || !sbValorAnterior.toString().isEmpty()) {
            logs.add(log);
        }
    }

    private StringBuilder populateSbList(Field[] fields, Object object, Object objectToCompare) throws IllegalAccessException {
        StringBuilder sb = new StringBuilder();
        StringBuilder sbCodigo = new StringBuilder("{");
        for (Field field : fields) {
            field.setAccessible(true);
            if (field.isAnnotationPresent(Id.class)) {
                sbCodigo.append(field.getName()).append(": ").append(field.get(object)).append(", ");
            } else {
                sb.append(makeAppend(field, object, objectToCompare));
            }
            field.setAccessible(false);
        }
        if (sb.length() > 1) {
            sb = new StringBuilder(sb.toString().trim());
            sbCodigo.append(sb);
            sbCodigo.deleteCharAt(sbCodigo.length() - 1);
            sbCodigo.append("},");
        } else {
            sbCodigo = new StringBuilder();
        }
        return sbCodigo;
    }

    private StringBuilder makeAppend(Field field, Object object, Object objectToCompare) throws IllegalAccessException {
        StringBuilder sb = new StringBuilder();
        if (field.get(object) != null) {
            Object o = field.get(object);
            Object o2 = null;
            if (field.get(object) instanceof Double) {
                o = BigDecimal.valueOf((Double) field.get(object)).setScale(2, RoundingMode.HALF_UP).doubleValue();
                if (objectToCompare != null) {
                    o2 = field.get(objectToCompare);
                    if (o2 != null) {
                        o2 = BigDecimal.valueOf((Double) field.get(objectToCompare)).setScale(2, RoundingMode.HALF_UP).doubleValue();
                    }
                }
            }
            if (objectToCompare == null || !o.equals(o2)) {
                if (field.get(object) instanceof Collection && ((Collection) field.get(object)).size() == 0) {
                    return new StringBuilder();
                }
                sb.append(field.getName()).append(": ");
                if (field.isAnnotationPresent(OneToOne.class) || field.isAnnotationPresent(ManyToOne.class)) {
                    StringBuilder sb2 = getSbFromObject(field.get(object), objectToCompare == null ? null : field.get(objectToCompare));
                    if (!sb2.toString().isEmpty()) {
                        sb.append(sb2);
                    } else {
                        sb = new StringBuilder();
                    }
                } else if (field.isAnnotationPresent(ManyToMany.class) || field.isAnnotationPresent(OneToMany.class)) {
                    sb.append(getSbFromCollection((Collection<?>) field.get(object), objectToCompare == null ? null : (Collection<?>) field.get(objectToCompare)));
                } else {
                    if (field.get(object) instanceof Double) {
                        sb.append(BigDecimal.valueOf((Double) field.get(object)).setScale(2, RoundingMode.HALF_UP).doubleValue());
                    } else if (field.get(object) instanceof Date) {
                        SimpleDateFormat formato = new SimpleDateFormat("dd/MM/yyyy");
                        String data = formato.format(field.get(object));
                        sb.append(data);
                    } else {
                        sb.append(field.get(object));
                    }
                }
                sb.append(", ");
            }
        }
        if (sb.toString().trim().length() == 1) {
            sb = new StringBuilder();
        }
        return sb;
    }

    private StringBuilder getSbFromObject(Object object, Object objectToCompare) throws IllegalAccessException {
        StringBuilder sb = new StringBuilder("{");
        Field[] fields = getFieldsMarkedUseOnlyThisToLog(object).length > 1 ?
                getFieldsMarkedUseOnlyThisToLog(object) : getFIeldsNotMarkedWithNotLogged(object);
        for (Field f : fields) {
            f.setAccessible(true);
            if (f.get(object) != null) {
                sb.append(makeAppend(f, object, objectToCompare));
            }
            f.setAccessible(false);
        }
        if (sb.length() > 1) {
            sb = new StringBuilder(sb.toString().trim());
            sb.deleteCharAt(sb.length() - 1);
            sb.append("}");
            return sb;
        }
        return new StringBuilder();
    }

    private StringBuilder getSbFromCollection(Collection<?> object, Collection<?> objectToCompare) throws IllegalAccessException {
        StringBuilder sb = new StringBuilder();
        List<?> objects = object == null ? new ArrayList<>() : sortByCodigo(object);
        List<?> objectsToCompare = objectToCompare == null ? new ArrayList<>() : sortByCodigo(objectToCompare);

        AtomicReference<StringBuilder> finalSb = new AtomicReference<>(sb);
        if (objects.size() != 0) {
            sb.append("[");
            objects.forEach(
                    obj -> {
                        StringBuilder sb2 = new StringBuilder();
                        Field[] fields = getFieldsMarkedUseOnlyThisToLog(obj).length > 1 ?
                                getFieldsMarkedUseOnlyThisToLog(obj) : getFIeldsNotMarkedWithNotLogged(obj);
                        Field codigoField = null;
                        Object objectCompare = null;
                        List<?> filtered = objectsToCompare.stream().filter(o -> getId(o).equals(getId(obj))).collect(Collectors.toList());
                        if (filtered.size() == 1) {
                            objectCompare = filtered.get(0);
                        }
                        for (Field field1 : fields) {
                            try {
                                field1.setAccessible(true);
                                if (field1.isAnnotationPresent(Id.class)) {
                                    codigoField = field1;
                                } else {
                                    sb2.append(makeAppend(field1, obj, objectCompare));
                                }
                                field1.setAccessible(false);
                            } catch (Exception e) {
                                e.printStackTrace();
                            }
                        }
                        if (sb2.length() > 1) {
                            if (codigoField != null) {
                                codigoField.setAccessible(true);
                                try {
                                    finalSb.get().append("{").append(codigoField.getName()).append(": ").append(codigoField.get(obj)).append(", ");
                                } catch (IllegalAccessException e) {
                                    e.printStackTrace();
                                }
                                codigoField.setAccessible(false);
                            }
                            sb2 = new StringBuilder(sb2.toString().trim());
                            sb2.deleteCharAt(sb2.length() - 1);
                            sb2.append("}, ");
                            finalSb.get().append(sb2);
                        }
                    }
            );
            sb = new StringBuilder(finalSb.toString().trim());
            sb.deleteCharAt(sb.length() - 1);
            sb.append("]");
        }
        return sb;
    }

    private Field[] getFieldsMarkedUseOnlyThisToLog(Object object) {
        return Arrays.stream(object.getClass().getDeclaredFields())
                .filter(f -> f.isAnnotationPresent(UseOnlyThisToLog.class) ||
                        f.isAnnotationPresent(Id.class)).toArray(Field[]::new);
    }

    private Field[] getFIeldsNotMarkedWithNotLogged(Object object) {
        return Arrays.stream(object.getClass().getDeclaredFields())
                .filter(f -> !f.isAnnotationPresent(NotLogged.class) ||
                        f.isAnnotationPresent(Id.class)).toArray(Field[]::new);
    }

    private String getNomeEntidade(String nomeEntidadeMae, Field field) throws ClassNotFoundException {
        String simpleName;
        Class<?> type = field.getType();
        if (field.isAnnotationPresent(ManyToMany.class) || field.isAnnotationPresent(OneToMany.class)) {
            type = Class.forName(((ParameterizedType) field.getGenericType()).getActualTypeArguments()[0].getTypeName());
        }
        simpleName = type.getSimpleName();
        String nomeEntidadeDesc = nomeEntidadeMae + " - " + simpleName;
        if (field.isAnnotationPresent(NomeEntidadeLog.class)) {
            nomeEntidadeDesc += "/" + field.getAnnotation(NomeEntidadeLog.class).value();
        } else if (type.isAnnotationPresent(NomeEntidadeLog.class)) {
            nomeEntidadeDesc += "/" + type.getAnnotation(NomeEntidadeLog.class).value();
        }
        return nomeEntidadeDesc;
    }

    private List<?> sortByCodigo(Collection<?> collectionAnterior) {
        return collectionAnterior.stream().sorted(
                (o1, o2) -> {
                    try {
                        Field field1 = Arrays.stream(o1.getClass().getDeclaredFields()).filter(f -> f.getName().equals("codigo")).collect(Collectors.toList()).get(0);
                        field1.setAccessible(true);
                        Object c1 = field1.get(o1);
                        Object c2 = field1.get(o2);
                        field1.setAccessible(false);
                        if (c1 == null && c2 == null) {
                            return 0;
                        }
                        if (c1 == null) {
                            return (Integer) c2;
                        }
                        if (c2 == null) {
                            return (Integer) c1;
                        }
                        if (c1 instanceof Integer && c2 instanceof Integer) {
                            return Integer.compare((int) c1, (int) c2);
                        } else {
                            return Long.compare((long) c1, (long) c2);
                        }
                    } catch (IllegalAccessException e) {
                        e.printStackTrace();
                    }
                    return 0;
                }
        ).collect(Collectors.toList());
    }

    private void saveLogs() throws Exception {
        for (Log log : logs) {
            try {
                logDao.save(log);
            } catch (Exception e) {
                this.logs = new ArrayList<>();
                this.operacao = "";
                this.chavePrimaria = "";
                e.printStackTrace();
                throw e;
            }
        }
        this.logs = new ArrayList<>();
        this.operacao = "";
        this.chavePrimaria = "";
    }

    private void saveLogs(String operacao) throws Exception {
        for (Log log : logs) {
            try {
                log.setOperacao(operacao);
                logDao.save(log);
            } catch (Exception e) {
                e.printStackTrace();
                this.logs = new ArrayList<>();
                this.operacao = "";
                this.chavePrimaria = "";
                throw e;
            }
        }
        this.logs = new ArrayList<>();
        this.operacao = "";
        this.chavePrimaria = "";
    }

    private String getChavePrimariaObjeto(Object objetoAlterado, Object objetoAnterior) throws IllegalAccessException {
        Object object = objetoAlterado != null ? objetoAlterado : objetoAnterior;
        String chavePrimaria = "";
        if (object == null) {
            return chavePrimaria;
        }
        for (Field field : object.getClass().getDeclaredFields()) {
            if (field.isAnnotationPresent(Id.class)) {
                field.setAccessible(true);
                if (operacao.isEmpty()) {
                    if (field.get(objetoAlterado) == null || (objetoAnterior != null && field.get(objetoAnterior) == null)) {
                        operacao = "INCLUSÃO";
                    } else {
                        operacao = "ALTERAÇÃO";
                    }
                }
                if (field.get(object) != null) {
                    chavePrimaria = field.get(object).toString();
                }
                field.setAccessible(false);
                break;
            }
        }
        return chavePrimaria;
    }

    private Object getId(Object object) {
        Assert.notNull(object, "Object must not be null.");

        Field idField = Arrays.stream(object.getClass().getDeclaredFields()).filter(df -> df.isAnnotationPresent(Id.class)).collect(Collectors.toList()).get(0);
        idField.setAccessible(true);
        Object id = null;
        try {
            id = idField.get(object);
        } catch (IllegalAccessException e) {
            e.printStackTrace();
        }
        idField.setAccessible(false);
        return id;
    }

    private String getNomeDescritivo(String nomeDescricao) {
        String[] s = nomeDescricao.split("-\\s|/\\s|/");
        if (s.length == 0) {
            return nomeDescricao.trim();
        }
        return s[s.length - 1].trim().toUpperCase();
    }

}
