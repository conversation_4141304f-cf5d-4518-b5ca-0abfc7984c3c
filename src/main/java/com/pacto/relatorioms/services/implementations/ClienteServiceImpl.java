package com.pacto.relatorioms.services.implementations;

import com.pacto.relatorioms.adapters.ClienteAdapter;
import com.pacto.relatorioms.dao.interfaces.ClienteDao;
import com.pacto.relatorioms.dto.ClienteDTO;
import com.pacto.config.dto.PaginadorDTO;
import com.pacto.relatorioms.entities.Cliente;
import com.pacto.relatorioms.filter.FiltroClienteJSON;
import com.pacto.config.security.interfaces.RequestService;
import com.pacto.relatorioms.services.interfaces.ClienteService;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class ClienteServiceImpl implements ClienteService {

    private final ClienteDao clienteDao;
    private final ClienteAdapter clienteAdapter;
    private final RequestService requestService;

    public ClienteServiceImpl(ClienteDao clienteDao, ClienteAdapter clienteAdapter, RequestService requestService) {
        this.clienteDao = clienteDao;
        this.clienteAdapter = clienteAdapter;
        this.requestService = requestService;
    }

    @Override
    public List<ClienteDTO> findAll(FiltroClienteJSON filtros, PaginadorDTO paginadorDTO) throws Exception {
        List<Cliente> cliente = clienteDao.findAll(filtros, paginadorDTO);
        List<ClienteDTO> empresaDTOS = clienteAdapter.toDtos(cliente);
        return empresaDTOS;
    }
}
