package com.pacto.relatorioms.services.implementations;

import com.pacto.config.exceptions.ServiceException;
import com.pacto.config.security.interfaces.RequestService;
import com.pacto.config.utils.Uteis;
import com.pacto.relatorioms.dao.interfaces.RelatorioDynamicsSesiDao;
import com.pacto.relatorioms.filter.FiltroRelatorioDynamicsSesiJSON;
import com.pacto.relatorioms.filter.RelatorioDynamicsSesiFilter;
import com.pacto.relatorioms.services.interfaces.RelatorioDynamicsSesiService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import java.io.BufferedWriter;
import java.io.File;
import java.io.FileWriter;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.List;

@Service
public class RelatorioDynamicsSesiServiceImpl implements RelatorioDynamicsSesiService {
    @Autowired
    private RelatorioDynamicsSesiDao relatorioDynamicsSesiDao;
    @Autowired
    private RequestService requestService;

    public RelatorioDynamicsSesiServiceImpl(RequestService requestService) {
        this.requestService = requestService;
    }

    @Override
    public String gerarArquivoTexto(FiltroRelatorioDynamicsSesiJSON filtroJSON, HttpServletRequest request) throws ServiceException {
        try {
            RelatorioDynamicsSesiFilter relatorioDynamicsSesiFilter = new RelatorioDynamicsSesiFilter();

            List<String> validacaoDeCRSesi = relatorioDynamicsSesiDao.validarSeProdutoNaoContemCentroResponsabilidade(filtroJSON, relatorioDynamicsSesiFilter);
            if (!validacaoDeCRSesi.isEmpty()) {
                throw new ServiceException(validacaoDeCRSesi.toString());
            }

            List<String> relatorioDTOS = relatorioDynamicsSesiDao.gerarLinhasDoArquivoTexto(filtroJSON, relatorioDynamicsSesiFilter);

            File arquivo = criarArquivo(filtroJSON.getPeriodoPesquisa(), request);

            AdicionarLinhasDeTextoNoArquivo(relatorioDTOS, arquivo.getPath());

            return arquivo.getName();
        } catch (Exception e) {
            e.printStackTrace();
            throw new ServiceException(e);
        }
    }

    private File criarArquivo(Date periodoPesquisa, HttpServletRequest request) throws Exception {

        String nome = "Contabil_Dynamics_"
                + ExtrairDataFormatada(periodoPesquisa)
                + ".txt";

        File txtFolder = new File(Uteis.obterCaminhoWebAplicacao(request) + File.separator + "temp");
        txtFolder.mkdirs();
        File arquivo = new File(txtFolder.getPath() + File.separator + nome);

        if (arquivo.exists()) {
            arquivo.delete();
        }

        arquivo.createNewFile();

        return arquivo;
    }

    private String ExtrairDataFormatada(Date data){

        LocalDate localDate = data.toInstant()
                .atZone(ZoneId.systemDefault())
                .toLocalDate();
        return localDate.format(DateTimeFormatter.ofPattern("yyyy-MM"));
    }

    private void AdicionarLinhasDeTextoNoArquivo(List<String> linhas, String caminhoArquivo) {

        try (BufferedWriter writer = new BufferedWriter(new FileWriter(caminhoArquivo, true))) {
            for (String linha : linhas) {
                writer.write(linha);
                writer.newLine();
            }
        } catch (Exception e) {
        }
    }
}
