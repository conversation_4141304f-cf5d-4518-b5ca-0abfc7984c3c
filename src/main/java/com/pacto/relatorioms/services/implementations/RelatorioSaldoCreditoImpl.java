package com.pacto.relatorioms.services.implementations;

import com.pacto.relatorioms.dao.interfaces.RelatorioSaldoCreditoDao;
import com.pacto.relatorioms.dto.RelatorioSaldoCreditoDTO;
import com.pacto.config.dto.PaginadorDTO;
import com.pacto.config.exceptions.ServiceException;
import com.pacto.relatorioms.filter.FiltroRelatorioSaldoCreditoJSON;
import com.pacto.relatorioms.filter.RelatorioSaldoCreditoFilter;
import com.pacto.relatorioms.services.interfaces.RelatorioSaldoCreditoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;


@Service
public class RelatorioSaldoCreditoImpl implements RelatorioSaldoCreditoService {

    @Autowired
    private RelatorioSaldoCreditoDao relatorioSaldoCreditoDao;

    @Override
    public List<RelatorioSaldoCreditoDTO> consultarSaldoCredito(FiltroRelatorioSaldoCreditoJSON filtroJSON, PaginadorDTO paginadorDTO) throws ServiceException {
        try {
            RelatorioSaldoCreditoFilter relatorioSaldoCreditoFilter = new RelatorioSaldoCreditoFilter();
            List<RelatorioSaldoCreditoDTO> relatorioSaldoCreditoDTOS = relatorioSaldoCreditoDao.consultarSaldoCredito(filtroJSON, relatorioSaldoCreditoFilter, paginadorDTO);
            return relatorioSaldoCreditoDTOS;
        } catch (Exception e) {
            e.printStackTrace();
            throw new ServiceException(e);
        }
    }
}
