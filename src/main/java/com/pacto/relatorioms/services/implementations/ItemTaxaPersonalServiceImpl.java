package com.pacto.relatorioms.services.implementations;

import com.pacto.relatorioms.adapters.ItemTaxaPersonalAdapter;
import com.pacto.relatorioms.dao.interfaces.ItemTaxaPersonalDao;
import com.pacto.relatorioms.dto.ItemTaxaPersonalDTO;
import com.pacto.config.dto.PaginadorDTO;
import com.pacto.relatorioms.entities.ItemTaxaPersonal;
import com.pacto.config.security.interfaces.RequestService;
import com.pacto.relatorioms.services.interfaces.ItemTaxaPersonalService;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class ItemTaxaPersonalServiceImpl implements ItemTaxaPersonalService {

    private final RequestService requestService;
    private final ItemTaxaPersonalDao itemTaxaPersonalDao;
    private final ItemTaxaPersonalAdapter itemTaxaPersonalAdapter;

    public ItemTaxaPersonalServiceImpl(RequestService requestService, ItemTaxaPersonalDao itemTaxaPersonalDao, ItemTaxaPersonalAdapter itemTaxaPersonalAdapter) {
        this.requestService = requestService;
        this.itemTaxaPersonalDao = itemTaxaPersonalDao;
        this.itemTaxaPersonalAdapter = itemTaxaPersonalAdapter;
    }

    @Override
    public List<ItemTaxaPersonalDTO> consultarPorControleTaxaPersonal(Integer controleTaxaPersonal, PaginadorDTO paginadorDTO) throws Exception {
        List<ItemTaxaPersonal> itensTaxaPersonal = itemTaxaPersonalDao.consultarPorControleTaxaPersonal(controleTaxaPersonal, paginadorDTO);
        List<ItemTaxaPersonalDTO> itemTaxaPersonalDTOS = itemTaxaPersonalAdapter.toDtos(itensTaxaPersonal);
        return itemTaxaPersonalDTOS;
    }

}
