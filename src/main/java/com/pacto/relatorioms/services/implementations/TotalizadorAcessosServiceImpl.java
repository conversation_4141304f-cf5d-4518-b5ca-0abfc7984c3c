package com.pacto.relatorioms.services.implementations;

import com.pacto.config.utils.UteisValidacao;
import com.pacto.relatorioms.dao.interfaces.AcessoClienteDao;
import com.pacto.relatorioms.dto.RelatorioTotalizadorAcessosDTO;
import com.pacto.config.dto.PaginadorDTO;
import com.pacto.relatorioms.dto.TotalizadorAcessosDTO;
import com.pacto.relatorioms.enums.AgrupamentoEnum;
import com.pacto.relatorioms.enums.TipoFrequenciaEnum;
import com.pacto.config.exceptions.ServiceException;
import com.pacto.relatorioms.filter.FiltroTotalizadorAcessosJSON;
import com.pacto.config.security.interfaces.RequestService;
import com.pacto.relatorioms.services.interfaces.TotalizadorAcessosService;
import com.pacto.config.utils.Formatador;
import com.pacto.config.utils.Uteis;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;


@Service
public class TotalizadorAcessosServiceImpl implements TotalizadorAcessosService {

    private final MessageSource messageSource;

    @Autowired
    private RequestService requestService;
    @Autowired
    AcessoClienteDao acessoClienteDao;

    public TotalizadorAcessosServiceImpl(MessageSource messageSource) {
        this.messageSource = messageSource;
    }

    @Override
    public RelatorioTotalizadorAcessosDTO consultarRelatorioTotalizadorAcessos(FiltroTotalizadorAcessosJSON filtros, PaginadorDTO paginadorDTO) throws ServiceException {
        validacoes(filtros);
        List<TotalizadorAcessosDTO> listaTotalizadorAcessos = montarListaTotalizadorAcessos(filtros, paginadorDTO);
        Integer quantidadeTotalAcessos = somarQuantidadeAcessosLista(listaTotalizadorAcessos);
        if (UteisValidacao.emptyNumber(quantidadeTotalAcessos)) {
            throw new ServiceException(messageSource.getMessage("relatorio.totalizador.acessos.dados.nao.encontratos", null, new Locale(requestService.getLocale())));
        }
        RelatorioTotalizadorAcessosDTO relatorioTotalizadorAcessosDTO = new RelatorioTotalizadorAcessosDTO();
        relatorioTotalizadorAcessosDTO.setTotalAcessos(quantidadeTotalAcessos);
        if (paginadorDTO.getSize() != null && paginadorDTO.getPage() != null) {
            listaTotalizadorAcessos = paginarLista(listaTotalizadorAcessos, paginadorDTO);
        }
        relatorioTotalizadorAcessosDTO.setTotalizadorAcessos(listaTotalizadorAcessos);
        return relatorioTotalizadorAcessosDTO;
    }

    @Override
    public List<TotalizadorAcessosDTO> consultarListaTotalizadorAcessos(FiltroTotalizadorAcessosJSON filtros, PaginadorDTO paginadorDTO) throws ServiceException {
        validacoes(filtros);
        List<TotalizadorAcessosDTO> listaTotalizadorAcessosDTOS = montarListaTotalizadorAcessos(filtros, paginadorDTO);
        if (paginadorDTO.getSize() != null && paginadorDTO.getPage() != null) {
            listaTotalizadorAcessosDTOS = paginarLista(listaTotalizadorAcessosDTOS, paginadorDTO);
        }
        return listaTotalizadorAcessosDTOS;
    }

    public List<TotalizadorAcessosDTO> paginarLista(List<TotalizadorAcessosDTO> listaTotalizadorAcessos, PaginadorDTO paginadorDTO) {
        List<TotalizadorAcessosDTO> listaPaginadaTotalizadorAcessosDTOs = new ArrayList<>();
        if (paginadorDTO != null) {
            // Ordenar lista
            if (Uteis.notNullAndNotEmpty(paginadorDTO.getSort())) {
                try {
                    this.ordenarListaTotalizadorAcessos(listaTotalizadorAcessos, paginadorDTO);
                } catch (ClassNotFoundException e) {
                    e.printStackTrace();
                } catch (NoSuchMethodException e) {
                    e.printStackTrace();
                } catch (NoSuchFieldException e) {
                    e.printStackTrace();
                }
            }
            // Paginar lista
            int primeiroPaginacao = (int) (paginadorDTO.getPage() * paginadorDTO.getSize());
            int ultimoRegistro = (int) ((paginadorDTO.getPage() * paginadorDTO.getSize()) + paginadorDTO.getSize());
            if (ultimoRegistro > listaTotalizadorAcessos.size()) {
                listaPaginadaTotalizadorAcessosDTOs.addAll(listaTotalizadorAcessos.subList(primeiroPaginacao, listaTotalizadorAcessos.size()));
            } else {
                listaPaginadaTotalizadorAcessosDTOs.addAll(listaTotalizadorAcessos.subList(primeiroPaginacao, ultimoRegistro));
            }
        }

        if (paginadorDTO != null) {
            paginadorDTO.setQuantidadeTotalElementos((long) listaTotalizadorAcessos.size());
        }
        return listaPaginadaTotalizadorAcessosDTOs;
    }

    public void ordenarListaTotalizadorAcessos(List<TotalizadorAcessosDTO> listaTotalizadorAcessos, PaginadorDTO paginadorDTO) throws ClassNotFoundException, NoSuchMethodException, NoSuchFieldException {
        String sortColumn = paginadorDTO.getSort().split(",")[0];
        String sortDirection = paginadorDTO.getSort().split(",")[1];

        String nomeMetodoGet = "get" + StringUtils.capitalize(sortColumn);

        listaTotalizadorAcessos.sort(new Comparator<TotalizadorAcessosDTO>() {
            @Override
            public int compare(TotalizadorAcessosDTO a, TotalizadorAcessosDTO b) {
                int resultadoOrdenacao = 0;
                try {
                    Method getMethod = getMethod = Class.forName("com.pacto.relatorioms.dto.TotalizadorAcessosDTO").getMethod(nomeMetodoGet);
                    if (getMethod != null) {
                        try {
                            if (sortColumn.equalsIgnoreCase("data")) {
                                Date a1 = Uteis.getDate(a.getData(), "dd/MM/yyyy");
                                Date b2 = Uteis.getDate(b.getData(), "dd/MM/yyyy");
                                if (a1.after(b2)) {
                                    resultadoOrdenacao = 1;
                                } else if (a1.before(b2)) {
                                    resultadoOrdenacao = -1;
                                } else {
                                    resultadoOrdenacao = 0;
                                }
                            } else if (sortColumn.equalsIgnoreCase("porcentagem")) {
                                if (a.getPorcetagemDouble() > b.getPorcetagemDouble()) {
                                    resultadoOrdenacao = 1;
                                } else if (a.getPorcetagemDouble() < b.getPorcetagemDouble()) {
                                    resultadoOrdenacao = -1;
                                } else {
                                    resultadoOrdenacao = 0;
                                }
                            } else if (sortColumn.equalsIgnoreCase("porcentagemMaiorDia")) {
                                if (a.getPorcetagemAtivosDouble() > b.getPorcetagemMaiorDiaDouble()) {
                                    resultadoOrdenacao = 1;
                                } else if (a.getPorcetagemMaiorDiaDouble() > b.getPorcetagemMaiorDiaDouble()) {
                                    resultadoOrdenacao = -1;
                                } else {
                                    resultadoOrdenacao = 0;
                                }
                            }  else if (sortColumn.equalsIgnoreCase("porcentagemAtivos")) {
                                if (a.getPorcetagemAtivosDouble() > b.getPorcetagemAtivosDouble()) {
                                    resultadoOrdenacao = 1;
                                } else if (a.getPorcetagemMaiorDiaDouble() > b.getPorcetagemAtivosDouble()) {
                                    resultadoOrdenacao = -1;
                                } else {
                                    resultadoOrdenacao = 0;
                                }
                            } else if (getMethod.getReturnType() == String.class) {
                                String a1 = (String) getMethod.invoke(a);
                                String b1 = (String) getMethod.invoke(b);
                                resultadoOrdenacao = a1.compareTo(b1);
                            } else if (getMethod.getReturnType() == Integer.class) {
                                Integer a1 = (Integer) getMethod.invoke(a);
                                Integer b1 = (Integer) getMethod.invoke(b);
                                if (a1 < b1) {
                                    resultadoOrdenacao = 1;
                                } else if (a1 > b1) {
                                    resultadoOrdenacao = -1;
                                } else {
                                    resultadoOrdenacao = 0;
                                }
                            } else if (getMethod.getReturnType() == Double.class) {
                                Double a1 = (Double) getMethod.invoke(a);
                                Double b1 = (Double) getMethod.invoke(b);
                                if (a1 < b1) {
                                    resultadoOrdenacao = 1;
                                } else if (a1 > b1) {
                                    resultadoOrdenacao = -1;
                                } else {
                                    resultadoOrdenacao = 0;
                                }
                            }
                        } catch (IllegalAccessException e) {
                            e.printStackTrace();
                        } catch (InvocationTargetException e) {
                            e.printStackTrace();
                        }
                        if (sortDirection.equalsIgnoreCase("DESC")) {
                            // inverter sinal e consequentemente a ordenação
                            resultadoOrdenacao = resultadoOrdenacao * -1;
                        }
                    }
                } catch (Exception e) {
                }
                return resultadoOrdenacao;
            }
        });
    }

    private Integer somarQuantidadeAcessosLista(List<TotalizadorAcessosDTO> totalizadorAcessosDTOS) {
        AtomicReference<Integer> quantidadeTotalAcesso = new AtomicReference<>(0);
        totalizadorAcessosDTOS.forEach(item -> {
            quantidadeTotalAcesso.updateAndGet(v -> v + item.getQuantidade());
        });
        return quantidadeTotalAcesso.get();
    }

    private void validacoes(FiltroTotalizadorAcessosJSON filtros) throws ServiceException {
        if (filtros.getPeriodoInicial() == null) {
            throw new ServiceException(messageSource.getMessage("relatorio.totalizador.acessos.data.inicial.nao.informada", null, new Locale(requestService.getLocale())));
        }
        if (filtros.getPeriodoFinal() == null) {
            throw new ServiceException(messageSource.getMessage("relatorio.totalizador.acessos.data.final.nao.informada", null, new Locale(requestService.getLocale())));
        }
        if (Uteis.nrDiasEntreDatas(filtros.getPeriodoInicial(), filtros.getPeriodoFinal()) < 0) {
            throw new ServiceException(messageSource.getMessage("relatorio.totalizador.acessos.data.inicial.maior", null, new Locale(requestService.getLocale())));
        }
        if (Uteis.nrDiasEntreDatas(filtros.getPeriodoInicial(), filtros.getPeriodoFinal()) > 180) {
            throw new ServiceException(messageSource.getMessage("relatorio.totalizador.data.limite.consulta", null, new Locale(requestService.getLocale())));
        }
    }

    private List<TotalizadorAcessosDTO> montarListaTotalizadorAcessos(FiltroTotalizadorAcessosJSON filtros, PaginadorDTO paginadorDTO) throws ServiceException {
        List<TotalizadorAcessosDTO> listaTotalizadorAcessos = new ArrayList<>();

        if (filtros.getFrequencia().equals(TipoFrequenciaEnum.GERAL.getId())) { // geral

            String sql = gerarSqlAcessosFrequenciaGeral(filtros);

            try {
                acessoClienteDao.createSessionCurrentWork().doWork(connection -> {
                    try {
                        int quantidadeLocal = 0;
                        int ativosLocal = 0;
                        Date dataAnterior = null;
                        String nomeEmpresa = "";

                        ResultSet resultadoConsulta = acessoClienteDao.createStatement(connection, sql.toString());
                        while (resultadoConsulta.next()) {
                            if (filtros.getAgruparPorPessoa()) {

                                String nome = resultadoConsulta.getString("nome");
                                String matricula = resultadoConsulta.getString("matricula");
                                String nomePlano = resultadoConsulta.getString("nomeplano");
                                String nomeModalidade = resultadoConsulta.getString("nomemodalidade");
                                int atual = resultadoConsulta.getInt("quantidade");
                                int ativos = resultadoConsulta.getInt("quantidadeAtivos");
                                nomeEmpresa = resultadoConsulta.getString("nomeEmpresa");

                                TotalizadorAcessosDTO obj = new TotalizadorAcessosDTO();
                                obj.setQuantidade(atual);
                                obj.setNome(Uteis.notNullAndNotEmpty(nome) ? nome : "-");
                                obj.setMatricula(matricula);
                                obj.setPlano(Uteis.notNullAndNotEmpty(nomePlano) ? nomePlano : "-");
                                obj.setModalidade(Uteis.notNullAndNotEmpty(nomeModalidade) ? nomeModalidade : "-");
                                obj.setNome(nome);
                                obj.setQuantidadeAtivos(ativos);
                                obj.setNomeEmpresa(nomeEmpresa);
                                listaTotalizadorAcessos.add(obj);
                            } else {
                                int codigoPessoa = 0;
                                //preenche o objeto (data e quantidade)
                                Date dataAtual = resultadoConsulta.getDate("data");
                                int quantidadeAtual = resultadoConsulta.getInt("quantidade");
                                int quantidadeAtivos = resultadoConsulta.getInt("quantidadeAtivos");
                                nomeEmpresa = resultadoConsulta.getString("nomeEmpresa");

                                if (dataAnterior == null
                                        || dataAtual.compareTo(dataAnterior) == 0) {
                                    //se a data for igual a dataDeComparacao ou a data do objeto anterior  for nula
                                    // adicionar a quantidade
                                    try {
                                        //Caso seja colaborador ele retorne no sql  na coluna quantidade e quantidadeAtivos o total de acessos de todos os colaboradores, não usar o filtros.getConsiderarPrimeiroAcessoDia()
                                        // pois já foi feito para pegar só o primeiro acesso do dia no sql
                                        codigoPessoa = resultadoConsulta.getInt("pessoa");
                                        quantidadeLocal += (filtros.getConsiderarPrimeiroAcessoDia() ? 1 : quantidadeAtual);
                                        ativosLocal += (filtros.getConsiderarPrimeiroAcessoDia() && quantidadeAtivos >= 1) ? 1 : quantidadeAtivos;
                                    } catch (SQLException e) {
                                        quantidadeLocal += quantidadeAtual;
                                        ativosLocal += quantidadeAtivos;
                                    }

                                    // atualizar a data do objAnterior
                                    dataAnterior = dataAtual;

                                } else {
                                    adicionarLista(quantidadeLocal, ativosLocal, dataAnterior, listaTotalizadorAcessos, nomeEmpresa);
                                    //se a data for diferente da do objeto anterior, e a data do objeto anterior nao for nula
                                    dataAnterior = dataAtual;
                                    quantidadeLocal = filtros.getConsiderarPrimeiroAcessoDia() ? 1 : quantidadeAtual;
                                    ativosLocal += (filtros.getConsiderarPrimeiroAcessoDia() && quantidadeAtivos >= 1) ? 1 : quantidadeAtivos;
                                }
                            }
                        }
                        if (!filtros.getAgruparPorPessoa() && quantidadeLocal > 0) {
                            adicionarLista(quantidadeLocal, ativosLocal, dataAnterior, listaTotalizadorAcessos, nomeEmpresa);
                        }
                    } catch (Exception e) {
                        throw new RuntimeException(e);
                    } finally {
                        connection.close();
                    }
                });
            } catch (Exception e) {
                e.printStackTrace();
                throw new ServiceException(e);
            }
        }

        if (filtros.getFrequencia().equals(TipoFrequenciaEnum.GERAL_POR_DIA.getId())) { // geral por dia da semana

            String sql = gerarSqlAcessosFrequenciaGeralPorDia(filtros);

            try {
                acessoClienteDao.createSessionCurrentWork().doWork(connection -> {
                    try (ResultSet resultadoConsulta = acessoClienteDao.createStatement(connection, sql)) {
                        //cria um vetor pra armazenar a quantidade de frequencias por dia da semana
                        int[] quantidades = new int[7];
                        //instancia uma data de entrada do tipo Calendar
                        Calendar dtEntrada = Calendar.getInstance();
                        while (resultadoConsulta.next()) {
                            //obtemos a data atual e quantidade de frequencias da consulta do banco
                            Date dataAtual = resultadoConsulta.getDate("data");
                            int quantidadeAtual = resultadoConsulta.getInt("quantidade");
                            //setamos o valor da variavel do tipo dtEntrada para a data atual
                            dtEntrada.setTime(dataAtual);
                            //criamos a variavel que armazena o inteiro correspondente ao valor do dia da semana
                            int diaDaSemanaAtual = dtEntrada.get(Calendar.DAY_OF_WEEK);
                            //setamos o valor da quantidade de acordo com o dia da semana
                            quantidades[diaDaSemanaAtual - 1] += (filtros.getConsiderarPrimeiroAcessoDia() ? 1 : quantidadeAtual);
                        }
                        //condicionais que verificam se existem frequências por dia da semana
                        //e seta o valor do dia da semana e da frequência do objeto do tipo TotalizadorFrequenciaRel
                        if (quantidades[0] > 0) {
                            TotalizadorAcessosDTO objDomingo = new TotalizadorAcessosDTO();
                            objDomingo.setDiaDaSemana("Domingo");
                            objDomingo.setQuantidade(quantidades[0]);
                            listaTotalizadorAcessos.add(objDomingo);
                        }
                        if (quantidades[1] > 0) {
                            TotalizadorAcessosDTO objSegunda = new TotalizadorAcessosDTO();
                            objSegunda.setDiaDaSemana("Segunda-Feira");
                            objSegunda.setQuantidade(quantidades[1]);
                            listaTotalizadorAcessos.add(objSegunda);
                        }
                        if (quantidades[2] > 0) {
                            TotalizadorAcessosDTO objTerca = new TotalizadorAcessosDTO();
                            objTerca.setDiaDaSemana("Terça-Feira");
                            objTerca.setQuantidade(quantidades[2]);
                            listaTotalizadorAcessos.add(objTerca);
                        }
                        if (quantidades[3] > 0) {
                            TotalizadorAcessosDTO objQuarta = new TotalizadorAcessosDTO();
                            objQuarta.setDiaDaSemana("Quarta-Feira");
                            objQuarta.setQuantidade(quantidades[3]);
                            listaTotalizadorAcessos.add(objQuarta);
                        }
                        if (quantidades[4] > 0) {
                            TotalizadorAcessosDTO objQuinta = new TotalizadorAcessosDTO();
                            objQuinta.setDiaDaSemana("Quinta-Feira");
                            objQuinta.setQuantidade(quantidades[4]);
                            listaTotalizadorAcessos.add(objQuinta);
                        }
                        if (filtros.getAgrupamento().equals(AgrupamentoEnum.NENHUM.getId())) {
                            if (quantidades[5] > 0) {
                                TotalizadorAcessosDTO objSexta = new TotalizadorAcessosDTO();
                                objSexta.setDiaDaSemana("Sexta-Feira");
                                objSexta.setQuantidade(quantidades[5]);
                                listaTotalizadorAcessos.add(objSexta);
                            }
                            if (quantidades[6] > 0) {
                                TotalizadorAcessosDTO objSabado = new TotalizadorAcessosDTO();
                                objSabado.setDiaDaSemana("Sábado");
                                objSabado.setQuantidade(quantidades[6]);
                                listaTotalizadorAcessos.add(objSabado);
                            }
                        } else {
                            if ((quantidades[5] > 0) || (quantidades[6] > 0)) {
                                TotalizadorAcessosDTO objSexta = new TotalizadorAcessosDTO();
                                objSexta.setDiaDaSemana("Sexta-Feira e Sábado");
                                objSexta.setQuantidade(quantidades[5] + quantidades[6]);
                                listaTotalizadorAcessos.add(objSexta);
                            }
                        }
                    } catch (Exception e) {
                        throw new RuntimeException(e);
                    } finally {
                        connection.close();
                    }
                });
            } catch (Exception e) {
                throw new ServiceException(e);
            }
        }
        if (filtros.getFrequencia().equals(TipoFrequenciaEnum.GERAL_POR_HORARIO.getId())) { // por horário
            String sql = gerarSqlAcessosFrequenciaGeralPorHorario(filtros);
            try {
                acessoClienteDao.createSessionCurrentWork().doWork(connection -> {
                    try (ResultSet resultadoConsulta = acessoClienteDao.createStatement(connection, sql)) {
                        TreeMap<Double, HashMap<String, Integer>> mapaHora = new TreeMap<>();
                        HashMap<String, Integer> mapaClienteDia = new HashMap<>();
                        while (resultadoConsulta.next()) {
                            Double hora = 0.0;
                            if (!filtros.getConsiderarPrimeiroAcessoDia()) {
                                hora = resultadoConsulta.getDouble("data");
                                TotalizadorAcessosDTO obj = new TotalizadorAcessosDTO();
                                obj.setQuantidadeAtivos(new Integer(resultadoConsulta.getInt("quantidadeAtivos")));
                                obj.setQuantidade(resultadoConsulta.getInt("quantidade"));
                                obj.setHora(new Double(hora));
                                listaTotalizadorAcessos.add(obj);
                            } else {
                                hora = new Double(Uteis.gethoraHH(resultadoConsulta.getTimestamp("data")));
                                Integer quantidadeAtivos = resultadoConsulta.getInt("quantidadeAtivos");
                                HashMap<String, Integer> mapaDiaCliente = mapaHora.get(hora);
                                String keyCliente = Uteis.getData(resultadoConsulta.getDate("data")) + "|";

                                try {
                                    keyCliente += resultadoConsulta.getInt("pessoa");
                                } catch (SQLException e) {
                                    keyCliente += resultadoConsulta.getString("pessoa");
                                }

                                if (mapaClienteDia.get(keyCliente) != null) {
                                    continue;
                                }
                                if (mapaDiaCliente == null) {
                                    mapaDiaCliente = new HashMap<>();
                                    mapaDiaCliente.put(keyCliente, quantidadeAtivos);
                                    mapaHora.put(hora, mapaDiaCliente);
                                } else if (mapaDiaCliente.get(keyCliente) == null) {
                                    mapaDiaCliente.put(keyCliente, quantidadeAtivos);
                                }
                                mapaClienteDia.put(keyCliente, quantidadeAtivos);
                            }
                        }
                        for (Double key : mapaHora.keySet()) {
                            TotalizadorAcessosDTO obj = new TotalizadorAcessosDTO();
                            obj.setQuantidade(mapaHora.get(key).size());
                            obj.setHora(key);
                            Integer ativos = 0;
                            for (String k : mapaHora.get(key).keySet()) {
                                ativos += mapaHora.get(key).get(k);
                            }
                            obj.setQuantidadeAtivos(ativos);
                            listaTotalizadorAcessos.add(obj);
                        }
                    } catch (Exception e) {
                        throw new RuntimeException(e);
                    } finally {
                        connection.close();
                    }
                });
            } catch (Exception e) {
                throw new ServiceException(e);
            }
        }
        return this.gerarRelatorio(listaTotalizadorAcessos, filtros);
    }

    private void adicionarLista(int quantidadeLocal, int ativos, Date dataAnterior, List listaDeObjetos, String nomeEmpresas) {
        // adicionar o objeto anterior
        TotalizadorAcessosDTO objAnterior = new TotalizadorAcessosDTO();
        objAnterior.setQuantidade(quantidadeLocal);
        objAnterior.setData(Uteis.getData(dataAnterior, "br"));
        objAnterior.setDataInicial(Uteis.getData(dataAnterior, "br"));
        objAnterior.setQuantidadeAtivos(ativos);
        objAnterior.setNomeEmpresa(nomeEmpresas);
        listaDeObjetos.add(objAnterior);
    }

    private String gerarSqlAcessosFrequenciaGeralPorDia(FiltroTotalizadorAcessosJSON filtros) throws ServiceException {
        StringBuilder sql = new StringBuilder();

        sql.append(" SELECT \n");

        if (filtros.getConsiderarPrimeiroAcessoDia()) {
            if (filtros.getApresentarAcessosNessaUnidade() != null && filtros.getApresentarAcessosNessaUnidade()) {
                sql.append("sql.matricula as pessoa,");
            }else {
                sql.append("sql.pessoa as pessoa,");
            }
        }

        sql.append("CAST(sql.entrada AS DATE) as data, \n");
        sql.append("COUNT(DISTINCT(sql.codAcesso)) as quantidade \n");

        if (filtros.getApresentarAcessosNessaUnidade() != null && filtros.getApresentarAcessosNessaUnidade()) {
            sql.append(sqlBase2(filtros, false));
        } else {
            sql.append(sqlBase(filtros, false));
        }

        if (filtros.getConsiderarPrimeiroAcessoDia()) {
            sql.append(" GROUP BY 1,2 ");
        } else {
            sql.append(" GROUP BY data ");
        }
        sql.append(" ORDER BY data");
        System.out.println("--SQL GERAL POR DIA DA SEMANA: \n\n" + sql.toString());

        return sql.toString();
    }

    private String gerarSqlAcessosFrequenciaGeralPorHorario(FiltroTotalizadorAcessosJSON filtros) throws ServiceException {
        StringBuilder sql = new StringBuilder();
        sql.append(" SELECT ");
        if (filtros.getConsiderarPrimeiroAcessoDia()) {
            if (filtros.getApresentarAcessosNessaUnidade() != null && filtros.getApresentarAcessosNessaUnidade()) {
                sql.append("sql.matricula as pessoa,");
            }else {
                sql.append("sql.pessoa as pessoa,");
            }
            sql.append("sql.entrada as data, ");
        } else {
            sql.append(" EXTRACT( hour FROM sql.entrada) as data, ");
        }
        sql.append(" COUNT(DISTINCT(sql.codAcesso)) as quantidade, ");
//        sql.append(" COUNT(CASE WHEN sql.situacao = 'AT' THEN 1 ELSE null END) as quantidadeAtivos ");
        // Contar a quantidade de acessos dos alunos ativos de forma distinta

        if (filtros.getApresentarAcessosNessaUnidade() != null && filtros.getApresentarAcessosNessaUnidade()) {
            sql.append(" COUNT(distinct(sql.codAcesso)) as quantidadeAtivos ");
            sql.append(sqlBase2(filtros, false));
        } else {
            sql.append(" COUNT(distinct(CASE WHEN sql.situacao = 'AT' THEN sql.codAcesso ELSE null end)) as quantidadeAtivos ");
            sql.append(sqlBase(filtros, false));
        }
        if (filtros.getConsiderarPrimeiroAcessoDia()) {
            sql.append(" GROUP BY 1,2 ");
        } else {
            sql.append(" GROUP BY data ");
        }
        sql.append(" ORDER BY data");

        System.out.println("--SQL POR HORÁRIO: \n\n" + sql.toString());
        return sql.toString();
    }

    private String gerarSqlAcessosFrequenciaGeral(FiltroTotalizadorAcessosJSON filtros) throws ServiceException {
        StringBuilder sql = new StringBuilder();

        sql.append(" SELECT \n");

        if (filtros.getConsiderarPrimeiroAcessoDia()) {
            if (filtros.getApresentarAcessosNessaUnidade() != null && filtros.getApresentarAcessosNessaUnidade()) {
                sql.append("sql.matricula as pessoa,");
            }else {
                sql.append("sql.pessoa as pessoa,");
            }
        }

        if (filtros.getAgruparPorPessoa()) {
            sql.append(" sql.matricula, \n");
            sql.append(" sql.nome, \n");
            sql.append(" sql.nomePlano, \n");
            sql.append(" sql.nomeModalidade, \n");
        } else {
            sql.append(" CAST(sql.entrada AS DATE) as data, \n");
        }

        sql.append(" COUNT( distinct(sql.codAcesso)) as quantidade, \n");
        sql.append(" sql.nomeEmpresa as nomeEmpresa, \n");
//        sql.append(" COUNT(CASE WHEN sql.situacao = 'AT' THEN 1 ELSE null END) as quantidadeAtivos ");
        // Contar a quantidade de acessos dos alunos ativos de forma distinta

        if (filtros.getApresentarAcessosNessaUnidade() != null && filtros.getApresentarAcessosNessaUnidade()) {
            sql.append(" COUNT(distinct(sql.codAcesso)) as quantidadeAtivos ");
            sql.append(sqlBase2(filtros, false));
        } else {
            sql.append(" COUNT(distinct(CASE WHEN sql.situacao = 'AT' THEN sql.codAcesso ELSE null end)) as quantidadeAtivos ");
            sql.append(sqlBase(filtros, false));
        }

        if (filtros.getConsiderarPrimeiroAcessoDia()) {
            sql.append(" GROUP BY 1,2,nomeEmpresa \n");
            sql.append(" ORDER BY data ");
        } else if (filtros.getAgruparPorPessoa()) {
            sql.append(" GROUP BY matricula,nome,nomeplano,nomeModalidade,nomeEmpresa \n");
            sql.append(" ORDER BY nome ");
        } else {
            sql.append(" GROUP BY data,nomeEmpresa \n");
            sql.append(" ORDER BY data ");
        }

        System.out.println("--SQL GERAL: \n\n" + sql.toString());
        return sql.toString();
    }

    private List<TotalizadorAcessosDTO> gerarRelatorio(List<TotalizadorAcessosDTO> lista, FiltroTotalizadorAcessosJSON filtros) throws ServiceException {
        int totalizador = 0;
        double total = 0;
        for (Object aLista : lista) {
            TotalizadorAcessosDTO obj = new TotalizadorAcessosDTO();
            obj = (TotalizadorAcessosDTO) aLista;
            total += (double) obj.getQuantidade();
            //Setando o total de quantidades de acessos para o relatorio
            totalizador += obj.getQuantidade();
        }
        for (Object aLista : lista) {
            TotalizadorAcessosDTO obj;
            obj = (TotalizadorAcessosDTO) aLista;
            double atual = (double) obj.getQuantidade();
            String porcentagem = String.valueOf(atual * 100.0 / total);
            String porcentagemAtivos = String.valueOf(obj.getQuantidadeAtivos() * 100.0 / total);
            BigDecimal big = new BigDecimal(porcentagem).setScale(2, BigDecimal.ROUND_UP);
            porcentagem = Formatador.formatarValorMonetarioSemMoeda(big.doubleValue());
            BigDecimal bigAtivo = new BigDecimal(porcentagemAtivos).setScale(2, BigDecimal.ROUND_UP);
            porcentagemAtivos = Formatador.formatarValorMonetarioSemMoeda(bigAtivo.doubleValue());

            obj.setPorcentagem(porcentagem + " %");
            obj.setPorcentagemAtivos(porcentagemAtivos + " %");
        }
        if (filtros.getAgrupamento().equals(AgrupamentoEnum.AGRUPAR_SX_SAB.getId())) {
            double maiorValor = 0.0;
            for (Object aLista : lista) {
                TotalizadorAcessosDTO obj = (TotalizadorAcessosDTO) aLista;
                double atual = (double) obj.getQuantidade();
                if (atual > maiorValor) {
                    maiorValor = atual;
                }
            }

            for (Object aLista : lista) {
                TotalizadorAcessosDTO obj = (TotalizadorAcessosDTO) aLista;
                double atual = (double) obj.getQuantidade();
                String porcentagem = String.valueOf(atual * 100.0 / maiorValor);
                BigDecimal big = new BigDecimal(porcentagem).setScale(2, BigDecimal.ROUND_UP);
                porcentagem = Formatador.formatarValorMonetarioSemMoeda(big.doubleValue());
                obj.setPorcentagemMaiorDia(porcentagem + " %");
            }
        }
        return lista;
    }

    private String sqlBase2(FiltroTotalizadorAcessosJSON filtros, boolean comColunas) throws ServiceException {
        StringBuilder sql = new StringBuilder();

        if (UteisValidacao.emptyNumber(filtros.getEmpresa())) {
            throw new ServiceException(messageSource.getMessage("relatorio.totalizador.acessos.empresa.nao.informada", null, new Locale(requestService.getLocale())));
        }

        if (comColunas) {
            sql.append("SELECT  \n");
            sql.append("*  \n");
        }
        sql.append(" FROM ( \n");

        //cliente acessou nessa unidade
        sql.append(" SELECT  \n");
        sql.append(" ai.dthrentrada as entrada, \n");
        sql.append(" ai.codigo as codAcesso,  \n");
        sql.append(" ai.situacao as situacaoAcesso, \n");
        sql.append(" ai.nomeCodEmpresaAcessou, \n");
        sql.append(" ci.situacao as situacao, \n");
        sql.append(" pc.codigo as pessoa, \n");
        sql.append(" pc.nome as nome, \n");
        sql.append(" ci.matricula as matricula, \n");
        sql.append(" pc.sexo as sexo, \n");
        sql.append(" EXTRACT ('YEAR' FROM (age(now(), pc.datanasc))) as idade, \n");
        sql.append(" con.plano, \n");
        sql.append(" pl.descricao as nomePlano, \n");
        sql.append(" modalidade.codigo as modalidade, \n");
        sql.append(" modalidade.nome as nomeModalidade, \n");
        sql.append(" ci.codigo as cliente, \n");
        sql.append(" null as colaborador, \n");
        sql.append(" emp.codigo as empresa, \n");
        sql.append(" emp.nome as nomeEmpresa, \n");
        sql.append(" ai.nomeCodEmpresaOrigem, \n");
        sql.append(" ai.nomeCpfEmailClienteOrigem \n");
        sql.append(" FROM acessocliente ai \n");
        sql.append(" INNER JOIN cliente ci ON ai.cliente = ci.codigo  \n");
        sql.append(" INNER JOIN pessoa pc ON ci.pessoa = pc.codigo  \n");
        sql.append(" LEFT JOIN situacaoclientesinteticodw  sw ON sw.codigocliente = ci.codigo \n");
        sql.append(" LEFT JOIN contrato con ON sw.codigocontrato = con.codigo \n");
        sql.append(" LEFT JOIN contratomodalidade conm on sw.codigocontrato = conm.contrato \n");
        sql.append(" LEFT JOIN modalidade on modalidade.codigo = conm.modalidade \n");
        sql.append(" LEFT JOIN plano pl ON pl.codigo = con.plano \n");
        sql.append(" INNER JOIN empresa emp on emp.codigo = ci.empresa \n");
        sql.append(" inner join LocalAcesso loc ON loc.codigo = ai.localAcesso \n");
        sql.append(" inner join empresa empL ON loc.empresa = empL.codigo \n");
        sql.append(" WHERE ((empL.codigo is null) or (empL.codigo = ").append(filtros.getEmpresa()).append(")) \n");
        sql.append(" AND ((ai.nomeCodEmpresaAcessou is null or ai.nomeCodEmpresaAcessou = '') \n");
        sql.append(" or (ai.nomeCodEmpresaAcessou = (concat(concat(empL.codigo, ' - '), empL.nome)))) \n");
        sql.append(" and ((ai.nomeCpfEmailClienteOrigem is null) or (ai.nomeCpfEmailClienteOrigem = '')) \n");

        //Considerar só o primeiro acesso do dia
        if (filtros.getConsiderarPrimeiroAcessoDia() != null && filtros.getConsiderarPrimeiroAcessoDia()) {
            sql.append("AND ai.codigo IN ( SELECT MIN(ac2.codigo) FROM acessocliente ac2 WHERE ac2.cliente = ai.cliente GROUP BY TO_CHAR(ac2.dthrentrada, 'DD/MM/YYYY'))");
        }

        // colaborador
        sql.append(" UNION  \n");
        sql.append(" SELECT  \n");
        sql.append(" ac.dthrentrada as entrada, \n");
        sql.append(" ac.codigo as codAcesso,  \n");
        sql.append(" '' as situacaoAcesso,  \n");
        sql.append(" '' as nomeCodEmpresaAcessou,  \n");
        sql.append(" '' as  situacao, \n");
        sql.append(" po.codigo as pessoa, \n");
        sql.append(" po.nome as nome, \n");
        sql.append(" 'COLABORADOR' as matricula, \n");
        sql.append(" po.sexo as sexo, \n");
        sql.append(" EXTRACT ('YEAR' FROM (age(now(), po.datanasc))) as idade, \n");
        sql.append(" null as plano, \n");
        sql.append(" '' as nomePlano, \n");
        sql.append(" null as modalidade, \n");
        sql.append(" '' as nomeModalidade, \n");
        sql.append(" null as  cliente, \n");
        sql.append(" co.codigo as colaborador, \n");
        sql.append(" emp.codigo as empresa, \n");
        sql.append(" emp.nome as nomeEmpresa , \n");
        sql.append(" '' as nomeCodEmpresaOrigem, \n");
        sql.append(" '' as nomeCpfEmailClienteOrigem \n");
        sql.append(" FROM acessocolaborador ac \n");
        sql.append(" INNER JOIN colaborador co ON ac.colaborador = co.codigo  \n");
        sql.append(" INNER JOIN pessoa po ON co.pessoa = po.codigo  \n");
        sql.append(" INNER JOIN empresa emp on emp.codigo = co.empresa  \n");

        if (!UteisValidacao.emptyNumber(filtros.getEmpresa())) {
            sql.append("WHERE empresa = ").append(filtros.getEmpresa()).append(" \n");
        }
        //Considerar só o primeiro acesso do dia
        if (filtros.getConsiderarPrimeiroAcessoDia() != null && filtros.getConsiderarPrimeiroAcessoDia()) {
            sql.append("AND ac.codigo IN (SELECT MIN(ac2.codigo) FROM acessocolaborador ac2 WHERE ac2.colaborador = ac.colaborador GROUP BY TO_CHAR(ac2.dthrentrada, 'DD/MM/YYYY'))");
        }

        // cliente outra empresa acessou nessa unidade
        sql.append(" UNION  \n");
        sql.append(" SELECT  \n");
        sql.append(" ai.dthrentrada as entrada, \n");
        sql.append(" ai.codigo as codAcesso,  \n");
        sql.append(" ai.situacao as situacaoAcesso,  \n");
        sql.append(" ai.nomeCodEmpresaAcessou,  \n");
        sql.append(" '' as  situacao, \n");
        sql.append(" null as  pessoa, \n");
        sql.append(" (STRING_TO_ARRAY(ai.nomeCpfEmailClienteOrigem, ';'))[2] as nome, \n");
        sql.append(" (STRING_TO_ARRAY(ai.nomeCpfEmailClienteOrigem, ';'))[1] as matricula, \n");
        sql.append(" '' as  sexo, \n");
        sql.append(" null as  idade, \n");
        sql.append(" null as plano, \n");
        sql.append(" '' as  nomePlano, \n");
        sql.append(" null as  modalidade, \n");
        sql.append(" '' as  nomeModalidade, \n");
        sql.append(" null as  cliente, \n");
        sql.append(" null as colaborador, \n");
        sql.append(" substring(ai.nomeCodEmpresaOrigem FROM '^(.*?) - (.*)$')::integer as empresa, \n");
        sql.append(" substring(ai.nomeCodEmpresaOrigem FROM '^.*? - (.*)$') as nomeEmpresa, \n");
        sql.append(" ai.nomeCodEmpresaOrigem, \n");
        sql.append(" ai.nomeCpfEmailClienteOrigem \n");
        sql.append(" FROM acessocliente ai \n");
        sql.append(" inner join LocalAcesso loc ON loc.codigo = ai.localAcesso  \n");
        sql.append(" inner join empresa empL ON loc.empresa = empL.codigo  \n");
        sql.append(" WHERE ai.nomeCodEmpresaOrigem is not null and ai.nomeCodEmpresaOrigem <> ''  \n");
        sql.append(" AND ai.nomeCpfEmailClienteOrigem is not null and ai.nomeCpfEmailClienteOrigem <> ''  \n");
        sql.append(" AND ai.nomeCodEmpresaOrigem <> ai.nomeCodEmpresaAcessou  \n");
        sql.append(") as sql \n");
        sql.append("WHERE sql.entrada >= '").append(Uteis.getData(filtros.getPeriodoInicial(), "bd")).append(" 00:00:00'  \n");
        sql.append("AND sql.entrada <= '").append(Uteis.getData(filtros.getPeriodoFinal(), "bd")).append(" 23:59:59' \n");

        if (!UteisValidacao.emptyNumber(filtros.getFaixaEtariaInicial())) {
            sql.append("AND ((sql.idade is null) or (sql.idade >= ").append(filtros.getFaixaEtariaInicial()).append(")) \n");
        }

        if (!UteisValidacao.emptyNumber(filtros.getFaixaEtariaFinal())) {
            sql.append("AND ((sql.idade is null) or (sql.idade <= ").append(filtros.getFaixaEtariaFinal()).append(")) \n");
        }

        if (!UteisValidacao.emptyString(filtros.getSexo())) {
            sql.append("AND ((sql.sexo = '') or (sql.sexo = '").append(filtros.getSexo()).append("')) \n");
        }

        if (!UteisValidacao.emptyNumber(filtros.getColaborador())) {
            sql.append("AND ((sql.colaborador is null) or (sql.colaborador = ").append(filtros.getColaborador()).append(")) \n");
        }

        if (!UteisValidacao.emptyNumber(filtros.getCliente())) {
            sql.append("AND ((sql.cliente is null) or (sql.cliente = ").append(filtros.getCliente()).append(")) \n");
        }

        if (!UteisValidacao.emptyNumber(filtros.getPlano())) {
            sql.append("AND ((sql.plano is null) or (sql.plano = ").append(filtros.getPlano()).append(")) \n");
        }

        if (!UteisValidacao.emptyNumber(filtros.getModalidade())) {
            sql.append("AND ((sql.modalidade is null) or (sql.modalidade = ").append(filtros.getModalidade()).append(")) \n");
        }

        if (!filtros.isExibirAcessosBloqueados()) {
            sql.append(" AND sql.situacaoAcesso not like 'RV_BLOQ%' \n");
        }
        return sql.toString();
    }

    private String sqlBase(FiltroTotalizadorAcessosJSON filtros, boolean comColunas) throws ServiceException {
        StringBuilder sql = new StringBuilder();

        if (comColunas) {
            sql.append("SELECT  \n");
            sql.append("*  \n");
        }
        sql.append(" FROM ( \n");
        sql.append(" SELECT  \n");
        sql.append(" ai.dthrentrada as entrada, \n");
        sql.append(" ai.codigo as codAcesso,  \n");
        sql.append(" ai.situacao as situacaoAcesso,  \n");
        sql.append(" ci.situacao as situacao, \n");
        sql.append(" pc.codigo as pessoa, \n");
        sql.append(" pc.nome as nome, \n");
        sql.append(" ci.matricula as matricula, \n");
        sql.append(" pc.sexo as sexo, \n");
        sql.append(" EXTRACT ('YEAR' FROM (age(now(), pc.datanasc))) as idade, \n");
        sql.append(" con.plano, \n");
        sql.append(" pl.descricao as nomePlano, \n");
        sql.append(" modalidade.codigo as modalidade, \n");
        sql.append(" modalidade.nome as nomeModalidade, \n");
        sql.append(" ci.codigo as cliente, \n");
        sql.append(" null as colaborador, \n");
        sql.append(" emp.codigo as empresa, \n");
        sql.append(" emp.nome as nomeEmpresa \n");
        sql.append(" FROM acessocliente ai \n");
        sql.append(" INNER JOIN cliente ci ON ai.cliente = ci.codigo  \n");
        sql.append(" INNER JOIN pessoa pc ON ci.pessoa = pc.codigo  \n");
        sql.append(" LEFT JOIN situacaoclientesinteticodw  sw ON sw.codigocliente = ci.codigo \n");
        sql.append(" LEFT JOIN contrato con ON sw.codigocontrato = con.codigo \n");
        sql.append(" LEFT JOIN contratomodalidade conm on sw.codigocontrato = conm.contrato \n");
        sql.append(" LEFT JOIN modalidade on modalidade.codigo = conm.modalidade \n");
        sql.append(" LEFT JOIN plano pl ON pl.codigo = con.plano \n");
        sql.append(" INNER JOIN empresa emp on emp.codigo = ci.empresa \n");
        sql.append(" UNION  \n");
        sql.append(" SELECT  \n");
        sql.append(" ac.dthrentrada as entrada, \n");
        sql.append(" ac.codigo as codAcesso, \n");
        sql.append(" '' as situacaoAcesso, \n");
        sql.append(" '' as situacao, \n");
        sql.append(" po.codigo as pessoa, \n");
        sql.append(" po.nome as nome, \n");
        sql.append(" 'COLABORADOR' as matricula, \n");
        sql.append(" po.sexo as sexo, \n");
        sql.append(" EXTRACT ('YEAR' FROM (age(now(), po.datanasc))) as idade, \n");
        sql.append(" null as plano, \n");
        sql.append(" '' as nomePlano, \n");
        sql.append(" null as modalidade, \n");
        sql.append(" '' as nomeModalidade, \n");
        sql.append(" null as cliente, \n");
        sql.append(" co.codigo as colaborador, \n");
        sql.append(" emp.codigo as empresa, \n");
        sql.append(" emp.nome as nomeEmpresa \n");
        sql.append(" FROM acessocolaborador ac \n");
        sql.append(" INNER JOIN colaborador co ON ac.colaborador = co.codigo  \n");
        sql.append(" INNER JOIN pessoa po ON co.pessoa = po.codigo  \n");
        sql.append(" INNER JOIN empresa emp on emp.codigo = co.empresa  \n");
        sql.append(") as sql \n");
        sql.append("WHERE sql.entrada >= '").append(Uteis.getData(filtros.getPeriodoInicial(), "bd")).append(" 00:00:00'  \n");
        sql.append("AND sql.entrada <= '").append(Uteis.getData(filtros.getPeriodoFinal(), "bd")).append(" 23:59:59' \n");

        if (!UteisValidacao.emptyNumber(filtros.getFaixaEtariaInicial())) {
            sql.append("AND sql.idade >= ").append(filtros.getFaixaEtariaInicial()).append(" \n");
        }

        if (!UteisValidacao.emptyNumber(filtros.getFaixaEtariaFinal())) {
            sql.append("AND sql.idade <= ").append(filtros.getFaixaEtariaFinal()).append(" \n");
        }

        if (!UteisValidacao.emptyString(filtros.getSexo())) {
            sql.append("AND sql.sexo = '").append(filtros.getSexo()).append("' \n");
        }

        if (!UteisValidacao.emptyNumber(filtros.getEmpresa())) {
            sql.append("AND sql.empresa = ").append(filtros.getEmpresa()).append(" \n");
        }

        if (!UteisValidacao.emptyNumber(filtros.getColaborador())) {
            sql.append("AND sql.colaborador = ").append(filtros.getColaborador()).append(" \n");
        }

        if (!UteisValidacao.emptyNumber(filtros.getCliente())) {
            sql.append("AND sql.cliente = ").append(filtros.getCliente()).append(" \n");
        }

        if (!UteisValidacao.emptyNumber(filtros.getPlano())) {
            sql.append("AND sql.plano = ").append(filtros.getPlano()).append(" \n");
        }

        if (!UteisValidacao.emptyNumber(filtros.getModalidade())) {
            sql.append("AND sql.modalidade = ").append(filtros.getModalidade()).append(" \n");
        }

        if (!filtros.isExibirAcessosBloqueados()) {
            sql.append(" AND sql.situacaoAcesso not like 'RV_BLOQ%' \n");
        }
        return sql.toString();
    }
}
