package com.pacto.relatorioms.services.implementations;

import com.pacto.config.dto.PaginadorDTO;
import com.pacto.config.exceptions.ServiceException;
import com.pacto.relatorioms.dao.interfaces.RelatorioGogoodDao;
import com.pacto.relatorioms.dto.RelatorioGogoodDTO;
import com.pacto.relatorioms.filter.FiltroRelatorioGogoodJSON;
import com.pacto.relatorioms.filter.RelatorioGogoodFilter;
import com.pacto.relatorioms.services.interfaces.RelatorioGogoodService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;


@Service
public class RelatorioGogoodImpl implements RelatorioGogoodService {

    @Autowired
    private RelatorioGogoodDao relatorioGogoodDao;

    @Override
    public List<RelatorioGogoodDTO> consultarAlunosGogood(FiltroRelatorioGogoodJSON filtroJSON, PaginadorDTO paginadorDTO) throws ServiceException {
        try {
            RelatorioGogoodFilter relatorioGogoodFilter = new RelatorioGogoodFilter();
            return relatorioGogoodDao.consultarAlunosGogood(filtroJSON, relatorioGogoodFilter, paginadorDTO);
        } catch (Exception e) {
            e.printStackTrace();
            throw new ServiceException(e);
        }
    }
}
