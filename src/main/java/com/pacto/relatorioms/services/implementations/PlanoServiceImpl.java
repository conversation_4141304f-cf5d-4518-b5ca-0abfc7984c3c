package com.pacto.relatorioms.services.implementations;

import com.pacto.relatorioms.adapters.PlanoAdapter;
import com.pacto.relatorioms.dao.interfaces.PlanoDao;
import com.pacto.relatorioms.dto.PlanoDTO;
import com.pacto.relatorioms.entities.Plano;
import com.pacto.config.exceptions.ServiceException;
import com.pacto.relatorioms.filter.FiltroPlanoJSON;
import com.pacto.config.security.interfaces.RequestService;
import com.pacto.relatorioms.services.interfaces.PlanoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;


@Service
public class PlanoServiceImpl implements PlanoService {

    @Autowired
    private PlanoDao planoDao;
    @Autowired
    private PlanoAdapter planoAdapter;

    private final RequestService requestService;

    public PlanoServiceImpl(RequestService requestService) {
        this.requestService = requestService;
    }

    @Override
    public List<PlanoDTO> findAll(FiltroPlanoJSON filtros) throws ServiceException {
        try {
            List<Plano> planos = planoDao.findAll(filtros);
            List<PlanoDTO> planosDtos = planoAdapter.toDtos(planos);
            return planosDtos;
        } catch (Exception e) {
            e.printStackTrace();
            throw new ServiceException(e);
        }
    }

}
