package com.pacto.relatorioms.services.implementations;

import com.pacto.relatorioms.adapters.*;
import com.pacto.relatorioms.dao.interfaces.ColaboradorDao;
import com.pacto.relatorioms.dao.interfaces.ConfiguracaoSistemaDao;
import com.pacto.relatorioms.dto.ClienteDTO;
import com.pacto.relatorioms.dto.ColaboradorDTO;
import com.pacto.relatorioms.dto.ItemRelatorioPersonalDTO;
import com.pacto.relatorioms.dto.RelatorioPersonalTotaisDTO;
import com.pacto.config.dto.PaginadorDTO;
import com.pacto.relatorioms.entities.*;
import com.pacto.config.exceptions.ServiceException;
import com.pacto.relatorioms.filter.FiltroRelatorioPersonalJSON;
import com.pacto.config.security.interfaces.RequestService;
import com.pacto.relatorioms.services.interfaces.RelatorioPersonalService;
import com.pacto.config.utils.Uteis;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Locale;


@Service
public class RelatorioPersonalServiceImpl implements RelatorioPersonalService {

    private final MessageSource messageSource;
    private final RequestService requestService;

    @Autowired
    private ColaboradorDao colaboradorDao;
    @Autowired
    private ConfiguracaoSistemaDao configuracaoSistemaDao;
    @Autowired
    private ColaboradorAdapter colaboradorAdapter;
    @Autowired
    PessoaAdapter pessoaAdapter;
    @Autowired
    ClienteAdapter clienteAdapter;
    @Autowired
    MovParcelaAdapter movParcelaAdapter;
    @Autowired
    EmpresaAdapter empresaAdapter;
    @Autowired
    ProdutoAdapter produtoAdapter;

    public RelatorioPersonalServiceImpl(MessageSource messageSource, RequestService requestService) {
        this.messageSource = messageSource;
        this.requestService = requestService;
    }

    @Override
    public List<ItemRelatorioPersonalDTO> consultarRelatorioPersonal(FiltroRelatorioPersonalJSON filtros, PaginadorDTO paginadorDTO) throws ServiceException {
        try {
            validacoes(filtros);
            List<ItemRelatorioPersonalDTO> itensRelatorioPersonalDTO = new ArrayList<>();
            List<Object[]> objectsPersonais = colaboradorDao.consultarRelatorioPersonal(filtros, paginadorDTO);
            if (objectsPersonais != null) {
                itensRelatorioPersonalDTO = montarItensRelatorioPersonal(objectsPersonais);
                return itensRelatorioPersonalDTO;
            } else {
                return itensRelatorioPersonalDTO;
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw new ServiceException(e);
        }
    }

    @Override
    public RelatorioPersonalTotaisDTO consultarTotais(FiltroRelatorioPersonalJSON filtros) {
        RelatorioPersonalTotaisDTO relatorioPersonalTotaisDTO = new RelatorioPersonalTotaisDTO();
        relatorioPersonalTotaisDTO.setTotalLancado(colaboradorDao.consultarTotalLancado(filtros));
        relatorioPersonalTotaisDTO.setTotalPago(colaboradorDao.consultarTotalPago(filtros));
        return relatorioPersonalTotaisDTO;
    }

    private List<ItemRelatorioPersonalDTO> montarItensRelatorioPersonal(List<Object[]> objects) throws Exception {
        List<ItemRelatorioPersonalDTO> itensRelatorioPersonalDTO = new ArrayList<>();
        ConfiguracaoSistema configuracaoSistema = configuracaoSistemaDao.findAll().get(0);
        for (Object[] obj : objects) {
            ItemRelatorioPersonalDTO itemRelatorioPersonalDTO = new ItemRelatorioPersonalDTO();
            ColaboradorDTO personal = colaboradorAdapter.toDto((Colaborador) obj[0]);
            itemRelatorioPersonalDTO.setPersonal(personal.getPessoa().getNome());
            ClienteDTO aluno = clienteAdapter.toDto(((Cliente) obj[1]));
            itemRelatorioPersonalDTO.setAluno(aluno.getPessoa().getNome());
            // Dia vencimento personal
            if(personal.getDiaVencimento() <= 0) {
                personal.setDiaVencimento(configuracaoSistema.getVencimentoColaborador());
            }
            // Produtos, parcelas, situação
            ItemTaxaPersonal itemTaxaPersonal = new ItemTaxaPersonal();
            try {
                itemTaxaPersonal = (ItemTaxaPersonal) obj[2];
            } catch (Exception e) {
            }

            if (itemTaxaPersonal != null && itemTaxaPersonal.getCodigo() > 0) {
                itemRelatorioPersonalDTO.setProduto(itemTaxaPersonal.getProduto().getDescricao());
                itemRelatorioPersonalDTO.setValor(itemTaxaPersonal.getMovProduto().getProduto().getValorFinal());
                itemRelatorioPersonalDTO.setControleTaxaPersonal(itemTaxaPersonal.getControle().getCodigo());
                Double valorDesconto = itemTaxaPersonal.getMovProduto().getValorDesconto();
                if (valorDesconto > 0.0) {
                    itemRelatorioPersonalDTO.setDesconto(valorDesconto);
                } else {
                    itemRelatorioPersonalDTO.setDesconto(0.0);
                }
                itemRelatorioPersonalDTO.setValorFinal(itemTaxaPersonal.getMovProduto().getTotalFinal());
                if (itemTaxaPersonal.getMovProduto().getSituacao().equalsIgnoreCase("PG")) {
                    itemRelatorioPersonalDTO.setSituacao("PAGO");
                } else {
                    Boolean existeParcelaVencida = false;
                    for (MovProdutoParcela movProdutoParcela : itemTaxaPersonal.getMovProduto().getMovProdutoParcelas()) {
                        Date hoje = Uteis.getDataComHoraZerada(new Date());
                        Date dataVencimento = Uteis.getDataComHoraZerada(movProdutoParcela.getMovParcela().getDataVencimento());
                        if (hoje.after(dataVencimento)) {
                            existeParcelaVencida = true;
                        }
                    }
                    if (existeParcelaVencida) {
                        itemRelatorioPersonalDTO.setSituacao("VENCIDO");
                    } else {
                        itemRelatorioPersonalDTO.setSituacao("NEGOCIADO");
                    }
                }
                for(MovProdutoParcela mpp: itemTaxaPersonal.getMovProduto().getMovProdutoParcelas()) {
                    itemRelatorioPersonalDTO.setParcela(movParcelaAdapter.toDto(mpp.getMovParcela()));
                }
            } else {
                itemRelatorioPersonalDTO.setDesconto(0.0);
                itemRelatorioPersonalDTO.setValor(0.0);
                itemRelatorioPersonalDTO.setValorFinal(0.0);
                itemRelatorioPersonalDTO.setProduto("");
                itemRelatorioPersonalDTO.setSituacao("LIVRE");
            }
            itensRelatorioPersonalDTO.add(itemRelatorioPersonalDTO);
        }
        return itensRelatorioPersonalDTO;
    }

    public void validacoes(FiltroRelatorioPersonalJSON filtros) throws ServiceException {
        if (filtros.getMesReferencia() == null) {
            throw new ServiceException(messageSource.getMessage("relatorio.personal.data-nao-informada", null, new Locale(requestService.getLocale())));
        }
    }
}
