package com.pacto.relatorioms.services.implementations;

import com.pacto.relatorioms.dao.interfaces.RelatorioConvidadosDao;
import com.pacto.relatorioms.dto.RelatorioConvidadosDTO;
import com.pacto.config.dto.PaginadorDTO;
import com.pacto.config.exceptions.ServiceException;
import com.pacto.relatorioms.filter.FiltroRelatorioConvidadosJSON;
import com.pacto.relatorioms.services.interfaces.RelatorioConvidadosService;
import com.pacto.relatorioms.services.interfaces.RelatorioGympassService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;


@Service
public class RelatorioConvidadosImpl implements RelatorioConvidadosService {

    @Autowired
    private RelatorioConvidadosDao relatorioConvidadosDao;

    public List<RelatorioConvidadosDTO> consultarAlunosConvidados(FiltroRelatorioConvidadosJSON filtroJSON, PaginadorDTO paginadorDTO) throws ServiceException {
        try {
            List<RelatorioConvidadosDTO> relatorioConvidadosDTOS = relatorioConvidadosDao.consultarAlunosConvidados(filtroJSON, paginadorDTO);
            return relatorioConvidadosDTOS;
        } catch (Exception e) {
            e.printStackTrace();
            throw new ServiceException(e);
        }
    }
}
