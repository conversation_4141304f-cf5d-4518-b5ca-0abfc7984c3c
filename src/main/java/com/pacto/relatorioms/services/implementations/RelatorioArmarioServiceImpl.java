package com.pacto.relatorioms.services.implementations;

import com.pacto.relatorioms.adapters.AluguelArmarioAdapter;
import com.pacto.relatorioms.adapters.ContratoAdapter;
import com.pacto.relatorioms.dao.interfaces.ConfiguracaoSistemaDao;
import com.pacto.relatorioms.dao.interfaces.RelatorioArmarioDao;
import com.pacto.relatorioms.dto.AluguelArmarioDTO;
import com.pacto.relatorioms.dto.AluguelArmarioRelDTO;
import com.pacto.relatorioms.dto.ContratoDTO;
import com.pacto.config.dto.PaginadorDTO;
import com.pacto.relatorioms.entities.AluguelArmario;
import com.pacto.relatorioms.entities.ConfiguracaoSistema;
import com.pacto.relatorioms.entities.Contrato;
import com.pacto.config.exceptions.ServiceException;
import com.pacto.relatorioms.filter.RelatorioArmarioFiltroJSON;
import com.pacto.relatorioms.services.interfaces.RelatorioArmarioService;
import com.pacto.config.utils.Uteis;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Service
public class RelatorioArmarioServiceImpl implements RelatorioArmarioService {

    private final RelatorioArmarioDao relatorioArmarioDao;
    private final ConfiguracaoSistemaDao configuracaoSistemaDao;
    private final AluguelArmarioAdapter aluguelArmarioAdapter;
    private final ContratoAdapter contratoAdapter;

    public RelatorioArmarioServiceImpl(RelatorioArmarioDao relatorioArmarioDao, ConfiguracaoSistemaDao configuracaoSistemaDao, AluguelArmarioAdapter aluguelArmarioAdapter, ContratoAdapter contratoAdapter) {
        this.relatorioArmarioDao = relatorioArmarioDao;
        this.configuracaoSistemaDao = configuracaoSistemaDao;
        this.aluguelArmarioAdapter = aluguelArmarioAdapter;
        this.contratoAdapter = contratoAdapter;
    }

    @Override
    public List<AluguelArmarioRelDTO> consultarArmarios(RelatorioArmarioFiltroJSON filtrosJSON, PaginadorDTO paginadorDTO) throws ServiceException {
        try {
            ConfiguracaoSistema configuracaoSistema = configuracaoSistemaDao.findAll().get(0);
            filtrosJSON.setHabilitadoGestaoArmarios(configuracaoSistema.getHabilitarGestaoArmarios());
            List<Object[]> objects = relatorioArmarioDao.consultarArmarios(filtrosJSON, paginadorDTO);
            List<AluguelArmarioRelDTO> aluguelArmarioRelDTOS = new ArrayList<>();
            for (Object[] line : objects) {
                AluguelArmarioDTO aluguelArmarioDTO = aluguelArmarioAdapter.toDto((AluguelArmario) line[0]);
                ContratoDTO contratoDTO = contratoAdapter.toDto((Contrato) line[1]);
                AluguelArmarioRelDTO aluguelArmarioRelDTO = new AluguelArmarioRelDTO();
                aluguelArmarioRelDTO.setAluguelArmario(aluguelArmarioDTO);
                aluguelArmarioRelDTO.setContrato(contratoDTO);
                aluguelArmarioRelDTO.setNome(aluguelArmarioDTO.getCliente().getPessoa().getNome());
                aluguelArmarioRelDTO.setMatricula(aluguelArmarioDTO.getCliente().getMatricula());
                aluguelArmarioRelDTO.setNumeroArmario(aluguelArmarioDTO.getArmario().getDescricao());
                aluguelArmarioRelDTO.setTamanho(aluguelArmarioDTO.getArmario().getTamanhoArmario().getDescricao());
                String tipoGrupoArmario = "-";
                if (aluguelArmarioDTO.getArmario().getGrupo().equals("M")) {
                    tipoGrupoArmario = "Masculino";
                } else if (aluguelArmarioDTO.getArmario().getGrupo().equals("F")) {
                    tipoGrupoArmario = "Feminino";
                } else {
                    tipoGrupoArmario = "Unisex";
                }
                aluguelArmarioRelDTO.setTipo(tipoGrupoArmario);
                if (aluguelArmarioDTO.getDataInicio() != null) {
                    aluguelArmarioRelDTO.setDataInicio(Uteis.getData(aluguelArmarioDTO.getDataInicio(), "br"));
                } else {
                    aluguelArmarioRelDTO.setDataInicio("-");
                }
                if (aluguelArmarioDTO.getFimOriginal() != null) {
                    aluguelArmarioRelDTO.setDataFim(Uteis.getData(aluguelArmarioDTO.getFimOriginal(), "br"));
                } else {
                    aluguelArmarioRelDTO.setDataFim("-");
                }
                aluguelArmarioRelDTOS.add(aluguelArmarioRelDTO);
            }
            return aluguelArmarioRelDTOS;
        } catch (Exception ex) {
            ex.printStackTrace();
            throw new ServiceException(ex);
        }
    }

}
