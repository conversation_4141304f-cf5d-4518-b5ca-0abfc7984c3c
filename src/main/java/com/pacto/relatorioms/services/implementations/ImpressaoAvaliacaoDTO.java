package com.pacto.relatorioms.services.implementations;


import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ImpressaoAvaliacaoDTO {

    private Map<String, Object> parametros = new HashMap<>();
    private List<ItemRelatorioTO> parq = new ArrayList<ItemRelatorioTO>();
    private List<ItemRelatorioTO> dobras = new ArrayList<ItemRelatorioTO>();
    private List<ItemRelatorioTO> perimetria = new ArrayList<ItemRelatorioTO>();
    private List<ItemRelatorioTO> anamnese = new ArrayList<ItemRelatorioTO>();
    private List<ComparativoTO> comparativo = new ArrayList<>();

    public List<ComparativoTO> getComparativo() {
        return comparativo;
    }

    public void setComparativo(List<ComparativoTO> comparativo) {
        this.comparativo = comparativo;
    }

    public List<ItemRelatorioTO> getAnamnese() {
        return anamnese;
    }

    public void setAnamnese(List<ItemRelatorioTO> anamnese) {
        this.anamnese = anamnese;
    }
    public Map<String, Object> getParametros() {
        return parametros;
    }

    public void setParametros(Map<String, Object> parametros) {
        this.parametros = parametros;
    }

    public List<ItemRelatorioTO> getParq() {
        return parq;
    }

    public void setParq(List<ItemRelatorioTO> parq) {
        this.parq = parq;
    }

    public List<ItemRelatorioTO> getDobras() {
        return dobras;
    }

    public void setDobras(List<ItemRelatorioTO> dobras) {
        this.dobras = dobras;
    }

    public List<ItemRelatorioTO> getPerimetria() {
        return perimetria;
    }

    public void setPerimetria(List<ItemRelatorioTO> perimetria) {
        this.perimetria = perimetria;
    }
}
