package com.pacto.relatorioms.services.implementations;

import com.pacto.config.utils.UteisValidacao;
import com.pacto.relatorioms.dao.interfaces.ContratoDao;
import com.pacto.relatorioms.dto.*;
import com.pacto.config.dto.PaginadorDTO;
import com.pacto.relatorioms.enums.SituacaoClienteEnum;
import com.pacto.config.exceptions.ServiceException;
import com.pacto.relatorioms.filter.FiltroRelatorioContratoPorDuracaoJSON;
import com.pacto.config.security.interfaces.RequestService;
import com.pacto.relatorioms.services.interfaces.RelatorioContratosPorDuracaoService;
import com.pacto.config.utils.Uteis;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.sql.ResultSet;
import java.util.*;

@Service
public class RelatorioContratosPorDuracaoServiceImpl implements RelatorioContratosPorDuracaoService {

    @Autowired
    MessageSource messageSource;
    @Autowired
    private RequestService requestService;
    @Autowired
    ContratoDao contratoDao;

    @Override
    public RelatorioContratosPorDuracaoDTO consultarContratosPorDuracao(FiltroRelatorioContratoPorDuracaoJSON filtros, PaginadorDTO paginadorDTO) throws ServiceException {
        validacoes(filtros);
        RelatorioContratosPorDuracaoDTO relatorioContratosPorDuracaoDTO = consultarDuracaoPorPeriodoSituacaoContratoAnalitico(filtros);
        if (paginadorDTO.getSize() != null && paginadorDTO.getPage() != null) {
            if (Uteis.notNullAndNotEmpty(paginadorDTO.getSort())) {
                String campoOrdenacao = paginadorDTO.getSort().split(",")[0];
                String direcao = paginadorDTO.getSort().split(",")[1];
                ordenarLista(relatorioContratosPorDuracaoDTO.getContratosPorDuracao(), "com.pacto.relatorioms.dto.ContratoPorDuracaoDTO", campoOrdenacao, direcao);
            }
            relatorioContratosPorDuracaoDTO.setContratosPorDuracao(paginarLista(relatorioContratosPorDuracaoDTO.getContratosPorDuracao(), paginadorDTO));
        }
        return relatorioContratosPorDuracaoDTO;
    }

    @Override
    public List<ClientePorDuracaoDTO> consultarClientesPorDuracao(FiltroRelatorioContratoPorDuracaoJSON filtros, Integer duracao, PaginadorDTO paginadorDTO) throws ServiceException {
        List<ClientePorDuracaoDTO> clientePorDuracaoDTOS = new ArrayList<>();
        clientePorDuracaoDTOS.addAll(consultarListaClientesPorDuracao(filtros, duracao, paginadorDTO));
        if (paginadorDTO.getSize() != null && paginadorDTO.getPage() != null) {
            if (paginadorDTO.getSize() != null && paginadorDTO.getPage() != null) {
                if (Uteis.notNullAndNotEmpty(paginadorDTO.getSort())) {
                    String sortField = paginadorDTO.getSort().split(",")[0];
                    String sortDirection = paginadorDTO.getSort().split(",")[1];
                    ordenarLista(clientePorDuracaoDTOS, "com.pacto.relatorioms.dto.ClientePorDuracaoDTO", sortField, sortDirection);
                }
                clientePorDuracaoDTOS = paginarLista(clientePorDuracaoDTOS, paginadorDTO);
            }
        }
        return clientePorDuracaoDTOS;
    }

    private void agruparDuracaoEmpresa(RelatorioContratosPorDuracaoDTO relatorioContratosPorDuracaoDTO) {
        List<ContratoPorDuracaoDTO> listaFinal = new ArrayList<>();
        for (ContratoPorDuracaoDTO obj : relatorioContratosPorDuracaoDTO.getContratosPorDuracao()) {
            ContratoPorDuracaoDTO novo = null;

            for (ContratoPorDuracaoDTO lista : listaFinal) {
                if (lista.getEmpresa().getCodigo().equals(obj.getEmpresa().getCodigo()) &&
                        lista.getNumeroMeses().equals(obj.getNumeroMeses())) {
                    novo = lista;
                    novo.setQuantidade(novo.getQuantidade() + obj.getQuantidade());
                    novo.setPercentual(Uteis.arrendondarForcando2CadasDecimaisComVirgula((novo.getQuantidade() * 100.0) / relatorioContratosPorDuracaoDTO.getTotalizador()) + "%");
                    novo.setPercentualDouble((novo.getQuantidade() * 100.0) / relatorioContratosPorDuracaoDTO.getTotalizador());
                }
            }
            if (novo == null) {
                novo = obj;
                listaFinal.add(novo);
            }
        }
        relatorioContratosPorDuracaoDTO.setContratosPorDuracao(listaFinal);
    }

    public List paginarLista(List lista, PaginadorDTO paginadorDTO) {
        List listaPaginada = new ArrayList<>();
        if (paginadorDTO != null) {
            // Paginar lista
            int primeiroPaginacao = (int) (paginadorDTO.getPage() * paginadorDTO.getSize());
            int ultimoRegistro = (int) ((paginadorDTO.getPage() * paginadorDTO.getSize()) + paginadorDTO.getSize());
            if (ultimoRegistro > lista.size()) {
                listaPaginada.addAll(lista.subList(primeiroPaginacao, lista.size()));
            } else {
                listaPaginada.addAll(lista.subList(primeiroPaginacao, ultimoRegistro));
            }
        }
        if (paginadorDTO != null) {
            paginadorDTO.setQuantidadeTotalElementos((long) lista.size());
        }
        return listaPaginada;
    }

    public void ordenarLista(List<?> lista, String className, String campoOrdenacao, String direcaoOrdenacao) {
        if (campoOrdenacao.equals("percentual")) {
            campoOrdenacao = "percentualDouble";
        }

        String nomeMetodoGet = "get" + StringUtils.capitalize(campoOrdenacao);
        String finalCampoOrdenacao = campoOrdenacao;
        lista.sort(new Comparator<Object>() {
            @Override
            public int compare(Object a, Object b) {
                int resultadoOrdenacao = 0;
                try {
                    Method getMethod = getMethod = Class.forName(className).getMethod(nomeMetodoGet);
                    if (getMethod != null) {
                        try {
                            if (finalCampoOrdenacao.contains("data") && getMethod.getReturnType() == String.class) {
                                Date a1 = Uteis.getDate((String) getMethod.invoke(a), "dd/MM/yyyy");
                                Date b1 = Uteis.getDate((String) getMethod.invoke(b), "dd/MM/yyyy");
                                if (a1.after(b1)) {
                                    resultadoOrdenacao = 1;
                                } else if (a1.before(b1)) {
                                    resultadoOrdenacao = -1;
                                } else {
                                    resultadoOrdenacao = 0;
                                }
                            } else if (getMethod.getReturnType() == String.class) {
                                String a1 = (String) getMethod.invoke(a);
                                String b1 = (String) getMethod.invoke(b);
                                resultadoOrdenacao = a1.compareTo(b1);
                            } else if (getMethod.getReturnType() == Integer.class) {
                                Integer a1 = (Integer) getMethod.invoke(a);
                                Integer b1 = (Integer) getMethod.invoke(b);
                                if (a1 < b1) {
                                    resultadoOrdenacao = 1;
                                } else if (a1 > b1) {
                                    resultadoOrdenacao = -1;
                                } else {
                                    resultadoOrdenacao = 0;
                                }
                            } else if (getMethod.getReturnType() == Double.class) {
                                Double a1 = (Double) getMethod.invoke(a);
                                Double b1 = (Double) getMethod.invoke(b);
                                if (a1 < b1) {
                                    resultadoOrdenacao = 1;
                                } else if (a1 > b1) {
                                    resultadoOrdenacao = -1;
                                } else {
                                    resultadoOrdenacao = 0;
                                }
                            }
                        } catch (IllegalAccessException e) {
                            e.printStackTrace();
                        } catch (InvocationTargetException e) {
                            e.printStackTrace();
                        }
                        if (direcaoOrdenacao.equalsIgnoreCase("DESC")) {
                            // inverter sinal e consequentemente a ordenação
                            resultadoOrdenacao = resultadoOrdenacao * -1;
                        }
                    }
                } catch (Exception e) {
                }
                return resultadoOrdenacao;
            }
        });
    }

    private void validacoes(FiltroRelatorioContratoPorDuracaoJSON filtros) throws ServiceException {
        if (filtros.getData() == null) {
            throw new ServiceException(messageSource.getMessage("relatorio.contratos.por.duracao.data.nao.informada", null, new Locale(requestService.getLocale())));
        }
    }

    private RelatorioContratosPorDuracaoDTO consultarDuracaoPorPeriodoSituacaoContratoAnalitico(FiltroRelatorioContratoPorDuracaoJSON filtros) throws ServiceException {
        RelatorioContratosPorDuracaoDTO relatorioContratosPorDuracaoDTO = new RelatorioContratosPorDuracaoDTO();

        List<SituacaoClienteEnum> situacoesConsultar = new ArrayList<>();
        boolean consultarTodas = filtros.getSituacoes().size() == 0;
        if (consultarTodas) {
            String situacoes = "AT,CR,NO,TR,TV,AV,VE";
            for (String situacao : situacoes.split(",")) {
                situacoesConsultar.add(SituacaoClienteEnum.getSituacaoCliente(situacao.trim()));
            }
        } else {
            situacoesConsultar = filtros.getSituacoes();
        }
        List<ContratoPorDuracaoDTO> contratosPorDuracaoDTOS = new ArrayList<>();
        for (SituacaoClienteEnum situacao : situacoesConsultar) {
            try {
                String sql = gerarSqlConsultaContratoPorDuracao(filtros, situacao.getCodigo());
                contratoDao.createSessionCurrentWork().doWork(connection -> {
                    try {
                        ResultSet resultadoConsulta = contratoDao.createStatement(connection, sql);
                        while (resultadoConsulta.next()) {
                            ContratoPorDuracaoDTO contratoPorDuracaoDTO = new ContratoPorDuracaoDTO();
                            contratoPorDuracaoDTO.setNumeroMeses(resultadoConsulta.getInt("numeromeses"));
                            contratoPorDuracaoDTO.setQuantidade(resultadoConsulta.getInt("quantidade"));
                            contratoPorDuracaoDTO.setSituacao(situacao.getDescricao());
                            EmpresaDTO empresaDTO = new EmpresaDTO();
                            empresaDTO.setCodigo(resultadoConsulta.getInt("empresa"));
                            empresaDTO.setNome(resultadoConsulta.getString("nomeempresa"));
                            contratoPorDuracaoDTO.setEmpresa(empresaDTO);
                            if (empresaDTO.getCodigo() == 0) {
                                contratoPorDuracaoDTO.setNomeEmpresa("TODAS");
                            } else {
                                contratoPorDuracaoDTO.setNomeEmpresa(resultadoConsulta.getString("nomeempresa"));
                            }

                            relatorioContratosPorDuracaoDTO.setTotalizador(relatorioContratosPorDuracaoDTO.getTotalizador() + resultadoConsulta.getInt("quantidade"));
                            contratosPorDuracaoDTOS.add(contratoPorDuracaoDTO);
                        }

                    } catch (Exception e) {
                        throw new RuntimeException(e);
                    } finally {
                        connection.close();
                    }
                });
            } catch (Exception e) {
                e.printStackTrace();
                throw new ServiceException(e);
            }
        }
        relatorioContratosPorDuracaoDTO.setContratosPorDuracao(contratosPorDuracaoDTOS);

        relatorioContratosPorDuracaoDTO.getContratosPorDuracao().forEach(obj -> {
            obj.setPercentual(Uteis.arrendondarForcando2CadasDecimaisComVirgula((obj.getQuantidade() * 100.0) / relatorioContratosPorDuracaoDTO.getTotalizador()) + "%");
            obj.setPercentualDouble((obj.getQuantidade() * 100.0) / relatorioContratosPorDuracaoDTO.getTotalizador());
        });

        if (consultarTodas) {
            agruparDuracaoEmpresa(relatorioContratosPorDuracaoDTO);
        }

        return relatorioContratosPorDuracaoDTO;
    }

    private String gerarSqlConsultaContratoPorDuracao(FiltroRelatorioContratoPorDuracaoJSON filtros, String situacao) {
        StringBuilder sql = new StringBuilder();
        if (situacao.equals("CR") //carencia
                || situacao.equals("AT") //atestado
                || situacao.equals("TR") //trancamento
                || situacao.equals("TV"))//trancamento vencido
        {
            sql.append("Select count(o.contrato)as quantidade, contrato.empresa, contratoduracao.numeromeses as numeromeses, e.nome as nomeempresa  \n");
            sql.append("from contratooperacao  as o  \n");
            sql.append("inner join contrato on contrato.codigo = o.contrato ");
            sql.append("inner join plano on plano.codigo = contrato.plano ");
            if(!UteisValidacao.emptyNumber(filtros.getPlanoTipo())){
                sql.append(" inner join planoTipo pt on pt.codigo = plano.planoTipo ");
            }
            sql.append("left join planorecorrencia on planorecorrencia.plano = plano.codigo ");
            sql.append("inner join contratoduracao on contratoduracao.contrato = contrato.codigo  \n");
            sql.append("inner join empresa e on e.codigo = contrato.empresa  \n");
            sql.append("where o.tipooperacao in (%s)  \n");
            sql.append("and '%s' between o.datainicioefetivacaooperacao  \n");
            sql.append("and o.datafimefetivacaooperacao ").append(filtros.getSemBolsa() ? "and contrato.bolsa = false" : "");
            if (!filtros.getContabilizarPlanosAutoRenovaveis()) {
                sql.append(" and ((planorecorrencia.codigo IS NULL AND plano.renovavelautomaticamente IS FALSE) OR (planorecorrencia.codigo IS NOT NULL AND planorecorrencia.renovavelautomaticamente IS FALSE))");
            }
            if (!UteisValidacao.emptyNumber(filtros.getEmpresa())) {
                sql.append("and contrato.empresa = ").append(filtros.getEmpresa()).append(" \n");
            }
            if(!UteisValidacao.emptyNumber(filtros.getPlanoTipo())){
                sql.append(" and pt.codigo = ").append(filtros.getPlanoTipo()).append(" \n");
            }
            sql.append(" GROUP BY numeromeses,contrato.empresa,nomeempresa order by numeromeses");

        } else if (situacao.equals("NO")) { //ativos normais

            sql.append("Select count(c.codigo)as quantidade, c.empresa, contratoduracao.numeromeses as numeromeses, e.nome as nomeempresa  \n");
            sql.append("from contrato c  \n");
            sql.append("inner join plano p on p.codigo = c.plano ");
            sql.append("left join planorecorrencia on planorecorrencia.plano = p.codigo ");
            sql.append("inner join empresa e on e.codigo = c.empresa  \n");
            sql.append("inner join contratoduracao on contratoduracao.contrato = c.codigo  \n");
            if(!UteisValidacao.emptyNumber(filtros.getPlanoTipo())){
                sql.append(" inner join planoTipo pt on pt.codigo = p.planoTipo ");
            }
            sql.append("where 1 = 1 \n");
            if (!UteisValidacao.emptyNumber(filtros.getEmpresa())) {
                sql.append("and c.empresa = ").append(filtros.getEmpresa()).append(" \n");
            }
            sql.append("and '%s' between c.vigenciaDe and c.vigenciaAteAjustada  \n");
            sql.append("and not exists (select h.contrato from historicocontrato h  \n");
            sql.append("where h.contrato = c.codigo   \n");
            sql.append("and '%s' between h.datainiciosituacao and h.datafinalsituacao and h.tipohistorico in ('AV', 'VE', 'CA', 'DE'))  \n");
            sql.append("and not exists (select o.contrato from contratooperacao  o  \n");
            sql.append("where o.contrato = c.codigo and o.tipooperacao in ('AT','CR', 'TR','TV')  \n");
            sql.append("and '%s' >= o.datainicioefetivacaooperacao and '%s' <= o.datafimefetivacaooperacao)  \n");
            sql.append((filtros.getSemBolsa() ? "and c.bolsa = false" : ""));
            if (!filtros.getContabilizarPlanosAutoRenovaveis()) {
                sql.append(" and ((planorecorrencia.codigo IS NULL AND p.renovavelautomaticamente IS FALSE) OR (planorecorrencia.codigo IS NOT NULL AND planorecorrencia.renovavelautomaticamente IS FALSE))");
            }
            if(!UteisValidacao.emptyNumber(filtros.getPlanoTipo())){
                sql.append(" and pt.codigo = ").append(filtros.getPlanoTipo()).append(" \n");
            }
            sql.append(" GROUP BY numeromeses,c.empresa,nomeempresa order by numeromeses ");


        } else {//vencido, a vencer

            sql.append("Select count(h.contrato)as quantidade, contrato.empresa, contratoduracao.numeromeses as numeromeses, e.nome as nomeempresa  \n");
            sql.append("from historicocontrato  as h  \n");
            sql.append("inner join contrato on contrato.codigo = h.contrato ");
            sql.append("inner join plano on plano.codigo = contrato.plano ");
            if(!UteisValidacao.emptyNumber(filtros.getPlanoTipo())){
                sql.append(" inner join planoTipo pt on pt.codigo = plano.planoTipo ");
            }
            sql.append("left join planorecorrencia on planorecorrencia.plano = plano.codigo ");
            sql.append("inner join empresa e on e.codigo = contrato.empresa  \n");
            sql.append("inner join contratoduracao on contratoduracao.contrato = contrato.codigo  \n");
            sql.append("where h.tipohistorico = %s  \n");
            sql.append("and '%s' between h.datainiciosituacao  \n");
            sql.append("and h.datafinalsituacao ").append(filtros.getSemBolsa() ? "and contrato.bolsa = false" : " ");
            if (!filtros.getContabilizarPlanosAutoRenovaveis()) {
                sql.append(" and ((planorecorrencia.codigo IS NULL AND plano.renovavelautomaticamente IS FALSE) OR (planorecorrencia.codigo IS NOT NULL AND planorecorrencia.renovavelautomaticamente IS FALSE))");
            }
            if (!UteisValidacao.emptyNumber(filtros.getEmpresa())) {
                sql.append("and contrato.empresa = ").append(filtros.getEmpresa()).append(" \n");
            }
            if(!UteisValidacao.emptyNumber(filtros.getPlanoTipo())){
                sql.append(" and pt.codigo = ").append(filtros.getPlanoTipo()).append(" \n");
            }
            sql.append(" GROUP BY numeromeses,contrato.empresa,nomeempresa order by numeromeses ");
        }

        String sqlConsulta = "";
        if (situacao.equals("NO")) {
            sqlConsulta = String.format(sql.toString(),
                    Uteis.getData(filtros.getData(), "bd"),
                    Uteis.getData(filtros.getData(), "bd"),
                    Uteis.getData(filtros.getData(), "bd"),
                    Uteis.getData(filtros.getData(), "bd"));
        } else {
            sqlConsulta = String.format(sql.toString(),
                    situacao.equals("TR") ? "'TR', 'TV'" : "'" + situacao + "'",
                    Uteis.getData(filtros.getData(), "bd"));
        }

        return sqlConsulta;
    }

    public static String gerarSqlConsultaClientePorDuracao(FiltroRelatorioContratoPorDuracaoJSON filtros, String situacao, Integer duracao) throws Exception {

        if (situacao.equals("CR")//carencia
                || situacao.equals("AT")//atestado
                || situacao.equals("TR")//trancamento
                || situacao.equals("TV"))//trancamento vencido
        {

            StringBuilder sql = new StringBuilder();
            sql.append(" Select ");
            sql.append("cliente.matricula, ");
            sql.append("cliente.situacao, ");
            sql.append("pessoa.nome, ");
            sql.append("plano.descricao,");
            sql.append("contrato.codigo as contratocodigo , ");
            sql.append("cast(contrato.vigenciade as date) as datainicial, ");
            sql.append("cast(contrato.vigenciaateajustada as date) as datafinal, ");
            sql.append("contrato.nomemodalidades as modalidade, ");
            sql.append("pessoa.codigo as codigopessoa, ");
            sql.append("contrato.situacao as contratosituacao, ");
            sql.append("contratoduracao.numeromeses, ");
            sql.append("empresa.codigo as codigoempresa, ");
            sql.append("empresa.nome as nomeempresa ");
            sql.append("from contratooperacao as o  ");
            sql.append("inner join contrato on contrato.codigo = o.contrato ");
            sql.append("inner join empresa on empresa.codigo = contrato.empresa ");
            sql.append("inner join pessoa on pessoa.codigo = contrato.pessoa ");
            sql.append("inner join cliente on cliente.pessoa = pessoa.codigo ");
            sql.append("inner join contratoduracao on contratoduracao.contrato = contrato.codigo and contratoduracao.numeromeses = ").append(duracao).append(" ");
            sql.append("inner join plano on plano.codigo = contrato.plano and plano.descricao like ('").append(filtros.getDescricao()).append("%')  ");
            if(!UteisValidacao.emptyNumber(filtros.getPlanoTipo())){
                sql.append(" inner join planoTipo pt on pt.codigo = plano.planoTipo ");
            }
            sql.append("left join planorecorrencia on planorecorrencia.plano = plano.codigo ");
            sql.append("where (o.tipooperacao = '").append(situacao.toUpperCase()).append("' ");
            if (situacao.equals("TR")) {
                sql.append(" or o.tipooperacao = 'TV' ");
            }
            sql.append(") ");
            if (!UteisValidacao.emptyNumber(filtros.getEmpresa())) {
                sql.append(" and contrato.empresa =  ").append(filtros.getEmpresa());
            }
            if(!UteisValidacao.emptyNumber(filtros.getPlanoTipo())){
                sql.append(" and pt.codigo = ").append(filtros.getPlanoTipo()).append(" \n");
            }
            sql.append(filtros.getSemBolsa() ? " and contrato.bolsa = false " : " ");
            if (!filtros.getContabilizarPlanosAutoRenovaveis()) {
                sql.append(" and ((planorecorrencia.codigo IS NULL AND plano.renovavelautomaticamente IS FALSE) OR (planorecorrencia.codigo IS NOT NULL AND planorecorrencia.renovavelautomaticamente IS FALSE))");
            }
            sql.append(" and  '").append(Uteis.getData(filtros.getData(), "bd")).append("' between o.datainicioefetivacaooperacao and o.datafimefetivacaooperacao ");
            if (!Uteis.nullOrEmpty(filtros.getQuickSearchValue())) {
                sql.append(" and ( ");
                sql.append(" pessoa.nome ilike '%").append(filtros.getQuickSearchValue()).append("%' ");
                if (filtros.getQuickSearchValue().matches("\\d+")) {
                    sql.append(" or cliente.codigomatricula = ").append(filtros.getQuickSearchValue());
                }
                sql.append(") ");
            }
            sql.append("order by pessoa.nome ");
            return sql.toString();

        } else if (situacao.equals("NO")) {//ativos normais

            StringBuilder sql = new StringBuilder();
            sql.append(" Select distinct ");
            sql.append("cliente.matricula, ");
            sql.append("cliente.situacao, ");
            sql.append("pessoa.nome, ");
            sql.append("plano.descricao,");
            sql.append("contrato.codigo as contratocodigo , ");
            sql.append("cast( contrato.vigenciade as date) as datainicial, ");
            sql.append("cast( contrato.vigenciaateajustada as date) as datafinal, ");
            sql.append("contrato.nomemodalidades as modalidade, ");
            sql.append("pessoa.codigo as codigopessoa, ");
            sql.append("contrato.situacao as contratosituacao, ");
            sql.append("contratoduracao.numeromeses, ");
            sql.append("empresa.codigo as codigoempresa, ");
            sql.append("empresa.nome as nomeempresa ");
            sql.append("from historicocontrato  as h  ");
            sql.append("inner join contrato on contrato.codigo = h.contrato ");
            sql.append("inner join empresa on empresa.codigo = contrato.empresa ");
            sql.append("inner join pessoa on pessoa.codigo = contrato.pessoa ");
            sql.append("inner join cliente on cliente.pessoa = pessoa.codigo ");
            sql.append("inner join contratoduracao on contratoduracao.contrato = contrato.codigo  and contratoduracao.numeromeses = ").append(duracao).append(" ");
            sql.append("inner join plano on plano.codigo = contrato.plano and plano.descricao like ('").append(filtros.getDescricao()).append("%')  ");
            if(!UteisValidacao.emptyNumber(filtros.getPlanoTipo())){
                sql.append(" inner join planoTipo pt on pt.codigo = plano.planoTipo ");
            }
            sql.append("left join planorecorrencia on planorecorrencia.plano = plano.codigo ");
            sql.append("where 1 = 1 ");
            if (!UteisValidacao.emptyNumber(filtros.getEmpresa())) {
                sql.append("and contrato.empresa =  ").append(filtros.getEmpresa()).append(" ");
            }
            if(!UteisValidacao.emptyNumber(filtros.getPlanoTipo())){
                sql.append(" and pt.codigo = ").append(filtros.getPlanoTipo()).append(" \n");
            }
            sql.append("and '").append(Uteis.getData(filtros.getData(), "bd")).append("' between contrato.vigenciaDe and contrato.vigenciaAteAjustada ");
            sql.append("and contrato.codigo not in (select h.contrato from historicocontrato h ");
            sql.append("where h.tipohistorico in ('AV','VE','CA','DE') ");
            sql.append("and '").append(Uteis.getData(filtros.getData(), "bd")).append("' between h.datainiciosituacao and h.datafinalsituacao) ");
            sql.append("and contrato.codigo not in (select o.contrato from contratooperacao  o ");
            sql.append("where o.tipooperacao in ('TR', 'CR', 'AT', 'TV') ");
            sql.append("and '").append(Uteis.getData(filtros.getData(), "bd")).append("' >= o.datainicioefetivacaooperacao and '").append(Uteis.getData(filtros.getData(), "bd")).append("' <= o.datafimefetivacaooperacao) ");
            sql.append((filtros.getSemBolsa() ? " and contrato.bolsa = false " : " "));
            if (!filtros.getContabilizarPlanosAutoRenovaveis()) {
                sql.append(" and ((planorecorrencia.codigo IS NULL AND plano.renovavelautomaticamente IS FALSE) OR (planorecorrencia.codigo IS NOT NULL AND planorecorrencia.renovavelautomaticamente IS FALSE))");
            }
            if (!Uteis.nullOrEmpty(filtros.getQuickSearchValue())) {
                sql.append(" and ( ");
                sql.append(" pessoa.nome ilike '%").append(filtros.getQuickSearchValue()).append("%' ");
                if (filtros.getQuickSearchValue().matches("\\d+")) {
                    sql.append(" or cliente.codigomatricula = ").append(filtros.getQuickSearchValue());
                }
                sql.append(") ");
            }
            sql.append("order by pessoa.nome ");
            return sql.toString();

        } else {//vencido, a vencer, Retorno Carência, Retorno Atestado, Retorno Trancamento

            StringBuilder sql = new StringBuilder();
            sql.append(" Select ");
            sql.append("cliente.matricula, ");
            sql.append("cliente.situacao, ");
            sql.append("pessoa.nome, ");
            sql.append("plano.descricao,");
            sql.append("contrato.codigo as contratocodigo , ");
            sql.append("cast( contrato.vigenciade as date) as datainicial, ");
            sql.append("cast( contrato.vigenciaateajustada as date) as datafinal, ");
            sql.append("contrato.nomemodalidades as modalidade, ");
            sql.append("pessoa.codigo as codigopessoa, ");
            sql.append("contrato.situacao as contratosituacao, ");
            sql.append("contratoduracao.numeromeses, ");
            sql.append("empresa.codigo as codigoempresa, ");
            sql.append("empresa.nome as nomeempresa ");
            sql.append("from historicocontrato  as h  ");
            sql.append("inner join contrato on contrato.codigo = h.contrato ");
            sql.append("inner join empresa on empresa.codigo = contrato.empresa ");
            sql.append("inner join pessoa on pessoa.codigo = contrato.pessoa ");
            sql.append("inner join cliente on cliente.pessoa = pessoa.codigo ");
            sql.append("inner join contratoduracao on contratoduracao.contrato = h.contrato  and contratoduracao.numeromeses = ").append(duracao).append(" ");
            sql.append("inner join plano on plano.codigo = contrato.plano and plano.descricao like ('").append(filtros.getDescricao()).append("%')  ");
            if(!UteisValidacao.emptyNumber(filtros.getPlanoTipo())){
                sql.append(" inner join planoTipo pt on pt.codigo = plano.planoTipo ");
            }
            sql.append("left join planorecorrencia on planorecorrencia.plano = plano.codigo ");
            sql.append("where h.tipohistorico = '").append(situacao).append("' ").append(filtros.getSemBolsa() ? " and contrato.bolsa = false " : "");
            if (!UteisValidacao.emptyNumber(filtros.getEmpresa())) {
                sql.append("and contrato.empresa =  ").append(filtros.getEmpresa()).append(" ");
            }
            if(!UteisValidacao.emptyNumber(filtros.getPlanoTipo())){
                sql.append(" and pt.codigo = ").append(filtros.getPlanoTipo()).append(" \n");
            }
            if (!filtros.getContabilizarPlanosAutoRenovaveis()) {
                sql.append(" and ((planorecorrencia.codigo IS NULL AND plano.renovavelautomaticamente IS FALSE) OR (planorecorrencia.codigo IS NOT NULL AND planorecorrencia.renovavelautomaticamente IS FALSE))");
            }
            sql.append("and  '").append(Uteis.getData(filtros.getData(), "bd")).append("' between h.datainiciosituacao and h.datafinalsituacao ");
            sql.append("and contrato.codigo not in (select h.contrato from historicocontrato h ");
            sql.append("where h.tipohistorico in ('CA','DE') and '").append(Uteis.getData(filtros.getData())).append("' between h.datainiciosituacao and h.datafinalsituacao ) ");
            if (!Uteis.nullOrEmpty(filtros.getQuickSearchValue())) {
                sql.append(" and ( ");
                sql.append(" pessoa.nome ilike '%").append(filtros.getQuickSearchValue()).append("%' ");
                if (filtros.getQuickSearchValue().matches("\\d+")) {
                    sql.append(" or cliente.codigomatricula = ").append(filtros.getQuickSearchValue());
                }
                sql.append(") ");
            }
            sql.append("order by pessoa.nome ");
            return sql.toString();
        }
    }

    private List<ClientePorDuracaoDTO> consultarListaClientesPorDuracao(FiltroRelatorioContratoPorDuracaoJSON filtros, Integer duracao, PaginadorDTO paginadorDTO) throws ServiceException {
        List<SituacaoClienteEnum> situacoesConsultar = new ArrayList<>();
        boolean consultarTodas = filtros.getSituacoes().size() == 0;
        if (consultarTodas) {
            String situacoes = "NO,AV,AT,CR,VE,TR";
            for (String situacao : situacoes.split(",")) {
                situacoesConsultar.add(SituacaoClienteEnum.getSituacaoCliente(situacao.trim()));
            }
        } else {
            situacoesConsultar = filtros.getSituacoes();
        }
        List<ClientePorDuracaoDTO> clientesPorDuracao = new ArrayList<>();
        for (SituacaoClienteEnum situacao : situacoesConsultar) {
            try {
                String sql = gerarSqlConsultaClientePorDuracao(filtros, situacao.getCodigo(), duracao);
                contratoDao.createSessionCurrentWork().doWork(connection -> {
                    try {
                        ResultSet resultadoConsulta = contratoDao.createStatement(connection, sql);
                        while (resultadoConsulta.next()) {
                            ClientePorDuracaoDTO clientePorDuracaoDTO = new ClientePorDuracaoDTO();

                            clientePorDuracaoDTO.setMatricula(resultadoConsulta.getInt("matricula"));
                            clientePorDuracaoDTO.setNome(resultadoConsulta.getString("nome"));
                            clientePorDuracaoDTO.setSituacao(situacao.getDescricao());
                            clientePorDuracaoDTO.setDataInicio(Uteis.getData(resultadoConsulta.getDate("dataInicial"), "br"));
                            clientePorDuracaoDTO.setDataTermino(Uteis.getData(resultadoConsulta.getDate("dataFinal"), "br"));
                            clientePorDuracaoDTO.setModalidades(resultadoConsulta.getString("modalidade"));
                            clientePorDuracaoDTO.setPlano(resultadoConsulta.getString("descricao"));

                            ContratoDTO contratoDTO = new ContratoDTO();
                            contratoDTO.setCodigo(resultadoConsulta.getInt("contratocodigo"));
                            contratoDTO.getPlano().setDescricao(resultadoConsulta.getString("descricao"));
                            contratoDTO.setSituacao(resultadoConsulta.getString("contratosituacao"));
                            contratoDTO.getEmpresa().setCodigo(resultadoConsulta.getInt("codigoempresa"));
                            contratoDTO.getEmpresa().setNome(resultadoConsulta.getString("nomeempresa"));
                            contratoDTO.getContratoDuracao().setNumeroMeses(resultadoConsulta.getInt("numeromeses"));

                            ClienteDTO clienteDTO = new ClienteDTO();
                            clienteDTO.setMatricula(resultadoConsulta.getString("matricula"));
                            clienteDTO.getPessoa().setCodigo(resultadoConsulta.getInt("codigopessoa"));
                            clienteDTO.getPessoa().setNome(resultadoConsulta.getString("nome"));

                            clientePorDuracaoDTO.setContrato(contratoDTO);
                            clientePorDuracaoDTO.setCliente(clienteDTO);
                            clientesPorDuracao.add(clientePorDuracaoDTO);
                        }

                    } catch (Exception e) {
                        throw new RuntimeException(e);
                    } finally {
                        connection.close();
                    }
                });
            } catch (Exception e) {
                e.printStackTrace();
                throw new ServiceException(e);
            }
        }
        return clientesPorDuracao;
    }

}
