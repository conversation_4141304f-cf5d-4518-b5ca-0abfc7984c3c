package com.pacto.relatorioms.services.implementations;

import com.pacto.relatorioms.dao.interfaces.AcessoClienteDao;
import com.pacto.relatorioms.dto.IndicadorAcessosDTO;
import com.pacto.relatorioms.dto.RelatorioIndicadorAcessoDTO;
import com.pacto.config.exceptions.ServiceException;
import com.pacto.relatorioms.filter.FiltroIndicadorAcessosJSON;
import com.pacto.config.security.interfaces.RequestService;
import com.pacto.relatorioms.services.interfaces.IndicadorAcessosService;
import com.pacto.config.utils.Uteis;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.sql.ResultSet;
import java.util.*;

@Service
public class IndicadorAcessosServiceImpl implements IndicadorAcessosService {

    @Autowired
    AcessoClienteDao acessoClienteDao;

    @Override
    public RelatorioIndicadorAcessoDTO consultarIndicadorAcessosDia(FiltroIndicadorAcessosJSON filtros) throws ServiceException {
        RelatorioIndicadorAcessoDTO relatorioIndicadorAcessoDTO = montarListaIndicadorAcessos(filtros);
        return relatorioIndicadorAcessoDTO;
    }

    private RelatorioIndicadorAcessoDTO montarListaIndicadorAcessos(FiltroIndicadorAcessosJSON filtros) throws ServiceException {
        List<IndicadorAcessosDTO> listaIndicadorAcessos = new ArrayList<>();
        RelatorioIndicadorAcessoDTO relatorioIndicadorAcessoDTO = new RelatorioIndicadorAcessoDTO();

        if (filtros.getFiltrarPor().equalsIgnoreCase("COMPARATIVO_ANUAL")) {

            Calendar periodoInicialAnoAtual = Calendar.getInstance();
            periodoInicialAnoAtual.setTime(filtros.getPeriodoFinal());
            periodoInicialAnoAtual.set(Calendar.DATE , 1);
            periodoInicialAnoAtual.set(Calendar.MONTH, 0);

            Calendar periodoFinalAnoAnterior = Calendar.getInstance();
            periodoFinalAnoAnterior.setTime(filtros.getPeriodoFinal());
            periodoFinalAnoAnterior.set(Calendar.DATE,31);
            periodoFinalAnoAnterior.set(Calendar.MONTH,11);
            periodoFinalAnoAnterior.add(Calendar.YEAR,-1);

            Calendar periodoIncialAnoAnterior = (Calendar) periodoFinalAnoAnterior.clone();
            periodoIncialAnoAnterior.set(Calendar.DATE , 1);
            periodoIncialAnoAnterior.set(Calendar.MONTH , 0);

            try {
                List<IndicadorAcessosDTO> listaAnualAtual = gerarListaIndicadorAcessoAnual(periodoInicialAnoAtual.getTime(),filtros.getPeriodoFinal(),filtros);
                relatorioIndicadorAcessoDTO.setAnoAtual(listaAnualAtual);

                List<IndicadorAcessosDTO> listaAnualAnterior = gerarListaIndicadorAcessoAnual(periodoIncialAnoAnterior.getTime(),periodoFinalAnoAnterior.getTime(),filtros);
                relatorioIndicadorAcessoDTO.setAnoAnterior(listaAnualAnterior);

            } catch (Exception e) {
                e.printStackTrace();
                throw new ServiceException(e);
            }
        }
        else { // por horário
            String sql = gerarSqlAcessosFrequenciaGeralPorHorario(filtros);
            try {
                acessoClienteDao.createSessionCurrentWork().doWork(connection -> {
                    try (ResultSet resultadoConsulta = acessoClienteDao.createStatement(connection, sql)) {
                        TreeMap<Double, HashMap<String, Integer>> mapaHora = new TreeMap<>();
                        while (resultadoConsulta.next()) {
                            Double hora = 0.0;
                            hora = resultadoConsulta.getDouble("data");
                            IndicadorAcessosDTO obj = new IndicadorAcessosDTO();
                            obj.setQuantidadeAtivos(new Integer(resultadoConsulta.getInt("quantidadeAtivos")));
                            obj.setQuantidade(resultadoConsulta.getInt("quantidade"));
                            obj.setHora(new Double(hora));
                            listaIndicadorAcessos.add(obj);
                        }
                        for (Double key : mapaHora.keySet()) {
                            IndicadorAcessosDTO obj = new IndicadorAcessosDTO();
                            obj.setQuantidade(mapaHora.get(key).size());
                            obj.setHora(key);
                            Integer ativos = 0;
                            for (String k : mapaHora.get(key).keySet()) {
                                ativos += mapaHora.get(key).get(k);
                            }
                            obj.setQuantidadeAtivos(ativos);
                            listaIndicadorAcessos.add(obj);
                        }
                        relatorioIndicadorAcessoDTO.setHorarioDiaAcesso(listaIndicadorAcessos);
                    } catch (Exception e) {
                        throw new RuntimeException(e);
                    } finally {
                        connection.close();
                    }
                });
            } catch (Exception e) {
                throw new ServiceException(e);
            }
        }
        return relatorioIndicadorAcessoDTO;
    }

    private String gerarSqlAcessoAnual(Date periodoInicial, Date periodoFinal, FiltroIndicadorAcessosJSON filtros) throws ServiceException {
        StringBuilder sql = new StringBuilder();

        sql.append("SELECT \n");
        sql.append("CAST(sql.entrada AS DATE) as data, \n");
        sql.append("EXTRACT( month FROM sql.entrada) as month, \n");
        sql.append("COUNT(DISTINCT(sql.codAcesso)) as quantidade \n");
        sql.append(sqlBase());
        sql.append("WHERE sql.entrada >= '").append(Uteis.getData(periodoInicial, "bd")).append(" 00:00:00'  \n");
        sql.append("AND sql.entrada <= '").append(Uteis.getData(periodoFinal, "bd")).append(" 23:59:59' \n");
        sql.append(" AND sql.empresa = ").append(filtros.getEmpresa()).append(" \n");
        if (!filtros.isExibirAcessosBloqueados()) {
            sql.append(" AND sql.situacaoAcesso not like 'RV_BLOQ%' \n");
        }
        sql.append("GROUP BY month,data \n");
        sql.append("ORDER BY month,data \n");
        return sql.toString();
    }

    private String gerarSqlAcessosFrequenciaGeralPorHorario(FiltroIndicadorAcessosJSON filtros) throws ServiceException {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT ");
        sql.append("EXTRACT( hour FROM sql.entrada) as data, \n");
        sql.append("COUNT(DISTINCT(sql.codAcesso)) as quantidade, ");
        sql.append("COUNT(distinct(CASE WHEN sql.situacao = 'AT' THEN sql.codAcesso ELSE null end)) as quantidadeAtivos ");
        sql.append(sqlBase());
        sql.append("WHERE sql.entrada >= '").append(Uteis.getData(filtros.getPeriodoInicial(), "bd")).append(" 00:00:00'  \n");
        sql.append("AND sql.entrada <= '").append(Uteis.getData(filtros.getPeriodoFinal(), "bd")).append(" 23:59:59' \n");
        sql.append("AND sql.empresa = ").append(filtros.getEmpresa()).append(" \n");
        if (!filtros.isExibirAcessosBloqueados()) {
            sql.append(" AND sql.situacaoAcesso not like 'RV_BLOQ%' \n");
        }
        sql.append("GROUP BY data \n");
        sql.append("ORDER BY data \n");
        return sql.toString();
    }

    private String sqlBase() throws ServiceException {
        StringBuilder sql = new StringBuilder();

        sql.append(" FROM ( \n");
        sql.append(" SELECT  \n");
        sql.append(" ai.dthrentrada as entrada, \n");
        sql.append(" ai.codigo as codAcesso,  \n");
        sql.append(" ai.situacao as situacaoAcesso,  \n");
        sql.append(" ci.situacao as situacao, \n");
        sql.append(" emp.codigo as empresa \n");
        sql.append(" FROM acessocliente ai \n");
        sql.append(" INNER JOIN cliente ci ON ai.cliente = ci.codigo  \n");
        sql.append(" INNER JOIN empresa emp on emp.codigo = ci.empresa \n");
        sql.append(" UNION  \n");
        sql.append(" SELECT  \n");
        sql.append(" ac.dthrentrada as entrada, \n");
        sql.append(" ac.codigo as codAcesso,  \n");
        sql.append(" '' as situacaoAcesso,  \n");
        sql.append(" '' as situacao,  \n");
        sql.append(" emp.codigo as empresa \n");
        sql.append(" FROM acessocolaborador ac \n");
        sql.append(" INNER JOIN colaborador co ON ac.colaborador = co.codigo  \n");
        sql.append(" INNER JOIN empresa emp on emp.codigo = co.empresa \n");
        sql.append(") as sql \n");
        return sql.toString();
    }

    private List<IndicadorAcessosDTO> gerarListaIndicadorAcessoAnual(Date periodoInicial, Date periodoFianl, FiltroIndicadorAcessosJSON filtros) throws Exception {
        List<IndicadorAcessosDTO> listaIndicadorAcessos = new ArrayList<>();

        acessoClienteDao.createSessionCurrentWork().doWork(connection -> {
            try (ResultSet resultadoConsulta = acessoClienteDao.createStatement(connection, gerarSqlAcessoAnual(periodoInicial, periodoFianl, filtros))) {
                int[] quantidades = new int[12];
                Calendar dtEntrada = Calendar.getInstance();
                while (resultadoConsulta.next()) {
                    Date dataAtual = resultadoConsulta.getDate("data");
                    int quantidadeAtual = resultadoConsulta.getInt("quantidade");
                    dtEntrada.setTime(dataAtual);
                    int mesAnoAtual = dtEntrada.get(Calendar.MONTH);
                    quantidades[mesAnoAtual] += quantidadeAtual;
                }

                IndicadorAcessosDTO objJaneiro = new IndicadorAcessosDTO();
                objJaneiro.setMes("Janeiro");
                objJaneiro.setQuantidade(quantidades[0]);
                listaIndicadorAcessos.add(objJaneiro);


                IndicadorAcessosDTO objFevereiro = new IndicadorAcessosDTO();
                objFevereiro.setMes("Fevereiro");
                objFevereiro.setQuantidade(quantidades[1]);
                listaIndicadorAcessos.add(objFevereiro);


                IndicadorAcessosDTO objMarco = new IndicadorAcessosDTO();
                objMarco.setMes("Março");
                objMarco.setQuantidade(quantidades[2]);
                listaIndicadorAcessos.add(objMarco);


                IndicadorAcessosDTO objAbril = new IndicadorAcessosDTO();
                objAbril.setMes("Abril");
                objAbril.setQuantidade(quantidades[3]);
                listaIndicadorAcessos.add(objAbril);


                IndicadorAcessosDTO objMaio = new IndicadorAcessosDTO();
                objMaio.setMes("Maio");
                objMaio.setQuantidade(quantidades[4]);
                listaIndicadorAcessos.add(objMaio);

                IndicadorAcessosDTO objJunho = new IndicadorAcessosDTO();
                objJunho.setMes("Junho");
                objJunho.setQuantidade(quantidades[5]);
                listaIndicadorAcessos.add(objJunho);

                IndicadorAcessosDTO objJulho = new IndicadorAcessosDTO();
                objJulho.setMes("Julho");
                objJulho.setQuantidade(quantidades[6]);
                listaIndicadorAcessos.add(objJulho);

                IndicadorAcessosDTO objAgosto = new IndicadorAcessosDTO();
                objAgosto.setMes("Agosto");
                objAgosto.setQuantidade(quantidades[7]);
                listaIndicadorAcessos.add(objAgosto);

                IndicadorAcessosDTO objSetembro = new IndicadorAcessosDTO();
                objSetembro.setMes("Setembro");
                objSetembro.setQuantidade(quantidades[8]);
                listaIndicadorAcessos.add(objSetembro);

                IndicadorAcessosDTO objOutubro = new IndicadorAcessosDTO();
                objOutubro.setMes("Outubro");
                objOutubro.setQuantidade(quantidades[9]);
                listaIndicadorAcessos.add(objOutubro);

                IndicadorAcessosDTO objNovembro = new IndicadorAcessosDTO();
                objNovembro.setMes("Novembro");
                objNovembro.setQuantidade(quantidades[10]);
                listaIndicadorAcessos.add(objNovembro);

                IndicadorAcessosDTO objDezembro = new IndicadorAcessosDTO();
                objDezembro.setMes("Dezembro");
                objDezembro.setQuantidade(quantidades[11]);
                listaIndicadorAcessos.add(objDezembro);

            } catch (Exception e) {
                e.printStackTrace();
                throw new RuntimeException(e);
            } finally {
                connection.close();
            }
        });
        return listaIndicadorAcessos;
    }
}
