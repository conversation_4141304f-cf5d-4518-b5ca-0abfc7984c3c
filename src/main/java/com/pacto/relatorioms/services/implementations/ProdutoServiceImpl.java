package com.pacto.relatorioms.services.implementations;

import com.pacto.relatorioms.adapters.ProdutoAdapter;
import com.pacto.relatorioms.dao.interfaces.ProdutoDao;
import com.pacto.relatorioms.dto.ProdutoDTO;
import com.pacto.config.dto.PaginadorDTO;
import com.pacto.relatorioms.entities.Produto;
import com.pacto.config.exceptions.ServiceException;
import com.pacto.relatorioms.filter.FiltroProdutoJSON;
import com.pacto.config.security.interfaces.RequestService;
import com.pacto.relatorioms.services.interfaces.ProdutoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class ProdutoServiceImpl implements ProdutoService {

    @Autowired
    private ProdutoDao produtoDao;
    @Autowired
    private ProdutoAdapter produtoAdapter;

    private final RequestService requestService;

    public ProdutoServiceImpl(RequestService requestService) {
        this.requestService = requestService;
    }


    @Override
    public List<ProdutoDTO> findAll(FiltroProdutoJSON filtroJSON, PaginadorDTO paginadorDTO) throws ServiceException {
        try {
            List<Produto> produtos = produtoDao.findAll(filtroJSON, paginadorDTO);
            List<ProdutoDTO> produtoDTOS = produtoAdapter.toDtos(produtos);
            return produtoDTOS;
        } catch (Exception e) {
            e.printStackTrace();
            throw new ServiceException(e);
        }
    }

    @Override
    public List<ProdutoDTO> findAllMin(FiltroProdutoJSON filtroProdutoJSON) throws ServiceException {
        try {
            List<Produto> produtos = produtoDao.findAllMin(filtroProdutoJSON);
            List<ProdutoDTO> produtoDTOS = produtoAdapter.toDtos(produtos);
            return produtoDTOS;
        } catch (Exception e) {
            e.printStackTrace();
            throw new ServiceException(e);
        }
    }
}
