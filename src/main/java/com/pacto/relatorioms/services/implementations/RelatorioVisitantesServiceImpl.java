package com.pacto.relatorioms.services.implementations;

import com.pacto.relatorioms.dao.interfaces.ClienteDao;
import com.pacto.relatorioms.dto.RelatorioVisitantesDTO;
import com.pacto.config.dto.PaginadorDTO;
import com.pacto.relatorioms.entities.Cliente;
import com.pacto.relatorioms.entities.Endereco;
import com.pacto.config.exceptions.ServiceException;
import com.pacto.relatorioms.filter.FiltroRelatorioVisitantesJSON;
import com.pacto.relatorioms.services.interfaces.RelatorioVisitantesService;
import com.pacto.config.utils.Uteis;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;


@Service
public class RelatorioVisitantesServiceImpl implements RelatorioVisitantesService {

    @Autowired
    private ClienteDao clienteDao;

    @Override
    public List<RelatorioVisitantesDTO> findAllVisitantes(FiltroRelatorioVisitantesJSON filtroJSON, PaginadorDTO paginadorDTO) throws ServiceException {
        try {
            List<Cliente> clientes = clienteDao.findAllRelatorioVisitante(filtroJSON, paginadorDTO);
            return montarRelatorioVisitantes(clientes);
        } catch (Exception e) {
            e.printStackTrace();
            throw new ServiceException(e);
        }
    }

    private List<RelatorioVisitantesDTO> montarRelatorioVisitantes(List<Cliente> clientes) {
        List<RelatorioVisitantesDTO> relatorioVisitantesDTOS = new ArrayList<>();

        for(Cliente c: clientes){
            RelatorioVisitantesDTO r = new RelatorioVisitantesDTO();
            r.setMatricula(c.getMatricula());
            r.setNome(c.getPessoa().getNome());
            r.setTelefones(c.getPessoa().getTelefonesApresentar());
            r.setEmail(c.getPessoa().getEmailApresentar());
            String dtNasc = Uteis.getData(c.getPessoa().getDataNasc(), "br");
            r.setNascimento(dtNasc);
            r.setCadastro(Uteis.getData(c.getPessoa().getDataCadastro(), "br"));
            r.setSituacao(c.getSituacaoApresentar());
            r.setSexo(c.getPessoa().getSexo());
            r.setEstadoCivil(c.getPessoa().getEstadoCivil());
            if(c.getPessoa().getProfissao() != null) {
                r.setProfissao(c.getPessoa().getProfissao().getDescricao());
            }
            r.setEmpresa(c.getEmpresa().getNome());
            if(c.getPessoa().getEnderecos().size() > 0) {
                int count = 0;
                for(Endereco e: c.getPessoa().getEnderecos()) {
                    if(count == 0) {
                        r.setLogradouro(e.getEndereco());
                        r.setNumero(e.getNumero());
                        r.setComplemento(e.getComplemento());
                        r.setBairro(e.getBairro());
                        r.setCep(e.getCep());
                        if(c.getPessoa().getCidade() != null) {
                            r.setCidade(c.getPessoa().getCidade().getNome());
                        }
                    }
                    count++;
                }
            }
            relatorioVisitantesDTOS.add(r);
        }
        return relatorioVisitantesDTOS;
    }
}
