package com.pacto.relatorioms.services.implementations;

import com.pacto.config.exceptions.ServiceException;
import com.pacto.config.security.interfaces.RequestService;
import com.pacto.relatorioms.services.interfaces.AvaliacaoFisicaService;
import com.pacto.relatorioms.utils.VisualizadorRelatorio;
import net.sf.jasperreports.engine.data.JRBeanCollectionDataSource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import java.io.File;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Service
public class AvaliacaoFisicaServiceImpl implements AvaliacaoFisicaService {

    @Autowired
    private RequestService requestService;

    public AvaliacaoFisicaServiceImpl(RequestService requestService) {
        this.requestService = requestService;
    }

    @Override
    public String exportarAvaliacao(ImpressaoAvaliacaoDTO impressao, HttpServletRequest request) throws ServiceException {
        try {
            String chave = requestService.getUsuarioAtual().getChave();
            tratarValoresAvaliacaoFisica(impressao);
            VisualizadorRelatorio visualizadorRelatorio = new VisualizadorRelatorio();
            Map<String, Object> parametros = impressao.getParametros();
            parametros.put("nomeDesignIReport", getDesign());
            parametros.put("nomeRelatorio", "avaliacaofisicaaluno");
            parametros.put("SUBREPORT_DIR", getDesignIReportAvaliacaoFisicaSubReport());

            parametros.put("parqJR", new JRBeanCollectionDataSource(impressao.getParq()));
            parametros.put("perimetriaJR", new JRBeanCollectionDataSource(impressao.getPerimetria()));
            parametros.put("dobrasJR", new JRBeanCollectionDataSource(impressao.getDobras()));
            parametros.put("anamneseJR", new JRBeanCollectionDataSource(impressao.getAnamnese()));
            parametros.put("showNovosItensFlexibilidade", validarApresentacaoNovosItensFlexibilidade(parametros));
            return visualizadorRelatorio.exportarPDFSemLista(chave, request, parametros);
        } catch (Exception e) {
            e.printStackTrace();
            throw new ServiceException(e);
        }
    }

    private boolean validarApresentacaoNovosItensFlexibilidade(Map<String, Object> parametros) {
        try {
            return parametros.get("mobilidadeOmbroEsquerdo") != null
                    || parametros.get("mobilidadeOmbroDireito") != null
                    || parametros.get("mobilidadeQuadrilEsquerdo") != null
                    || parametros.get("mobilidadeQuadrilDireito") != null
                    || parametros.get("mobilidadeJoelhoEsquerdo") != null
                    || parametros.get("mobilidadeJoelhoDireito") != null
                    || parametros.get("mobilidadeTornozeloEsquerdo") != null
                    || parametros.get("mobilidadeTornozeloDireito") != null
                    || parametros.get("observacao") != null
                    || parametros.get("observacaoOmbro") != null
                    || parametros.get("observacaoQuadril") != null
                    || parametros.get("observacaoJoelho") != null
                    || parametros.get("observacaoTornozelo") != null;
        } catch (Exception e) {
            return false;
        }
    }

    private static void tratarValoresAvaliacaoFisica(ImpressaoAvaliacaoDTO impressao) {
        List<String> atrParametrosDouble = new ArrayList<>();
        atrParametrosDouble.add("cargaAstrand");
        atrParametrosDouble.add("frequenciaAstrand");
        atrParametrosDouble.add("vo2Astrand");
        atrParametrosDouble.add("vo2MaxAstrand");

        for (String atributo : atrParametrosDouble) {
            if (impressao.getParametros().containsKey(atributo)) {
                Object valorCA = impressao.getParametros().get(atributo);
                try {
                    if (valorCA instanceof Double) {
                        continue;
                    }
                    if (valorCA instanceof Integer) {
                        impressao.getParametros().put(atributo, ((Integer) valorCA).doubleValue());
                    } else {
                        impressao.getParametros().put(atributo, 0.0);
                    }
                } catch (Exception ex) {
                    impressao.getParametros().put(atributo, 0.0);
                }
            } else {
                impressao.getParametros().put(atributo, 0.0);
            }
        }
    }

    @Override
    public String exportarParq(ImpressaoAvaliacaoDTO impressao, HttpServletRequest request) throws ServiceException {
        try {
            String chave = requestService.getUsuarioAtual().getChave();
            VisualizadorRelatorio visualizadorRelatorio = new VisualizadorRelatorio();
            Map<String, Object> parametros = impressao.getParametros();

            String leiParqValue = parametros.containsKey("showLeiParq") ? parametros.get("showLeiParq").toString() : "";

            if ("leiParqRJ".equalsIgnoreCase(leiParqValue)) {
                parametros.put("nomeDesignIReport", getDesignParqLeiRJ());
            } else if ("leiParqGO".equalsIgnoreCase(leiParqValue)) {
                parametros.put("nomeDesignIReport", getDesignParqLeiGO());
            } else {
                parametros.put("nomeDesignIReport", getDesignParq());
            }

            parametros.put("nomeRelatorio", "respostasAlunoParq");
            parametros.put("SUBREPORT_DIR", getDesignIReportAvaliacaoFisicaSubReport());
            parametros.put("parqJR", new JRBeanCollectionDataSource(impressao.getParq()));
            return visualizadorRelatorio.exportarPDFSemLista(chave, request, parametros);
        } catch (Exception e) {
            e.printStackTrace();
            throw new ServiceException(e);
        }
    }

    @Override
    public String exportarComparativo(ImpressaoAvaliacaoDTO impressao, HttpServletRequest request) throws ServiceException {
        try {
            String chave = requestService.getUsuarioAtual().getChave();
            VisualizadorRelatorio visualizadorRelatorio = new VisualizadorRelatorio();
            Map<String, Object> parametros = impressao.getParametros();
            parametros.put("nomeDesignIReport", getDesignComparativo());
            parametros.put("nomeRelatorio", "comparacoesavaliacaofisicaaluno");
            parametros.put("SUBREPORT_DIR", getDesignIReportAvaliacaoFisicaSubReport());

            parametros.put("comparativo", new JRBeanCollectionDataSource(impressao.getComparativo()));
            return visualizadorRelatorio.exportarPDFSemLista(chave, request, parametros);
        } catch (Exception e) {
            e.printStackTrace();
            throw new ServiceException(e);
        }
    }

    public String getDesign() {
        return ("relatorio" + File.separator +
                "avaliacao" + File.separator +
                "avaliacao_fisica.jasper");
    }

    public String getDesignParq() {
        return ("relatorio" + File.separator +
                "avaliacao" + File.separator +
                "parq.jasper");
    }

    public String getDesignParqLeiRJ() {
        return ("relatorio" + File.separator +
                "avaliacao" + File.separator +
                "parqLeiRJ.jasper");
    }

    public String getDesignParqLeiGO() {
        return ("relatorio" + File.separator +
                "avaliacao" + File.separator +
                "parqLeiGO.jasper");
    }

    public String getDesignComparativo() {
        return ("relatorio" + File.separator +
                "avaliacao" + File.separator +
                "comparacoes_avaliacoes.jasper");
    }

    public String getDesignIReportAvaliacaoFisicaSubReport() {
        return ("relatorio" + File.separator +
                "avaliacao" + File.separator);
    }

}
