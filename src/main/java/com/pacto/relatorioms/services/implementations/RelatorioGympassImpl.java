package com.pacto.relatorioms.services.implementations;

import com.pacto.relatorioms.dao.interfaces.RelatorioGympassDao;
import com.pacto.relatorioms.dto.RelatorioGympassDTO;
import com.pacto.relatorioms.dto.RelatorioVisitantesDTO;
import com.pacto.config.dto.PaginadorDTO;
import com.pacto.relatorioms.entities.Cliente;
import com.pacto.config.exceptions.ServiceException;
import com.pacto.relatorioms.filter.FiltroRelatorioGympassJSON;
import com.pacto.relatorioms.filter.RelatorioGympassFilter;
import com.pacto.relatorioms.services.interfaces.RelatorioGympassService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;


@Service
public class RelatorioGympassImpl implements RelatorioGympassService {

    @Autowired
    private RelatorioGympassDao relatorioGympassDao;

    @Override
    public List<RelatorioGympassDTO> consultarAlunosGympass(FiltroRelatorioGympassJSON filtroJSON, PaginadorDTO paginadorDTO) throws ServiceException {
        try {
            RelatorioGympassFilter relatorioGympassFilter = new RelatorioGympassFilter();
            List<RelatorioGympassDTO> relatorioGympassDTOS = relatorioGympassDao.consultarAlunosGympass(filtroJSON, relatorioGympassFilter, paginadorDTO);
            return relatorioGympassDTOS;
        } catch (Exception e) {
            e.printStackTrace();
            throw new ServiceException(e);
        }
    }
}
