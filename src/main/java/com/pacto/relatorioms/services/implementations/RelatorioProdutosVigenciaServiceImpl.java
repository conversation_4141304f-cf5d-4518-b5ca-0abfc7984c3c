package com.pacto.relatorioms.services.implementations;

import com.pacto.relatorioms.dao.interfaces.MovProdutoDao;
import com.pacto.relatorioms.dto.RelatorioProdutosVigenciaDTO;
import com.pacto.config.dto.PaginadorDTO;
import com.pacto.relatorioms.filter.FiltroRelatorioProdutosVigenciaFilterJSON;
import com.pacto.config.security.interfaces.RequestService;
import com.pacto.relatorioms.services.interfaces.RelatorioProdutosVigenciaService;
import com.pacto.config.utils.Uteis;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;
import org.springframework.stereotype.Service;

import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public class RelatorioProdutosVigenciaServiceImpl implements RelatorioProdutosVigenciaService {

    private final MessageSource messageSource;
    private final RequestService requestService;

    @Autowired
    MovProdutoDao movProdutoDao;

    private static final int MAXIMO_RESULTADOS = 10;

    public RelatorioProdutosVigenciaServiceImpl(MessageSource messageSource, RequestService requestService) {
        this.messageSource = messageSource;
        this.requestService = requestService;
    }


    @Override
    public List<RelatorioProdutosVigenciaDTO> consultarRelatorio(FiltroRelatorioProdutosVigenciaFilterJSON filtros, PaginadorDTO paginadorDTO) throws Exception {
        List<RelatorioProdutosVigenciaDTO> produtosVigenciaDTOS = getSqlGerado(filtros, paginadorDTO);
        return produtosVigenciaDTOS;
    }

    private List<RelatorioProdutosVigenciaDTO> getSqlGerado(FiltroRelatorioProdutosVigenciaFilterJSON filtros, PaginadorDTO paginadorDTO) throws Exception {
        List<RelatorioProdutosVigenciaDTO> produtosVigencia = new ArrayList<>();

        movProdutoDao.createSessionCurrentWork().doWork(connection -> {

            if (filtros.getClienteSemProduto()) {
                clientesSemProduto(filtros, paginadorDTO, produtosVigencia, connection);
            } else {
                clientesComProduto(filtros, paginadorDTO, produtosVigencia, connection);
            }
        });
        return produtosVigencia;
    }

    private void clientesSemProduto(FiltroRelatorioProdutosVigenciaFilterJSON filtros, PaginadorDTO paginadorDTO, List<RelatorioProdutosVigenciaDTO> produtosVigencia, Connection connection) throws SQLException {
        try (ResultSet resultadoConsulta = movProdutoDao.createStatement(connection, sqlClienteSemProduto(filtros, paginadorDTO, true))) {
            while (resultadoConsulta.next()) {
                paginadorDTO.setQuantidadeTotalElementos(resultadoConsulta.getLong(1));
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException(e);
        }
        try (ResultSet resultadoConsulta = movProdutoDao.createStatement(connection, sqlClienteSemProduto(filtros, paginadorDTO, false))) {
            //TODO PROBLEMA NO  RELATORIO FRONT, ONDE NÃO EXISTE INDEX.
            int index = 0;
            if (paginadorDTO != null) {
                index = paginadorDTO.getPage() == null ? index : paginadorDTO.getPage().intValue();
            }
            while (resultadoConsulta.next()) {
                RelatorioProdutosVigenciaDTO relatorioProdutosVigenciaDTO = new RelatorioProdutosVigenciaDTO();
                relatorioProdutosVigenciaDTO.setMatricula(resultadoConsulta.getString("matricula"));
                relatorioProdutosVigenciaDTO.setNome(resultadoConsulta.getString("nome"));
                relatorioProdutosVigenciaDTO.setTelefone(resultadoConsulta.getString("telefones"));
                relatorioProdutosVigenciaDTO.setEmpresa(resultadoConsulta.getString("nomeEmpresa"));
                relatorioProdutosVigenciaDTO.setIndex(++index);
                produtosVigencia.add(relatorioProdutosVigenciaDTO);
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException(e);
        } finally {
            connection.close();
        }
    }

    private void clientesComProduto(FiltroRelatorioProdutosVigenciaFilterJSON filtros, PaginadorDTO paginadorDTO, List<RelatorioProdutosVigenciaDTO> produtosVigencia, Connection connection) throws SQLException {
        try (ResultSet resultadoConsulta = movProdutoDao.createStatement(connection, sqlClienteProdutos(filtros, paginadorDTO, true))) {
            while (resultadoConsulta.next()) {
                paginadorDTO.setQuantidadeTotalElementos(resultadoConsulta.getLong(1));
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException(e);
        }
        try (ResultSet resultadoConsulta = movProdutoDao.createStatement(connection, sqlClienteProdutos(filtros, paginadorDTO, false))) {
            //TODO PROBLEMA NO  RELATORIO FRONT, ONDE NÃO EXISTE INDEX.
            int index = 0;
            if (paginadorDTO != null) {
                index = paginadorDTO.getPage() == null ? index : paginadorDTO.getPage().intValue();
            }
            while (resultadoConsulta.next()) {
                RelatorioProdutosVigenciaDTO relatorioProdutosVigenciaDTO = new RelatorioProdutosVigenciaDTO();
                relatorioProdutosVigenciaDTO.setMatricula(resultadoConsulta.getString("matricula"));
                relatorioProdutosVigenciaDTO.setNome(resultadoConsulta.getString("nome"));
                relatorioProdutosVigenciaDTO.setTelefone(resultadoConsulta.getString("telefones"));
                relatorioProdutosVigenciaDTO.setDescricao(resultadoConsulta.getString("descricao"));
                relatorioProdutosVigenciaDTO.setResponsavelLançamento(resultadoConsulta.getString("responsavellancamento"));
                relatorioProdutosVigenciaDTO.setValidade(resultadoConsulta.getString("datafinalvigencia"));
                relatorioProdutosVigenciaDTO.setValor(resultadoConsulta.getString("totalfinal"));
                relatorioProdutosVigenciaDTO.setEmpresa(resultadoConsulta.getString("nomeEmpresa"));
                relatorioProdutosVigenciaDTO.setIndex(++index);
                produtosVigencia.add(relatorioProdutosVigenciaDTO);
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException(e);
        } finally {
            connection.close();
        }
    }

    private String sqlClienteSemProduto(FiltroRelatorioProdutosVigenciaFilterJSON filtros, PaginadorDTO paginadorDTO, boolean montarCount) {

        int maxResults = MAXIMO_RESULTADOS;
        int indiceInicial = 0;

        if (paginadorDTO != null) {
            maxResults = paginadorDTO.getSize() == null ? MAXIMO_RESULTADOS : paginadorDTO.getSize().intValue();
            indiceInicial = paginadorDTO.getPage() == null ? indiceInicial : paginadorDTO.getPage().intValue() * maxResults;
        }

        StringBuilder sql = new StringBuilder();
        StringBuilder sqlCount = new StringBuilder("SELECT count(distinct cli.codigo) \n");
        StringBuilder sqlSemCount = new StringBuilder();

        sqlSemCount.append("SELECT\n");
        sqlSemCount.append("  cli.codigo as codigoCliente,\n");
        sqlSemCount.append("  cli.matricula,\n");
        sqlSemCount.append("  pes.nome,\n");
        sqlSemCount.append("  array_to_string(array(SELECT\n");
        sqlSemCount.append("  t.numero\n");
        sqlSemCount.append("        FROM telefone t\n");
        sqlSemCount.append("        WHERE t.pessoa = pes.codigo), ', ', '*') AS telefones,\n");
        sqlSemCount.append("  emp.nome as nomeEmpresa, \n");
        sqlSemCount.append("  count(distinct cli.codigo) as qtd\n");
        sql.append("FROM cliente cli\n");
        filtrarSituacoes(sql, filtros);
        //sql.append("  INNER JOIN situacaoclientesinteticodw scsdw ON scsdw.codigopessoa = cli.pessoa AND vi.tipovinculo = 'CO'\n");
        sql.append("  INNER JOIN pessoa pes ON cli.pessoa = pes.codigo\n");
        sql.append("  left JOIN vinculo vi ON cli.codigo = vi.cliente \n");
        sql.append("  INNER JOIN empresa emp ON emp.codigo = cli.empresa \n");
        sql.append("  WHERE 1 = 1 \n");

        if (Uteis.notNullAndNotEmpty(filtros.getParametro())) {
            sql.append(" AND upper(pes.nome) like CONCAT('%" + filtros.getParametro() + "%')\n");
        }

        if (filtros.getEmpresa() != null) {
            sql.append("  AND cli.empresa = ").append(filtros.getEmpresa()).append("\n");
        }

        if (filtros.getPeriodoCadastroDe() != null) {
            sql.append(" AND pes.datacadastro >= '").append(Uteis.getData(filtros.getPeriodoCadastroDe(), "bd")).append(" 00:00:00'  \n");
        }
        sql.append("        AND NOT EXISTS (\n");
        sql.append("  SELECT\n");
        sql.append("    movproduto.pessoa\n");
        sql.append("  FROM movproduto\n");
        sql.append("    INNER JOIN produto ON movproduto.produto = produto.codigo\n");
        sql.append("            AND movproduto.pessoa IS NOT NULL\n");
        sql.append("            AND movproduto.pessoa = cli.pessoa\n");
        sql.append("            AND produto.tipoproduto IN ('AT','SE')\n");
        sql.append(" and produto.tipovigencia   IN ('ID', 'VV')\n ");

        if (filtros.getPeriodoLançamentoDe() != null && filtros.getPeriodoLançamentoAte() != null) {
            sql.append("      AND datalancamento >= '").append(Uteis.getData(filtros.getPeriodoLançamentoDe(), "bd")).append(" 00:00:00'  \n");
            sql.append("      AND datalancamento <= '").append(Uteis.getData(filtros.getPeriodoLançamentoAte(), "bd")).append(" 23:59:59'  \n");
        }
        sql.append(") \n");
        if (filtros.getConsultor() != null && filtros.getConsultor() > 0) {
            sql.append(" AND vi.colaborador = ").append(filtros.getConsultor()).append("\n");
        }
        sqlCount.append(sql);
        sqlSemCount.append(sql);
        sqlSemCount.append("GROUP BY 1, 2, 3, 4, 5\n");

        if (!montarCount) {
            paginadorDTO.setSize((long) maxResults);
            paginadorDTO.setPage((long) indiceInicial);
            if (paginadorDTO.getSort() != null && !paginadorDTO.getSort().isEmpty()) {
                if (paginadorDTO.getSort().toLowerCase().contains("matricula")) {
                    sqlSemCount.append(" order by matricula " + paginadorDTO.getSort().split(",")[1] + " \n");
                } else if (paginadorDTO.getSort().toLowerCase().contains("nome")) {
                    sqlSemCount.append(" order by pes.nome " + paginadorDTO.getSort().split(",")[1] + " \n");
                } else if (paginadorDTO.getSort().toLowerCase().contains("telefone")) {
                    sqlSemCount.append(" order by telefones " + paginadorDTO.getSort().split(",")[1] + " \n");
                } else if (paginadorDTO.getSort().toLowerCase().contains("empresa")) {
                    sqlSemCount.append(" order by nomeEmpresa " + paginadorDTO.getSort().split(",")[1] + " \n");
                }
            } else {
                sqlSemCount.append("ORDER BY pes.nome \n");
            }
            sqlSemCount.append("limit ").append(maxResults).append("offset ").append(indiceInicial);
            return sqlSemCount.toString();
        }

        return sqlCount.toString();
    }

    private String sqlClienteProdutos(FiltroRelatorioProdutosVigenciaFilterJSON filtros, PaginadorDTO paginadorDTO, boolean montarCount) {

        int maxResults = MAXIMO_RESULTADOS;
        int indiceInicial = 0;

        if (paginadorDTO != null) {
            maxResults = paginadorDTO.getSize() == null ? MAXIMO_RESULTADOS : paginadorDTO.getSize().intValue();
            indiceInicial = paginadorDTO.getPage() == null ? indiceInicial : paginadorDTO.getPage().intValue() * maxResults;
        }

        StringBuilder sql = new StringBuilder();
        StringBuilder sqlCount = new StringBuilder("SELECT COUNT(distinct movAux.codigo) \n");
        StringBuilder sqlSemCount = new StringBuilder();

        sqlSemCount.append("SELECT\n");
        sqlSemCount.append("  cli.codigo as codigoCliente,\n");
        sqlSemCount.append("  cli.matricula,\n");
        sqlSemCount.append("  pes.nome,\n");
        sqlSemCount.append("  array_to_string(array(SELECT\n");
        sqlSemCount.append("  t.numero\n");
        sqlSemCount.append("        FROM telefone t\n");
        sqlSemCount.append("        WHERE t.pessoa = pes.codigo), ', ', '*') AS telefones,\n");
        sqlSemCount.append("  prod.descricao,\n");
        sqlSemCount.append("  to_char(mp.datafinalvigencia,'DD/MM/YYYY') as datafinalvigencia,\n");
        sqlSemCount.append("mp.datafinalvigencia as datavalidade,\n");
        sqlSemCount.append("  mp.totalfinal,\n");
        sqlSemCount.append("  pes1.nome AS responsavellancamento,\n");
        sqlSemCount.append("  usu.nome,\n");
        sqlSemCount.append("  emp.nome as nomeEmpresa,\n");
        sqlSemCount.append("  count(distinct movAux.codigo) as qtd\n");
        sql.append("FROM movproduto mp\n");
        sql.append("  INNER JOIN produto prod ON mp.produto = prod.codigo AND prod.tipovigencia IN ('ID', 'VV')\n");
        sql.append("  INNER JOIN pessoa pes ON mp.pessoa = pes.codigo\n");
        sql.append("  INNER JOIN cliente cli ON pes.codigo = cli.pessoa\n");
        sql.append("  INNER JOIN empresa emp ON emp.codigo = cli.empresa\n");
        sql.append("  INNER JOIN usuario usu on usu.codigo = mp.responsavellancamento \n");
        sql.append("  LEFT JOIN colaborador co ON usu.colaborador = co.codigo\n");
        sql.append("  LEFT JOIN pessoa pes1 ON co.pessoa = pes1.codigo\n");

        filtrarSituacoes(sql, filtros);
        sql.append("  LEFT JOIN vinculo vi ON scsdw.codigocliente = vi.cliente AND vi.tipovinculo = 'CO'\n");
        sql.append("  INNER JOIN(SELECT distinct movproduto.pessoa,movproduto.produto,max(movproduto.codigo) as codigo\n");
        sql.append("    FROM movproduto \n");
        sql.append("        INNER JOIN produto prod ON movproduto.produto = prod.codigo\n");
        if (filtros.getTipoProduto() != null) {
            sql.append(" AND prod.tipoproduto = ('").append(filtros.getTipoProduto()).append("')\n");
        } else {
            sql.append(" AND prod.tipoproduto IN ('AT','SE')\n");
        }
        sql.append("and not exists (select 1 from movproduto mp1 where\tmovproduto.pessoa = mp1.pessoa and movproduto.produto = mp1.produto and movproduto.codigo != mp1.codigo and mp1.datafinalvigencia > movproduto.datafinalvigencia)");
        sql.append("    GROUP BY movproduto.pessoa, movproduto.produto)  as movAux \n");
        sql.append("        ON  movAux.codigo = mp.codigo AND movAux.produto = mp.produto\n");
        sql.append("WHERE 1 = 1 AND mp.pessoa = movAux.pessoa\n");

        if (Uteis.notNullAndNotEmpty(filtros.getParametro())) {
            sql.append(" AND upper(pes.nome) like CONCAT('%" + filtros.getParametro() + "%')\n");
            if (filtros.getParametro().matches("\\d+")) {
                sql.append("OR upper(usu.nome) like CONCAT('%" + filtros.getParametro() + "%')\n");
            }
        }

        if (filtros.getEmpresa() != null) {
            sql.append("  AND cli.empresa = ").append(filtros.getEmpresa()).append("\n");
        }

        if (filtros.getProduto() != null) {
            sql.append("AND mp.produto = ").append(filtros.getProduto()).append("\n");
        }
        if (filtros.getPeriodoCadastroDe() != null) {
            sql.append(" AND pes.datacadastro >= '").append(Uteis.getData(filtros.getPeriodoCadastroDe(), "bd")).append(" 00:00:00'  \n");
        }
        if (filtros.getTipoProduto() != null) {
            sql.append(" AND prod.tipoproduto =   ('").append(filtros.getTipoProduto()).append("')\n");
            sql.append(" and prod.tipovigencia   IN ('ID', 'VV') ");
        }
        if (filtros.getPeriodoLançamentoDe() != null && filtros.getPeriodoLançamentoAte() != null) {
            sql.append("      AND mp.datalancamento >= '").append(Uteis.getData(filtros.getPeriodoLançamentoDe(), "bd")).append(" 00:00:00'  \n");
            sql.append("      AND mp.datalancamento <= '").append(Uteis.getData(filtros.getPeriodoLançamentoAte(), "bd")).append(" 23:59:59'  \n");
        }
        if (filtros.getPeriodoVencimentoDe() != null && filtros.getPeriodoVencimentoAte() != null) {
            sql.append("      AND mp.datafinalvigencia >= '").append(Uteis.getData(filtros.getPeriodoVencimentoDe(), "bd")).append(" 00:00:00'  \n");
            sql.append("      AND mp.datafinalvigencia <= '").append(Uteis.getData(filtros.getPeriodoVencimentoAte(), "bd")).append(" 23:59:59'  \n");
        }
        if (filtros.getConsultor() != null && filtros.getConsultor() > 0) {
            sql.append(" AND vi.colaborador = ").append(filtros.getConsultor()).append("\n");
        }
        if (filtros.getResponsavel() != null && filtros.getResponsavel() > 0) {
            sql.append("      AND mp.responsavellancamento = ").append(filtros.getResponsavel()).append("\n");
        }
        sqlCount.append(sql);
        sqlSemCount.append(sql);
        sqlSemCount.append("GROUP BY 1, 2, 3, 4, 5,6,7,8,9,10,11\n");

        if (!montarCount) {
            paginadorDTO.setSize((long) maxResults);
            paginadorDTO.setPage((long) indiceInicial);
            if (paginadorDTO.getSort() != null && !paginadorDTO.getSort().isEmpty()) {
                if (paginadorDTO.getSort().toLowerCase().contains("matricula")) {
                    sqlSemCount.append(" order by matricula " + paginadorDTO.getSort().split(",")[1] + " \n");
                } else if (paginadorDTO.getSort().toLowerCase().contains("nome")) {
                    sqlSemCount.append(" order by pes.nome " + paginadorDTO.getSort().split(",")[1] + " \n");
                } else if (paginadorDTO.getSort().toLowerCase().contains("telefone")) {
                    sqlSemCount.append(" order by telefones " + paginadorDTO.getSort().split(",")[1] + " \n");
                } else if (paginadorDTO.getSort().toLowerCase().contains("descricao")) {
                    sqlSemCount.append(" order by descricao " + paginadorDTO.getSort().split(",")[1] + " \n");
                } else if (paginadorDTO.getSort().toLowerCase().contains("responsavellancamento")) {
                    sqlSemCount.append(" order by responsavellancamento " + paginadorDTO.getSort().split(",")[1] + " \n");
                } else if (paginadorDTO.getSort().toLowerCase().contains("validade")) {
                    sqlSemCount.append(" order by datavalidade " + paginadorDTO.getSort().split(",")[1] + " \n");
                } else if (paginadorDTO.getSort().toLowerCase().contains("valor")) {
                    sqlSemCount.append(" order by totalfinal " + paginadorDTO.getSort().split(",")[1] + " \n");
                } else if (paginadorDTO.getSort().toLowerCase().contains("empresa")) {
                    sqlSemCount.append(" order by nomeEmpresa " + paginadorDTO.getSort().split(",")[1] + " \n");
                }
            } else {
                sqlSemCount.append("ORDER BY pes.nome \n");
            }
            sqlSemCount.append("limit ").append(maxResults).append("\n offset ").append(indiceInicial);
            System.out.println(sqlSemCount);
            return sqlSemCount.toString();
        }

        return sqlCount.toString();
    }

    private void filtrarSituacoes(StringBuilder sql, FiltroRelatorioProdutosVigenciaFilterJSON filtros) {
        sql.append("  INNER JOIN situacaoclientesinteticodw scsdw ON scsdw.codigopessoa = cli.pessoa \n");

        StringBuilder situacao = new StringBuilder();
        StringBuilder situacaoContrato = new StringBuilder();

        if (filtros.getAtivo() == true) {
            situacao.append("'AT'");
        }
        if (filtros.getInativo() == true) {
            if (situacao.length() > 0){
                situacao.append(",");
            }
            situacao.append("'IN'");
        }
        if (filtros.getVisitante() == true) {
            if (situacao.length() > 0){
                situacao.append(",");
            }
            situacao.append("'VI'");
        }
        //Contrato
        if (filtros.getaVencer() == true) {
            situacaoContrato.append("'AV'");
        }
        if (filtros.getCancelado() == true) {
            if (situacaoContrato.length() > 0){
                situacaoContrato.append(",");
            }
            situacaoContrato.append("'CA'");
        }
        if (filtros.getDesistente() == true) {
            if (situacaoContrato.length() > 0){
                situacaoContrato.append(",");
            }
            situacaoContrato.append("'DE'");
        }

        if (filtros.getTrancado() == true) {
            if (situacaoContrato.length() > 0){
                situacaoContrato.append(",");
            }
            situacaoContrato.append("'TR'");
        }
        if (filtros.getVencido() == true) {
            if (situacaoContrato.length() > 0){
                situacaoContrato.append(",");
            }
            situacaoContrato.append("'VE'");
        }


        if (situacao.length() > 0 || situacaoContrato.length() > 0) {
            sql.append("    AND (");
            if (situacao.length() > 0) {
                sql.append("scsdw.situacao IN (").append(situacao).append(")");
            }
            if (situacao.length() > 0 && situacaoContrato.length() > 0){
                sql.append(" OR ");
            }
            if (situacaoContrato.length() > 0){
                sql.append("scsdw.situacaocontrato IN (").append(situacaoContrato).append(")");
            }
            sql.append(")\n");
        }
    }

}
