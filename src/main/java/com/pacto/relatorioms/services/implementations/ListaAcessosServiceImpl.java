package com.pacto.relatorioms.services.implementations;

import com.pacto.config.utils.Ordenacao;
import com.pacto.config.utils.UteisValidacao;
import com.pacto.relatorioms.adapters.AcessoClienteAdapter;
import com.pacto.relatorioms.adapters.AcessoColaboradorAdapter;
import com.pacto.relatorioms.adapters.EmpresaAdapter;
import com.pacto.relatorioms.dao.interfaces.*;
import com.pacto.relatorioms.dto.AcessoClienteDTO;
import com.pacto.relatorioms.dto.AcessoColaboradorDTO;
import com.pacto.relatorioms.dto.EmpresaDTO;
import com.pacto.relatorioms.dto.PessoaDTO;
import com.pacto.relatorioms.entities.Pessoa;
import com.pacto.relatorioms.entities.Usuario;
import com.pacto.config.exceptions.ServiceException;
import com.pacto.relatorioms.enums.MidiaEntidadeEnum;
import com.pacto.relatorioms.filter.FiltroListaAcessosJSON;
import com.pacto.relatorioms.mscomunication.midiams.MediaMs;
import com.pacto.relatorioms.mscomunication.midiams.dto.MidiaDTO;
import com.pacto.config.security.interfaces.RequestService;
import com.pacto.relatorioms.services.interfaces.ListaAcessosService;
import com.pacto.config.utils.HttpServico;
import com.pacto.relatorioms.utils.VisualizadorRelatorio;
import com.pacto.config.utils.Uteis;
import com.pacto.relatorioms.utils.exportador.Exportador;
import com.pacto.relatorioms.utils.exportador.RelatorioBuilder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.MessageSource;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Service;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.client.ResourceAccessException;
import org.springframework.web.client.RestTemplate;

import javax.imageio.ImageIO;
import javax.imageio.ImageWriteParam;
import javax.imageio.ImageWriter;
import javax.imageio.stream.ImageOutputStream;
import javax.servlet.http.HttpServletRequest;
import java.awt.image.BufferedImage;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.InputStream;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;


@Service
public class ListaAcessosServiceImpl implements ListaAcessosService {

    private final MessageSource messageSource;
    private final MediaMs mediaMs;

    @Autowired
    private AcessoClienteDao acessoClienteDao;
    @Autowired
    private AcessoColaboradorDao acessoColaboradorDao;
    @Autowired
    private final EmpresaDao empresaDao;
    @Autowired
    private RequestService requestService;
    @Autowired
    private AcessoClienteAdapter acessoClienteAdapter;
    @Autowired
    private AcessoColaboradorAdapter acessoColaboradorAdapter;
    @Autowired
    private EmpresaAdapter empresaAdapter;
    @Autowired
    UsuarioDao usuarioDAO;

    private final HttpServico httpServico;

    @Value("${discovery.url}")
    private String urlDiscovery;

    @Value("${url.fotos.nuvem}")
    private String urlFotosNuvem;


    public ListaAcessosServiceImpl(MessageSource messageSource, MediaMs mediaMs, EmpresaDao empresaDao, RequestService requestService, EmpresaAdapter empresaAdapter, HttpServico httpServico) {
        this.messageSource = messageSource;
        this.mediaMs = mediaMs;
        this.empresaDao = empresaDao;
        this.requestService = requestService;
        this.empresaAdapter = empresaAdapter;
        this.httpServico = httpServico;
    }

    @Override
    public String exportarListaAcessos(FiltroListaAcessosJSON filtros, HttpServletRequest request) throws ServiceException {
        try {
            validacoes(filtros);
            String chave = requestService.getUsuarioAtual().getChave();
            EmpresaDTO empresaDTO = empresaAdapter.toDto(empresaDao.findById(requestService.getEmpresaId()));
            String retorno = "";
            if (filtros.getTipoListaAcesso().equals("CL")) {
                List<AcessoClienteDTO> acessoClienteDTOS = acessoClienteAdapter.toDtos(acessoClienteDao.consultarListaAcessosClientes(filtros));
                if (filtros.getApresentarAcessosNessaUnidade() != null && filtros.getApresentarAcessosNessaUnidade()) {
                    Map<String, AcessoClienteDTO> acessoPorClienteEDia = new HashMap<>();
                    List<AcessoClienteDTO> novaLista = new ArrayList<>();
                    acessoClienteDTOS.addAll(acessoClienteAdapter.toDtos(acessoClienteDao.consultarListaAcessosClientesOutrasUnidades(filtros)));
                    acessoClienteDTOS.forEach(e -> {
                        if (!UteisValidacao.emptyString(e.getNomeCodEmpresaAcessou()) &&
                                e.getNomeCodEmpresaAcessou().equalsIgnoreCase(e.getLocalAcesso().getEmpresa().getCodigo() + " - " + e.getLocalAcesso().getEmpresa().getNome())) {

                            if (filtros.getExibirSomentePrimeiroAcessoPorDia()) {
                                String chaveAcesso = (e.getCliente() != null ? e.getCliente().getCodigo() : e.getNomeCpfEmailClienteOrigem()) + "_" + Uteis.getData(e.getDataHoraEntrada(), "br");
                                if (!acessoPorClienteEDia.containsKey(chaveAcesso) ||
                                        e.getDataHoraEntrada().before(acessoPorClienteEDia.get(chaveAcesso).getDataHoraEntrada())) {
                                    acessoPorClienteEDia.put(chaveAcesso, e); // Mantém o menor horário
                                }
                            } else {
                                novaLista.add(e);
                            }
                        }
                    });
                    if (filtros.getExibirSomentePrimeiroAcessoPorDia()) {
                        acessoClienteDTOS = new ArrayList<>(acessoPorClienteEDia.values());
                    } else {
                        acessoClienteDTOS = new ArrayList<>(novaLista);
                    }
                }
                //Ordenar por
                if (filtros.getOrdenadoPor() != null) {
                    if (filtros.getOrdenadoPor().equals("dthrentrada")) {
                        Ordenacao.ordenarLista(acessoClienteDTOS, "dataHoraEntrada");
                    } else {
                        Ordenacao.ordenarLista(acessoClienteDTOS, "nomeClienteOrigem");
                    }
                }
                if (acessoClienteDTOS.size() > 0) {
                    if (filtros.getTipoArquivoExportar().equals("PDF")) {
                        Map<String, Object> paramentros = obterParamentosRelatorio(acessoClienteDTOS, getDesignListaAcessosClientes(), filtros, empresaDTO, chave);
                        VisualizadorRelatorio visualizadorRelatorio = new VisualizadorRelatorio();
                        retorno = visualizadorRelatorio.exportarRelatorio(chave, request, paramentros);
                    } else if (filtros.getTipoArquivoExportar().equals("XLS")) {
                        retorno = imprimirExcelCliente(acessoClienteDTOS, request);
                    }
                } else {
                    throw new ServiceException(messageSource.getMessage("relatorio.lista.acessos.dados.nao.encontrados", null, new Locale(requestService.getLocale())));
                }
            } else if (filtros.getTipoListaAcesso().equals("CO")) {
                List<AcessoColaboradorDTO> acessosColaboradoresDTOS = acessoColaboradorAdapter.toDtos(acessoColaboradorDao.consultarListaAcessosColaboradores(filtros));
                if (acessosColaboradoresDTOS.size() > 0) {
                    if (filtros.getTipoArquivoExportar().equals("PDF")) {
                        Map<String, Object> paramentros = obterParamentosRelatorio(acessosColaboradoresDTOS, getDesignListaAcessosColaboradores(), filtros, empresaDTO, chave);
                        VisualizadorRelatorio visualizadorRelatorio = new VisualizadorRelatorio();
                        retorno = visualizadorRelatorio.exportarRelatorio(chave, request, paramentros);
                    } else if (filtros.getTipoArquivoExportar().equals("XLS")) {
                        retorno = imprimirExcelColaborador(acessosColaboradoresDTOS, request);
                    }
                } else {
                    throw new ServiceException(messageSource.getMessage("relatorio.lista.acessos.dados.nao.encontrados", null, new Locale(requestService.getLocale())));
                }
            }
            return retorno;
        } catch (Exception e) {
            e.printStackTrace();
            throw new ServiceException(e);
        }
    }

    private void validacoes(FiltroListaAcessosJSON filtros) throws ServiceException {
        if (Uteis.nrDiasEntreDatas(filtros.getPeriodoPesquisaInicial(), filtros.getPeriodoPesquisaFinal()) < 0) {
            throw new ServiceException(messageSource.getMessage("relatorio.lista.acessos.data.inicial.maior", null, new Locale(requestService.getLocale())));
        }
        if (Uteis.nrDiasEntreDatas(filtros.getPeriodoPesquisaInicial(), filtros.getPeriodoPesquisaFinal()) > 31
                && filtros.getCliente() == 0 && filtros.getTipoListaAcesso().equals("CL")) {
            throw new ServiceException(messageSource.getMessage("relatorio.lista.acessos.data.limite-um-mes", null, new Locale(requestService.getLocale())));
        } else if ( Uteis.nrDiasEntreDatas(filtros.getPeriodoPesquisaInicial(), filtros.getPeriodoPesquisaFinal()) > 31
                && filtros.getColaborador() == 0 && filtros.getTipoListaAcesso().equals("CO")) {
            throw new ServiceException(messageSource.getMessage("relatorio.lista.acessos.data.limite-um-mes", null, new Locale(requestService.getLocale())));
        } else if (Uteis.nrDiasEntreDatas(filtros.getPeriodoPesquisaInicial(), filtros.getPeriodoPesquisaFinal()) > 365) {
            throw new ServiceException(messageSource.getMessage("relatorio.lista.acessos.data.limite-doze-meses", null, new Locale(requestService.getLocale())));
        }
    }

    private void contarQtdListaAcessos(List<Object> listaObjetos) {
        long count = listaObjetos.stream()
                .filter(obj -> obj instanceof AcessoClienteDTO)
                .count();
        System.out.println("qtd. de acessos no relatorio = " + (int) count);
    }

    private void processarFotos(List<Object> listaObjetos) {

        float QUALIDADE_COMPRESSAO = 0.3f; //VALOR FLOAT ENTRE 0 E 1 (1 = qualidade maxima)

        Map<Integer, InputStream> mapaFotos = new HashMap<>();
        RestTemplate restTemplate = new RestTemplate();

        contarQtdListaAcessos(listaObjetos);

        for (Object obj : listaObjetos) {
            if (obj instanceof AcessoClienteDTO) {
                AcessoClienteDTO acessoCliente = (AcessoClienteDTO) obj;
                if (acessoCliente.getCliente() != null) {
                    PessoaDTO pessoa = acessoCliente.getCliente().getPessoa();

                    if (!UteisValidacao.emptyString(pessoa.getFotokey())) {

                        if (mapaFotos.containsKey(pessoa.getCodigo())) {
                            pessoa.setFotoIS(mapaFotos.get(pessoa.getCodigo()));
                            continue;
                        }

                        String urlFoto = urlFotosNuvem + pessoa.getFotokey();
                        try {
                            byte[] byteFoto = restTemplate.getForObject(urlFoto, byte[].class);

                            if (byteFoto == null || byteFoto.length == 0) {
                                mapaFotos.put(pessoa.getCodigo(), null);
                                continue;
                            }

                            InputStream inputStream = new ByteArrayInputStream(byteFoto);
                            BufferedImage bufferedImage = ImageIO.read(inputStream);
                            InputStream imgComprimidaInputStream = comprimirImagem(bufferedImage, QUALIDADE_COMPRESSAO);

                            mapaFotos.put(pessoa.getCodigo(), imgComprimidaInputStream);
                            pessoa.setFotoIS(imgComprimidaInputStream);

                        } catch (Exception e) {
                            System.out.println("ATENÇÃO - link da imagem quebrado ou corrompido. Cod. Pessoa = " + pessoa.getCodigo() + " (" + pessoa.getNome() + ")");
                            mapaFotos.put(pessoa.getCodigo(), null);
                        }
                    }
                }
            }
        }
    }

    private InputStream comprimirImagem(BufferedImage image, float qualidadeImg) throws Exception {
        ByteArrayOutputStream os = new ByteArrayOutputStream();
        ImageOutputStream ios = ImageIO.createImageOutputStream(os);
        ImageWriter writer = ImageIO.getImageWritersByFormatName("jpg").next();
        writer.setOutput(ios);

        ImageWriteParam param = writer.getDefaultWriteParam();
        param.setCompressionMode(ImageWriteParam.MODE_EXPLICIT);
        param.setCompressionQuality(qualidadeImg);

        writer.write(null, new javax.imageio.IIOImage(image, null, null), param);
        ios.close();
        writer.dispose();

        return new ByteArrayInputStream(os.toByteArray());
    }


    private Map<String, Object> obterParamentosRelatorio(List listaObjetos, String nomeDesignIReport, FiltroListaAcessosJSON filtros, EmpresaDTO empresaDTO, String chave) throws Exception {


        MidiaDTO midiaDTO = new MidiaDTO();
        midiaDTO.setChave(chave);
        midiaDTO.setTipo(MidiaEntidadeEnum.FOTO_EMPRESA.name());
        midiaDTO.setIdentificador(empresaDTO.getCodigo().toString());
        InputStream fs = null;
        try{
            fs = new ByteArrayInputStream(mediaMs.downloadByteArray(midiaDTO));
        } catch (Exception e) {
            e.printStackTrace();
        }

        if (filtros.getExibirMaisDetalhesCliente()) {
            Long tempoInicio = new Date().getTime();
            processarFotos(listaObjetos);
            Long tempoFinal = new Date().getTime();
            System.out.println(" processarFotos levou: " + (tempoFinal - tempoInicio)/1000 + " segs");
        }

        Map<String, Object> params = new HashMap<>();
        params.put("apresentarAcessosNessaUnidade", filtros.getApresentarAcessosNessaUnidade() != null ? filtros.getApresentarAcessosNessaUnidade() : false);
        params.put("exibirMaisDetalhesCliente", filtros.getExibirMaisDetalhesCliente());
        params.put("tipoRelatorio", filtros.getTipoArquivoExportar());
        params.put("logoPadraoRelatorio", fs);
        if (filtros.getExibirMaisDetalhesCliente()) {
            InputStream fotoPadraoClienteIS = new ClassPathResource("relatorio/imgs/fotoPadrao.jpg").getInputStream();
            params.put("imagemPadraoCliente", fotoPadraoClienteIS);
        }
        params.put("considerarCompensacaoOriginal", "");
        params.put("mensagemRel", "");
        params.put("tipoImplementacao", "");
        params.put("dataIni", "");
        params.put("dataFim", "");
        params.put("imagemLogo", "");
        params.put("nomeDesignIReport", nomeDesignIReport);
        params.put("REPORT_VIRTUALIZER", null);
        params.put("tituloRelatorio", "Lista de Acessos");
        Usuario usuarioLogado = usuarioDAO.findById(requestService.getUsuarioAtual().getCodZw());
        params.put("nomeUsuario", usuarioLogado.getNome());
        params.put("listaObjetos", listaObjetos);
        // Filtros
        StringBuilder filtrosStr = new StringBuilder();
        filtrosStr.append("Empresa: ").append(empresaDTO.getNome());
        filtrosStr.append(" Período de: ").append(Uteis.getData(filtros.getPeriodoPesquisaInicial(), "br"));
        filtrosStr.append(" até: ").append(Uteis.getData(filtros.getPeriodoPesquisaFinal(), "br"));
        filtrosStr.append(" Horário Inicial: ").append(filtros.getFaixaHorariaInicial());
        filtrosStr.append(" Horário Final: ").append(filtros.getFaixaHorariaFinal());
        String consultaPor = "";
        if (filtros.getTipoListaAcesso().equals("CL")) {
            params.put("nomeRelatorio", "ListaAcessoRelCliente");
            params.put("caminhoParserXML", "/ListaAcessoRelCliente/registros");
            consultaPor = "Cliente";
        } else if (filtros.getTipoListaAcesso().equals("CO")) {
            params.put("nomeRelatorio", "ListaAcessoRelColaborador");
            params.put("caminhoParserXML", "/ListaAcessoRelColaborador/registros");
            consultaPor = "Colaborador";
        }
        filtrosStr.append(" \nConsulta por: ").append(consultaPor);
        params.put("filtros", filtrosStr.toString());
        // Empresa
        params.put("empresaVO.fone", empresaDTO.getTelComercial1());
        params.put("empresaVO.cnpj", empresaDTO.getCnpj());
        params.put("nomeEmpresa", empresaDTO.getNome());
        params.put("enderecoEmpresa", empresaDTO.getEndereco());
        StringBuilder enderecoEmpresa = new StringBuilder();
        if(empresaDTO.getEndereco() != null) {
            enderecoEmpresa.append(empresaDTO.getEndereco());
        }
        if(empresaDTO.getCidade() != null) {
            enderecoEmpresa.append(" - ").append(empresaDTO.getCidade().getNome());
        }
        if(empresaDTO.getEstado() != null) {
            enderecoEmpresa.append(" - ").append(empresaDTO.getEstado().getNome());
        }
        params.put("empresaVO.endereco", enderecoEmpresa.toString());
        String cidadeEstado = "";
        if(empresaDTO.getCidade() != null && empresaDTO.getEstado() != null) {
            cidadeEstado = empresaDTO.getCidade().getNome() + "/" + empresaDTO.getEstado().getSigla();
        }
        params.put("cidadeEmpresa", cidadeEstado);
        params.put("empresaVO.site", empresaDTO.getSite() == null ? "" : empresaDTO.getSite());
        return params;
    }

    @Override
    public File fileTemp(HttpServletRequest request,
                         String name) {
        try {
            String nomeRelPDF = "temp";
            File pdfFolder = new File(Uteis.obterCaminhoWebAplicacao(request) + File.separator + nomeRelPDF);
            pdfFolder.mkdirs();
            nomeRelPDF = nomeRelPDF + File.separator + name;
            return new File(Uteis.obterCaminhoWebAplicacao(request) + File.separator + nomeRelPDF);
        } catch (Exception e) {
            return null;
        }
    }

    public String getDesignListaAcessosClientes() {
        return ("relatorio" + File.separator +
                "lista_acessos" + File.separator +
                "ListaAcessoRelCliente.jasper");
    }

    public String getDesignListaAcessosColaboradores() {
        return ("relatorio" + File.separator +
                "lista_acessos" + File.separator +
                "ListaAcessoRelColaborador.jasper");
    }


    private String imprimirExcelCliente(List<AcessoClienteDTO> resultado, HttpServletRequest request) throws Exception {
        File arquivo = criarArquivo("ListaAcessoRelCliente", request);
        RelatorioBuilder relatorio = new RelatorioBuilder();
        relatorio.dado(resultado);

        relatorio.addColuna("Mat. cliente", "matriculaClienteOrigem")
                .addColuna("Nome", "nomeClienteOrigem")
                .addColuna("Cpf", "cpfClienteOrigem")
                .addColuna("Data entrada", "dataHoraEntrada")
                .addColuna("Data saída", "dataHoraSaida")
                .addColuna("Tempo", "intervaloDataHoras")
                .addColuna("Sentido", "sentido")
                .addColuna("Meio identificação", "meioIdentificacaoEntrada.descricao")
                .addColuna("Coletor", "coletor.descricao")
                .addColuna("Bloqueio", "situacao.descricao")
                .addColuna("Empresa Origem", "nomeCodEmpresaOrigem")
                .addColuna("Empresa Acesso", "nomeCodEmpresaAcessou")
                .addColuna("Email", "cliente.pessoa.email")
                .addColuna("Usuário lib.", "usuario.primeiroNomeConcatenado")
                .addColuna("Categoria", "cliente.categoria.nome");
        relatorio.titulo("Lista de Acessos");
        Exportador.exportarExcel(relatorio, arquivo);
        return arquivo.getName();
    }

    private String imprimirExcelColaborador(List<AcessoColaboradorDTO> resultado, HttpServletRequest request) throws Exception {
        File arquivo = criarArquivo("ListaAcessoRelColaborador", request);
        RelatorioBuilder relatorio = new RelatorioBuilder();
        relatorio.dado(resultado);
        relatorio.addColuna("Código", "colaborador.codigo")
                .addColuna("Nome", "colaborador.pessoa.nome")
                .addColuna("Data entrada", "dataHoraEntrada")
                .addColuna("Data saída", "dataHoraSaida")
                .addColuna("Tempo", "intervaloDataHoras")
                .addColuna("Sentido", "sentido")
                .addColuna("Meio identificação", "meioIdentificacaoEntrada.descricao")
                .addColuna("Coletor", "coletor.descricao")
                .addColuna("Email", "colaborador.pessoa.email")
                .addColuna("Empresa", "localAcesso.empresa.nome");
        relatorio.titulo("Lista de Acessos");
        Exportador.exportarExcel(relatorio, arquivo);
        return arquivo.getName();
    }

    private File criarArquivo(String s, HttpServletRequest request) throws Exception {

        String nome = s
                + "-" + requestService.getUsuarioAtual().getChave()
                + "-" + String.valueOf(new Date().getTime())
                + ".xlsx";
        File xlsxFolder = new File(Uteis.obterCaminhoWebAplicacao(request) + File.separator + "temp");
        xlsxFolder.mkdirs();
        File arquivo = new File(xlsxFolder.getPath() + File.separator + nome);
        if (arquivo.exists()) {
            arquivo.delete();
        }
        arquivo.createNewFile();
        return arquivo;
    }
}
