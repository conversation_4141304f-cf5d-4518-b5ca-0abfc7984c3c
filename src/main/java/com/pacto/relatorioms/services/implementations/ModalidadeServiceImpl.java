package com.pacto.relatorioms.services.implementations;

import com.pacto.relatorioms.adapters.ModalidadeAdapter;
import com.pacto.relatorioms.dao.interfaces.ModalidadeDao;
import com.pacto.relatorioms.dto.ModalidadeDTO;
import com.pacto.relatorioms.entities.Modalidade;
import com.pacto.config.exceptions.ServiceException;
import com.pacto.relatorioms.filter.FiltroModalidadeJSON;
import com.pacto.config.security.interfaces.RequestService;
import com.pacto.relatorioms.services.interfaces.ModalidadeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;


@Service
public class ModalidadeServiceImpl implements ModalidadeService {

    @Autowired
    private ModalidadeDao modalidadeDao;
    @Autowired
    private ModalidadeAdapter modalidadeAdapter;

    private final RequestService requestService;

    public ModalidadeServiceImpl(RequestService requestService) {
        this.requestService = requestService;
    }

    @Override
    public List<ModalidadeDTO> findAll(FiltroModalidadeJSON filtros) throws ServiceException {
        try {
            List<Modalidade> modalidades = modalidadeDao.findAll(filtros);
            List<ModalidadeDTO> modalidadesDtos = modalidadeAdapter.toDtos(modalidades);
            return modalidadesDtos;
        } catch (Exception e) {
            e.printStackTrace();
            throw new ServiceException(e);
        }
    }

}
