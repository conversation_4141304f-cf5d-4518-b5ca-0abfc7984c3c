package com.pacto.relatorioms.services.implementations;

import com.pacto.relatorioms.adapters.MovProdutoAdapter;
import com.pacto.relatorioms.dao.interfaces.MovProdutoDao;
import com.pacto.relatorioms.dto.MovProdutoDTO;
import com.pacto.config.dto.PaginadorDTO;
import com.pacto.relatorioms.entities.MovProduto;
import com.pacto.config.exceptions.ServiceException;
import com.pacto.relatorioms.services.interfaces.MovProdutoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class MovProdutoServiceImpl implements MovProdutoService {

    @Autowired
    private MovProdutoDao movProdutoDao;
    @Autowired
    private MovProdutoAdapter movProdutoAdapter;

    public List<MovProdutoDTO> findAllByCodPessoa(Integer codPessoa, PaginadorDTO paginadorDTO) throws ServiceException {
        try {
            List<MovProduto> movProdutos = movProdutoDao.findAllByPessoa(codPessoa, paginadorDTO);

            List<MovProdutoDTO> movProdutoDTOS = movProdutoAdapter.toDtos(movProdutos);

            if (paginadorDTO != null
                    && paginadorDTO.getPage() != null
                    && paginadorDTO.getSize() != null) {
                int primeiroPaginacao = (int) (paginadorDTO.getPage() * paginadorDTO.getSize());
                int ultimoRegistro = (int) ((paginadorDTO.getPage() * paginadorDTO.getSize()) + paginadorDTO.getSize());
                if (ultimoRegistro > movProdutoDTOS.size()) {
                    movProdutoDTOS = movProdutoDTOS.subList(primeiroPaginacao, movProdutoDTOS.size());
                } else {
                    movProdutoDTOS = movProdutoDTOS.subList(primeiroPaginacao, ultimoRegistro);
                }
            }

            if (paginadorDTO != null) {
                paginadorDTO.setQuantidadeTotalElementos((long) movProdutos.size());
            }
            return movProdutoDTOS;
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

}
