package com.pacto.relatorioms.services.interfaces;

import com.pacto.relatorioms.dto.ProdutoDTO;
import com.pacto.config.dto.PaginadorDTO;
import com.pacto.config.exceptions.ServiceException;
import com.pacto.relatorioms.filter.FiltroProdutoJSON;

import java.util.List;

public interface ProdutoService {

    List<ProdutoDTO> findAll(FiltroProdutoJSON filtroJSON, PaginadorDTO paginadorDTO) throws ServiceException;

    List<ProdutoDTO> findAllMin(FiltroProdutoJSON filtroProdutoJSON) throws ServiceException;
}
