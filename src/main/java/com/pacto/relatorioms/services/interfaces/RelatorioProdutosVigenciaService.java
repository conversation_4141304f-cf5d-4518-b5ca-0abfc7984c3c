package com.pacto.relatorioms.services.interfaces;

import com.pacto.relatorioms.dto.RelatorioProdutosVigenciaDTO;
import com.pacto.config.dto.PaginadorDTO;
import com.pacto.config.exceptions.ServiceException;
import com.pacto.relatorioms.filter.FiltroRelatorioProdutosVigenciaFilterJSON;

import java.util.List;

public interface RelatorioProdutosVigenciaService {

    List<RelatorioProdutosVigenciaDTO> consultarRelatorio (FiltroRelatorioProdutosVigenciaFilterJSON filtros, PaginadorDTO paginadorDTO) throws Exception;
}
