package com.pacto.relatorioms.services.interfaces;

import com.pacto.relatorioms.dto.RelatorioConvidadosDTO;
import com.pacto.config.dto.PaginadorDTO;
import com.pacto.config.exceptions.ServiceException;
import com.pacto.relatorioms.filter.FiltroRelatorioConvidadosJSON;

import java.util.List;

public interface RelatorioConvidadosService {

    List<RelatorioConvidadosDTO> consultarAlunosConvidados(FiltroRelatorioConvidadosJSON filtroJSON, PaginadorDTO paginadorDTO) throws ServiceException;

}
