package com.pacto.relatorioms.services.interfaces;

import com.pacto.config.dto.PaginadorDTO;
import com.pacto.config.exceptions.ServiceException;
import com.pacto.relatorioms.dto.RelatorioGogoodDTO;
import com.pacto.relatorioms.filter.FiltroRelatorioGogoodJSON;

import java.util.List;

public interface RelatorioGogoodService {

    List<RelatorioGogoodDTO> consultarAlunosGogood(FiltroRelatorioGogoodJSON filtroJSON, PaginadorDTO paginadorDTO) throws ServiceException;

}
