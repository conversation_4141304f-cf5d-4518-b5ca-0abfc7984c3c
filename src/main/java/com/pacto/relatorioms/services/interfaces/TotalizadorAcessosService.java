package com.pacto.relatorioms.services.interfaces;

import com.pacto.relatorioms.dto.RelatorioTotalizadorAcessosDTO;
import com.pacto.config.dto.PaginadorDTO;
import com.pacto.relatorioms.dto.TotalizadorAcessosDTO;
import com.pacto.config.exceptions.ServiceException;
import com.pacto.relatorioms.filter.FiltroTotalizadorAcessosJSON;

import java.util.List;

public interface TotalizadorAcessosService {

    List<TotalizadorAcessosDTO> consultarListaTotalizadorAcessos(FiltroTotalizadorAcessosJSON filtros, PaginadorDTO paginadorDTO) throws ServiceException;

    RelatorioTotalizadorAcessosDTO consultarRelatorioTotalizadorAcessos(FiltroTotalizadorAcessosJSON filtros, PaginadorDTO paginadorDTO) throws ServiceException;

}
