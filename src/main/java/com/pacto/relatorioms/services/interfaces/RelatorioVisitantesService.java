package com.pacto.relatorioms.services.interfaces;

import com.pacto.relatorioms.dto.RelatorioVisitantesDTO;
import com.pacto.config.dto.PaginadorDTO;
import com.pacto.config.exceptions.ServiceException;
import com.pacto.relatorioms.filter.FiltroRelatorioVisitantesJSON;

import java.util.List;

public interface RelatorioVisitantesService {

    List<RelatorioVisitantesDTO> findAllVisitantes(FiltroRelatorioVisitantesJSON filtroJSON, PaginadorDTO paginadorDTO) throws ServiceException;

}
