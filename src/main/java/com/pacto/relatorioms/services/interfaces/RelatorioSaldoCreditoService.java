package com.pacto.relatorioms.services.interfaces;

import com.pacto.relatorioms.dto.RelatorioSaldoCreditoDTO;
import com.pacto.config.dto.PaginadorDTO;
import com.pacto.config.exceptions.ServiceException;
import com.pacto.relatorioms.filter.FiltroRelatorioSaldoCreditoJSON;

import java.util.List;

public interface RelatorioSaldoCreditoService {

    List<RelatorioSaldoCreditoDTO> consultarSaldoCredito(FiltroRelatorioSaldoCreditoJSON filtroJSON, PaginadorDTO paginadorDTO) throws ServiceException;

}
