package com.pacto.relatorioms.services.interfaces;

import com.pacto.relatorioms.dto.ColaboradorDTO;
import com.pacto.config.dto.PaginadorDTO;
import com.pacto.config.exceptions.ServiceException;
import com.pacto.relatorioms.filter.FiltroColaboradorJSON;

import java.util.List;

public interface ColaboradorService {

    List<ColaboradorDTO> findAll(FiltroColaboradorJSON filtroJSON, PaginadorDTO paginadorDTO) throws ServiceException;

    List<ColaboradorDTO> findAll(FiltroColaboradorJSON filtroJSON) throws ServiceException;

    List<ColaboradorDTO> findAllByEmpresaId(Integer empresaId, FiltroColaboradorJSON filtros) throws ServiceException;
}
