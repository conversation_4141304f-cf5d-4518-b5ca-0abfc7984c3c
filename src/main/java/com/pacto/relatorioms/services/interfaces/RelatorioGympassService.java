package com.pacto.relatorioms.services.interfaces;

import com.pacto.relatorioms.dto.RelatorioGympassDTO;
import com.pacto.config.dto.PaginadorDTO;
import com.pacto.config.exceptions.ServiceException;
import com.pacto.relatorioms.filter.FiltroRelatorioGympassJSON;

import java.util.List;

public interface RelatorioGympassService {

    List<RelatorioGympassDTO> consultarAlunosGympass(FiltroRelatorioGympassJSON filtroJSON, PaginadorDTO paginadorDTO) throws ServiceException;

}
