package com.pacto.relatorioms.services.interfaces;

import com.pacto.relatorioms.dto.IndicadorAcessosDTO;
import com.pacto.relatorioms.dto.RelatorioIndicadorAcessoDTO;
import com.pacto.config.dto.PaginadorDTO;
import com.pacto.config.exceptions.ServiceException;
import com.pacto.relatorioms.filter.FiltroIndicadorAcessosJSON;

import java.util.List;

public interface IndicadorAcessosService {

    RelatorioIndicadorAcessoDTO consultarIndicadorAcessosDia(FiltroIndicadorAcessosJSON filtros) throws ServiceException;

}
