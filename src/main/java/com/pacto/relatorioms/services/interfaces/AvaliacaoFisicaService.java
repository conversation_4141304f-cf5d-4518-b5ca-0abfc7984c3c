package com.pacto.relatorioms.services.interfaces;

import com.pacto.config.exceptions.ServiceException;
import com.pacto.relatorioms.services.implementations.ImpressaoAvaliacaoDTO;

import javax.servlet.http.HttpServletRequest;
import java.util.Map;

public interface AvaliacaoFisicaService {

        String exportarAvaliacao(ImpressaoAvaliacaoDTO impressao, HttpServletRequest request) throws ServiceException;

        String exportarParq(ImpressaoAvaliacaoDTO impressao, HttpServletRequest request) throws ServiceException;

        String exportarComparativo(ImpressaoAvaliacaoDTO impressao, HttpServletRequest request) throws ServiceException;
}
