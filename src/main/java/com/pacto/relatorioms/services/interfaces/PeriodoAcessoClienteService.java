package com.pacto.relatorioms.services.interfaces;


import com.pacto.relatorioms.dto.PeriodoAcessoClienteDTO;
import com.pacto.config.dto.PaginadorDTO;

import java.util.List;

public interface PeriodoAcessoClienteService {

    List<PeriodoAcessoClienteDTO> findAllByMatriculaRelGympass(Integer matricula, PaginadorDTO paginadorDTO) throws Exception;

    List<PeriodoAcessoClienteDTO> findAllByMatriculaRelGogood(Integer matricula, PaginadorDTO paginadorDTO) throws Exception;

}
