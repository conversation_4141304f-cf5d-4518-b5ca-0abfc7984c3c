package com.pacto.relatorioms.services.interfaces;

import com.pacto.relatorioms.dto.AluguelArmarioRelDTO;
import com.pacto.config.dto.PaginadorDTO;
import com.pacto.config.exceptions.ServiceException;
import com.pacto.relatorioms.filter.RelatorioArmarioFiltroJSON;

import java.util.List;

public interface RelatorioArmarioService {

    List<AluguelArmarioRelDTO> consultarArmarios(RelatorioArmarioFiltroJSON filtros, PaginadorDTO paginadorDTO) throws ServiceException;

}
