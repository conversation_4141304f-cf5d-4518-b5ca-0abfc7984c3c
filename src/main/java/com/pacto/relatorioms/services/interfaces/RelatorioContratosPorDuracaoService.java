package com.pacto.relatorioms.services.interfaces;

import com.pacto.relatorioms.dto.ClientePorDuracaoDTO;
import com.pacto.relatorioms.dto.RelatorioContratosPorDuracaoDTO;
import com.pacto.config.dto.PaginadorDTO;
import com.pacto.config.exceptions.ServiceException;
import com.pacto.relatorioms.filter.FiltroRelatorioContratoPorDuracaoJSON;

import java.util.List;

public interface RelatorioContratosPorDuracaoService {

    RelatorioContratosPorDuracaoDTO consultarContratosPorDuracao(FiltroRelatorioContratoPorDuracaoJSON filtros, PaginadorDTO paginadorDTO) throws ServiceException;

    List<ClientePorDuracaoDTO> consultarClientesPorDuracao(FiltroRelatorioContratoPorDuracaoJSON filtros, Integer duracao, PaginadorDTO paginadorDTO) throws ServiceException;

}
