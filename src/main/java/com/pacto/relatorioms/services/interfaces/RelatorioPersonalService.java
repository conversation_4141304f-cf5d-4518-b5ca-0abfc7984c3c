package com.pacto.relatorioms.services.interfaces;

import com.pacto.relatorioms.dto.ItemRelatorioPersonalDTO;
import com.pacto.relatorioms.dto.RelatorioPersonalTotaisDTO;
import com.pacto.config.dto.PaginadorDTO;
import com.pacto.config.exceptions.ServiceException;
import com.pacto.relatorioms.filter.FiltroRelatorioPersonalJSON;
import com.pacto.config.utils.EnvelopeRespostaDTO;

import java.util.List;

public interface RelatorioPersonalService {

    List<ItemRelatorioPersonalDTO> consultarRelatorioPersonal(FiltroRelatorioPersonalJSON filtros, PaginadorDTO paginadorDTO) throws ServiceException;

    RelatorioPersonalTotaisDTO consultarTotais(FiltroRelatorioPersonalJSON filtroRelatorioPersonalJSON);

}
