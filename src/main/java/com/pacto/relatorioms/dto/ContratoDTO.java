package com.pacto.relatorioms.dto;

import com.fasterxml.jackson.annotation.JsonInclude;

import java.sql.Timestamp;

@JsonInclude
public class ContratoDTO {

    private Integer codigo;
    private Timestamp vigenciaDe;
    private Timestamp vigenciaAteAjustada;
    private PlanoDTO plano;
    private String situacao;
    private EmpresaDTO empresa;
    private ContratoPorDuracaoDTO contratoDuracao;

    public ContratoDTO() {
        inicializarDados();
    }

    public void inicializarDados() {
        this.plano = new PlanoDTO();
        this.empresa = new EmpresaDTO();
        this.contratoDuracao = new ContratoPorDuracaoDTO();
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Timestamp getVigenciaDe() {
        return vigenciaDe;
    }

    public void setVigenciaDe(Timestamp vigenciaDe) {
        this.vigenciaDe = vigenciaDe;
    }

    public Timestamp getVigenciaAteAjustada() {
        return vigenciaAteAjustada;
    }

    public void setVigenciaAteAjustada(Timestamp vigenciaAteAjustada) {
        this.vigenciaAteAjustada = vigenciaAteAjustada;
    }

    public PlanoDTO getPlano() {
        return plano;
    }

    public void setPlano(PlanoDTO plano) {
        this.plano = plano;
    }

    public String getSituacao() {
        return situacao;
    }

    public void setSituacao(String situacao) {
        this.situacao = situacao;
    }

    public EmpresaDTO getEmpresa() {
        return empresa;
    }

    public void setEmpresa(EmpresaDTO empresa) {
        this.empresa = empresa;
    }

    public ContratoPorDuracaoDTO getContratoDuracao() {
        return contratoDuracao;
    }

    public void setContratoDuracao(ContratoPorDuracaoDTO contratoDuracao) {
        this.contratoDuracao = contratoDuracao;
    }
}
