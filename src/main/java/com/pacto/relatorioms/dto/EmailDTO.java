package com.pacto.relatorioms.dto;

import com.fasterxml.jackson.annotation.JsonInclude;

@JsonInclude
public class EmailDTO {
    private Integer codigo;
    private String email;
    private Integer pessoa;
    protected Boolean emailCorrespondencia;
    private Boolean bloqueadoBounce;
    private Boolean receberEmailNovidades;

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public Integer getPessoa() {
        return pessoa;
    }

    public void setPessoa(Integer pessoa) {
        this.pessoa = pessoa;
    }

    public Boolean getEmailCorrespondencia() {
        return emailCorrespondencia;
    }

    public void setEmailCorrespondencia(Boolean emailCorrespondencia) {
        this.emailCorrespondencia = emailCorrespondencia;
    }

    public Boolean getBloqueadoBounce() {
        return bloqueadoBounce;
    }

    public void setBloqueadoBounce(Boolean bloqueadoBounce) {
        this.bloqueadoBounce = bloqueadoBounce;
    }

    public Boolean getReceberEmailNovidades() {
        return receberEmailNovidades;
    }

    public void setReceberEmailNovidades(Boolean receberEmailNovidades) {
        this.receberEmailNovidades = receberEmailNovidades;
    }
}
