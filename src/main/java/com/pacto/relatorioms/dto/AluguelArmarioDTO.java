package com.pacto.relatorioms.dto;
import javax.persistence.*;
import java.util.Date;

public class AluguelArmarioDTO {

    private Integer codigo;
    private Date dataCadastro;
    private Date dataInicio;
    private Date fimOriginal;
    private Boolean contratoAssinado;
    private Integer relacionamentoRenovacao;
    private ClienteDTO cliente;
    private MovProdutoDTO movproduto;
    private ArmarioDTO armario;
    private VendaAvulsaDTO vendaavulsa;

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Date getDataCadastro() {
        return dataCadastro;
    }

    public void setDataCadastro(Date dataCadastro) {
        this.dataCadastro = dataCadastro;
    }

    public Date getDataInicio() {
        return dataInicio;
    }

    public void setDataInicio(Date dataInicio) {
        this.dataInicio = dataInicio;
    }

    public Date getFimOriginal() {
        return fimOriginal;
    }

    public void setFimOriginal(Date fimOriginal) {
        this.fimOriginal = fimOriginal;
    }

    public Boolean getContratoAssinado() {
        return contratoAssinado;
    }

    public void setContratoAssinado(Boolean contratoAssinado) {
        this.contratoAssinado = contratoAssinado;
    }

    public Integer getRelacionamentoRenovacao() {
        return relacionamentoRenovacao;
    }

    public void setRelacionamentoRenovacao(Integer relacionamentoRenovacao) {
        this.relacionamentoRenovacao = relacionamentoRenovacao;
    }

    public ClienteDTO getCliente() {
        return cliente;
    }

    public void setCliente(ClienteDTO cliente) {
        this.cliente = cliente;
    }

    public MovProdutoDTO getMovproduto() {
        return movproduto;
    }

    public void setMovproduto(MovProdutoDTO movproduto) {
        this.movproduto = movproduto;
    }

    public ArmarioDTO getArmario() {
        return armario;
    }

    public void setArmario(ArmarioDTO armario) {
        this.armario = armario;
    }

    public VendaAvulsaDTO getVendaavulsa() {
        return vendaavulsa;
    }

    public void setVendaavulsa(VendaAvulsaDTO vendaavulsa) {
        this.vendaavulsa = vendaavulsa;
    }
}
