package com.pacto.relatorioms.dto;

import com.fasterxml.jackson.annotation.JsonInclude;

import java.util.Date;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class PeriodoAcessoClienteDTO {
    private Integer codigo;
    private PessoaDTO pessoa;
    private Integer contrato;
    private Integer aulaAvulsaDiaria;
    private Integer contratoBaseadoRenovacao;
    private Date dataInicioAcesso;
    private Date dataFinalAcesso;
    private String tipoAcesso;
    private Integer responsavel;
    private Date dataLancamento;
    private String tokenGymPass;
    private String tipoGymPass;
    private Integer reposicao;
    private Double valorGympass;
    private String descricaoTipoGymPass;
    private String descricaoTipoGogood;

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public PessoaDTO getPessoa() {
        return pessoa;
    }

    public void setPessoa(PessoaDTO pessoa) {
        this.pessoa = pessoa;
    }

    public Integer getContrato() {
        return contrato;
    }

    public void setContrato(Integer contrato) {
        this.contrato = contrato;
    }

    public Integer getAulaAvulsaDiaria() {
        return aulaAvulsaDiaria;
    }

    public void setAulaAvulsaDiaria(Integer aulaAvulsaDiaria) {
        this.aulaAvulsaDiaria = aulaAvulsaDiaria;
    }

    public Integer getContratoBaseadoRenovacao() {
        return contratoBaseadoRenovacao;
    }

    public void setContratoBaseadoRenovacao(Integer contratoBaseadoRenovacao) {
        this.contratoBaseadoRenovacao = contratoBaseadoRenovacao;
    }

    public Date getDataInicioAcesso() {
        return dataInicioAcesso;
    }

    public void setDataInicioAcesso(Date dataInicioAcesso) {
        this.dataInicioAcesso = dataInicioAcesso;
    }

    public Date getDataFinalAcesso() {
        return dataFinalAcesso;
    }

    public void setDataFinalAcesso(Date dataFinalAcesso) {
        this.dataFinalAcesso = dataFinalAcesso;
    }

    public String getTipoAcesso() {
        return tipoAcesso;
    }

    public void setTipoAcesso(String tipoAcesso) {
        this.tipoAcesso = tipoAcesso;
    }

    public Integer getResponsavel() {
        return responsavel;
    }

    public void setResponsavel(Integer responsavel) {
        this.responsavel = responsavel;
    }

    public Date getDataLancamento() {
        return dataLancamento;
    }

    public void setDataLancamento(Date dataLancamento) {
        this.dataLancamento = dataLancamento;
    }

    public String getTokenGymPass() {
        return tokenGymPass;
    }

    public void setTokenGymPass(String tokenGymPass) {
        this.tokenGymPass = tokenGymPass;
    }

    public String getTipoGymPass() {
        return tipoGymPass;
    }

    public void setTipoGymPass(String tipoGymPass) {
        this.tipoGymPass = tipoGymPass;
    }

    public Integer getReposicao() {
        return reposicao;
    }

    public void setReposicao(Integer reposicao) {
        this.reposicao = reposicao;
    }

    public Double getValorGympass() {
        return valorGympass;
    }

    public void setValorGympass(Double valorGympass) {
        this.valorGympass = valorGympass;
    }

    public String getDescricaoTipoGymPass() {
        return descricaoTipoGymPass;
    }

    public void setDescricaoTipoGymPass(String descricaoTipoGymPass) {
        this.descricaoTipoGymPass = descricaoTipoGymPass;
    }

    public String getDescricaoTipoGogood() {
        return descricaoTipoGogood;
    }

    public void setDescricaoTipoGogood(String descricaoTipoGogood) {
        this.descricaoTipoGogood = descricaoTipoGogood;
    }
}
