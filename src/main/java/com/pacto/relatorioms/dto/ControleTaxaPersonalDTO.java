package com.pacto.relatorioms.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import java.util.Date;

@JsonInclude
public class ControleTaxaPersonalDTO {

    private Integer codigo;
    private Integer empresa;
    private Date dataRegistro;
    private Date dataInicioVigenciaPlano;
    private Date dataFimVigenciaPlano;
    private Boolean cancelado;
    private Date dataCancelamento;
    private Integer responsavelCancelamento;
    private ColaboradorDTO personal;

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Integer getEmpresa() {
        return empresa;
    }

    public void setEmpresa(Integer empresa) {
        this.empresa = empresa;
    }

    public Date getDataRegistro() {
        return dataRegistro;
    }

    public void setDataRegistro(Date dataRegistro) {
        this.dataRegistro = dataRegistro;
    }

    public Date getDataInicioVigenciaPlano() {
        return dataInicioVigenciaPlano;
    }

    public void setDataInicioVigenciaPlano(Date dataInicioVigenciaPlano) {
        this.dataInicioVigenciaPlano = dataInicioVigenciaPlano;
    }

    public Date getDataFimVigenciaPlano() {
        return dataFimVigenciaPlano;
    }

    public void setDataFimVigenciaPlano(Date dataFimVigenciaPlano) {
        this.dataFimVigenciaPlano = dataFimVigenciaPlano;
    }

    public Boolean getCancelado() {
        return cancelado;
    }

    public void setCancelado(Boolean cancelado) {
        this.cancelado = cancelado;
    }

    public Date getDataCancelamento() {
        return dataCancelamento;
    }

    public void setDataCancelamento(Date dataCancelamento) {
        this.dataCancelamento = dataCancelamento;
    }

    public Integer getResponsavelCancelamento() {
        return responsavelCancelamento;
    }

    public void setResponsavelCancelamento(Integer responsavelCancelamento) {
        this.responsavelCancelamento = responsavelCancelamento;
    }

    public ColaboradorDTO getPersonal() {
        return personal;
    }

    public void setPersonal(ColaboradorDTO personal) {
        this.personal = personal;
    }
}
