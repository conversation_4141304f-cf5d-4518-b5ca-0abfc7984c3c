package com.pacto.relatorioms.dto;

import com.fasterxml.jackson.annotation.JsonInclude;

@JsonInclude
public class CategoriaDTO {

    private Integer codigo;
    private Integer nrConvitePermitido;
    private String tipoCategoria;
    private String nome;
    private Integer tipoCategoriaClube;
    private Integer tipoBloqueioInadimplencia;
    private String nomeExterno;

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Integer getNrConvitePermitido() {
        return nrConvitePermitido;
    }

    public void setNrConvitePermitido(Integer nrConvitePermitido) {
        this.nrConvitePermitido = nrConvitePermitido;
    }

    public String getTipoCategoria() {
        return tipoCategoria;
    }

    public void setTipoCategoria(String tipoCategoria) {
        this.tipoCategoria = tipoCategoria;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public Integer getTipoCategoriaClube() {
        return tipoCategoriaClube;
    }

    public void setTipoCategoriaClube(Integer tipoCategoriaClube) {
        this.tipoCategoriaClube = tipoCategoriaClube;
    }

    public Integer getTipoBloqueioInadimplencia() {
        return tipoBloqueioInadimplencia;
    }

    public void setTipoBloqueioInadimplencia(Integer tipoBloqueioInadimplencia) {
        this.tipoBloqueioInadimplencia = tipoBloqueioInadimplencia;
    }

    public String getNomeExterno() {
        return nomeExterno;
    }

    public void setNomeExterno(String nomeExterno) {
        this.nomeExterno = nomeExterno;
    }
}
