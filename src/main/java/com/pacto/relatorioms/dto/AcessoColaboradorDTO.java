package com.pacto.relatorioms.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.pacto.relatorioms.enums.MeioIdentificacaoEnum;

import java.util.Date;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class AcessoColaboradorDTO {
    private Integer codigo;
    private ColaboradorDTO colaborador;
    private Date dataHoraEntrada;
    private Date dataHoraSaida;
    private LocalAcessoDTO localAcesso;
    private ColetorDTO coletor;
    private String sentido = "";
    private String nomeDiaSemanaAcesso = "";
    private MeioIdentificacaoEnum meioIdentificacaoEntrada;
    private MeioIdentificacaoEnum meioIdentificacaoSaida ;
    private String intervaloDataHoras="";

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public ColaboradorDTO getColaborador() {
        return colaborador;
    }

    public void setColaborador(ColaboradorDTO colaborador) {
        this.colaborador = colaborador;
    }

    public Date getDataHoraEntrada() {
        return dataHoraEntrada;
    }

    public void setDataHoraEntrada(Date dataHoraEntrada) {
        this.dataHoraEntrada = dataHoraEntrada;
    }

    public Date getDataHoraSaida() {
        return dataHoraSaida;
    }

    public void setDataHoraSaida(Date dataHoraSaida) {
        this.dataHoraSaida = dataHoraSaida;
    }

    public LocalAcessoDTO getLocalAcesso() {
        return localAcesso;
    }

    public void setLocalAcesso(LocalAcessoDTO localAcesso) {
        this.localAcesso = localAcesso;
    }

    public ColetorDTO getColetor() {
        return coletor;
    }

    public void setColetor(ColetorDTO coletor) {
        this.coletor = coletor;
    }

    public String getSentido() {
        return sentido;
    }

    public void setSentido(String sentido) {
        this.sentido = sentido;
    }

    public String getNomeDiaSemanaAcesso() {
        return nomeDiaSemanaAcesso;
    }

    public void setNomeDiaSemanaAcesso(String nomeDiaSemanaAcesso) {
        this.nomeDiaSemanaAcesso = nomeDiaSemanaAcesso;
    }

    public MeioIdentificacaoEnum getMeioIdentificacaoEntrada() {
        return meioIdentificacaoEntrada;
    }

    public void setMeioIdentificacaoEntrada(MeioIdentificacaoEnum meioIdentificacaoEntrada) {
        this.meioIdentificacaoEntrada = meioIdentificacaoEntrada;
    }

    public MeioIdentificacaoEnum getMeioIdentificacaoSaida() {
        return meioIdentificacaoSaida;
    }

    public void setMeioIdentificacaoSaida(MeioIdentificacaoEnum meioIdentificacaoSaida) {
        this.meioIdentificacaoSaida = meioIdentificacaoSaida;
    }

    public String getIntervaloDataHoras() {
        return intervaloDataHoras;
    }

    public void setIntervaloDataHoras(String intervaloDataHoras) {
        this.intervaloDataHoras = intervaloDataHoras;
    }
}
