package com.pacto.relatorioms.dto;

import com.fasterxml.jackson.annotation.JsonInclude;

@JsonInclude
public class ItemTaxaPersonalDTO {

    private Integer codigo;
    private Double descontoEspecifico;
    private ClienteDTO aluno;
    private ProdutoDTO produto;
    private DescontoDTO desconto;
    private MovProdutoDTO movProduto;

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Double getDescontoEspecifico() {
        return descontoEspecifico;
    }

    public void setDescontoEspecifico(Double descontoEspecifico) {
        this.descontoEspecifico = descontoEspecifico;
    }

    public ClienteDTO getAluno() {
        return aluno;
    }

    public void setAluno(ClienteDTO aluno) {
        this.aluno = aluno;
    }

    public ProdutoDTO getProduto() {
        return produto;
    }

    public void setProduto(ProdutoDTO produto) {
        this.produto = produto;
    }

    public DescontoDTO getDesconto() {
        return desconto;
    }

    public void setDesconto(DescontoDTO desconto) {
        this.desconto = desconto;
    }

    public MovProdutoDTO getMovProduto() {
        return movProduto;
    }

    public void setMovProduto(MovProdutoDTO movProduto) {
        this.movProduto = movProduto;
    }
}
