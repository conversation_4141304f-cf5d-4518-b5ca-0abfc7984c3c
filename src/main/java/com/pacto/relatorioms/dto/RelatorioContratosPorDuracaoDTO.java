package com.pacto.relatorioms.dto;

import java.util.ArrayList;
import java.util.List;

public class RelatorioContratosPorDuracaoDTO {

    private Integer totalizador = 0;
    private List<ContratoPorDuracaoDTO> contratosPorDuracao = new ArrayList<>();

    public Integer getTotalizador() {
        return totalizador;
    }

    public void setTotalizador(Integer totalizador) {
        this.totalizador = totalizador;
    }

    public List<ContratoPorDuracaoDTO> getContratosPorDuracao() {
        return contratosPorDuracao;
    }

    public void setContratosPorDuracao(List<ContratoPorDuracaoDTO> contratosPorDuracao) {
        this.contratosPorDuracao = contratosPorDuracao;
    }
}
