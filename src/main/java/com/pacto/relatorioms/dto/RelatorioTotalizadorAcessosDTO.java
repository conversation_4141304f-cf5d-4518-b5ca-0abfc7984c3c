package com.pacto.relatorioms.dto;

import com.fasterxml.jackson.annotation.JsonInclude;

import java.util.List;

@JsonInclude
public class RelatorioTotalizadorAcessosDTO {

    private Integer totalAcessos;
    private List<TotalizadorAcessosDTO> totalizadorAcessos;

    public Integer getTotalAcessos() {
        return totalAcessos;
    }

    public void setTotalAcessos(Integer totalAcessos) {
        this.totalAcessos = totalAcessos;
    }

    public List<TotalizadorAcessosDTO> getTotalizadorAcessos() {
        return totalizadorAcessos;
    }

    public void setTotalizadorAcessos(List<TotalizadorAcessosDTO> totalizadorAcessos) {
        this.totalizadorAcessos = totalizadorAcessos;
    }
}
