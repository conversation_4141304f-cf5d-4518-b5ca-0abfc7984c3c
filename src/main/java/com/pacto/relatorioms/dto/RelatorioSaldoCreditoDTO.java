package com.pacto.relatorioms.dto;

import com.fasterxml.jackson.annotation.JsonInclude;


@JsonInclude(JsonInclude.Include.NON_NULL)
public class RelatorioSaldoCreditoDTO {
    private String codigoCliente;
    private String matricula;
    private String nome;
    private String saldoCreditos;
    private String dataVigenciaAte;
    private String situacao;
    private String telefonesCliente;
    private String empresa;
    private String codigoEmpresa;


    public RelatorioSaldoCreditoDTO(){
    }

    public RelatorioSaldoCreditoDTO(String codigoCliente, String matricula, String nome, String saldoCreditos, String dataVigenciaAte, String situacao, String telefone, String nomeEmpresa, String codigoEmpresa) {
        this.codigoCliente = codigoCliente;
        this.matricula = matricula;
        this.nome = nome;
        this.saldoCreditos = saldoCreditos;
        this.dataVigenciaAte = dataVigenciaAte;
        this.situacao = situacao;
        this.telefonesCliente = telefone;
        this.empresa = empresa;
        this.codigoEmpresa = codigoEmpresa;
    }

    public String getCodigoEmpresa() {
        return codigoEmpresa;
    }

    public void setCodigoEmpresa(String codigoEmpresa) {
        this.codigoEmpresa = codigoEmpresa;
    }

    public String getCodigoCliente() {
        return codigoCliente;
    }

    public void setCodigoCliente(String codigoCliente) {
        this.codigoCliente = codigoCliente;
    }

    public String getMatricula() {
        return matricula;
    }

    public void setMatricula(String matricula) {
        this.matricula = matricula;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public String getSaldoCreditos() {
        return saldoCreditos;
    }

    public void setSaldoCreditos(String saldoCreditos) {
        this.saldoCreditos = saldoCreditos;
    }

    public String getDataVigenciaAte() {
        return dataVigenciaAte;
    }

    public void setDataVigenciaAte(String dataVigenciaAte) {
        this.dataVigenciaAte = dataVigenciaAte;
    }

    public String getSituacao() {
        return situacao;
    }

    public void setSituacao(String situacao) {
        this.situacao = situacao;
    }

    public String getTelefone() {
        return telefonesCliente;
    }

    public void setTelefone(String telefone) {
        this.telefonesCliente = telefone;
    }

    public String getEmpresa() {
        return empresa;
    }

    public void setEmpresa(String empresa) {
        this.empresa = empresa;
    }

}
