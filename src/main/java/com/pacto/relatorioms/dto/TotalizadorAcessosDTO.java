package com.pacto.relatorioms.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.pacto.config.utils.Uteis;

@JsonInclude
public class TotalizadorAcessosDTO {

    private String data;
    private String dataInicial;
    private String dataFinal;
    private Integer quantidade;
    private Integer quantidadeAtivos;
    private String porcentagem;
    private String porcentagemAtivos;
    private String porcentagemMaiorDia;
    private String matricula;
    private String nome;
    private String nomeEmpresa;
    private String plano;
    private String modalidade;
    private String diaDaSemana;
    private Double hora;
    private Integer totalAcessos;

    public TotalizadorAcessosDTO() {
        inicializarDados();
    }

    public void inicializarDados() {
        setQuantidade(0);
        setQuantidadeAtivos(0);
        setDiaDaSemana("");
        setHora((double) 0);
        setNomeEmpresa("");
    }


    public String getData() {
        return data;
    }

    public void setData(String data) {
        this.data = data;
    }

    public String getDataInicial() {
        return dataInicial;
    }

    public void setDataInicial(String dataInicial) {
        this.dataInicial = dataInicial;
    }

    public String getDataFinal() {
        return dataFinal;
    }

    public void setDataFinal(String dataFinal) {
        this.dataFinal = dataFinal;
    }

    public Integer getQuantidade() {
        return quantidade;
    }

    public void setQuantidade(Integer quantidade) {
        this.quantidade = quantidade;
    }

    public Integer getQuantidadeAtivos() {
        return quantidadeAtivos;
    }

    public void setQuantidadeAtivos(Integer quantidadeAtivos) {
        this.quantidadeAtivos = quantidadeAtivos;
    }

    public String getPorcentagem() {
        return porcentagem;
    }

    public void setPorcentagem(String porcentagem) {
        this.porcentagem = porcentagem;
    }

    public String getPorcentagemAtivos() {
        return porcentagemAtivos;
    }

    public void setPorcentagemAtivos(String porcentagemAtivos) {
        this.porcentagemAtivos = porcentagemAtivos;
    }

    public String getMatricula() {
        return matricula;
    }

    public void setMatricula(String matricula) {
        this.matricula = matricula;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public String getNomeEmpresa() {
        return nomeEmpresa;
    }

    public void setNomeEmpresa(String nomeEmpresa) {
        this.nomeEmpresa = nomeEmpresa;
    }

    public String getPlano() {
        return plano;
    }

    public void setPlano(String plano) {
        this.plano = plano;
    }

    public String getModalidade() {
        return modalidade;
    }

    public void setModalidade(String modalidade) {
        this.modalidade = modalidade;
    }

    public String getPorcentagemMaiorDia() {
        return porcentagemMaiorDia;
    }

    public void setPorcentagemMaiorDia(String porcentagemMaiorDia) {
        this.porcentagemMaiorDia = porcentagemMaiorDia;
    }

    public String getDiaDaSemana() {
        return diaDaSemana;
    }

    public void setDiaDaSemana(String diaDaSemana) {
        this.diaDaSemana = diaDaSemana;
    }

    public Double getHora() {
        return hora;
    }

    public void setHora(Double hora) {
        this.hora = hora;
    }

    public Integer getTotalAcessos() {
        return totalAcessos;
    }

    public void setTotalAcessos(Integer totalAcessos) {
        this.totalAcessos = totalAcessos;
    }

    public Double getPorcetagemDouble() {
        if(Uteis.notNullAndNotEmpty(this.porcentagem)) {
            String porcentagem = this.porcentagem
                    .replace("%", "")
                    .replace(".", "")
                    .replace(",", "");
            return Double.parseDouble(porcentagem);
        }
        return 0.0;
    }

    public Double getPorcetagemAtivosDouble() {
        if(Uteis.notNullAndNotEmpty(this.porcentagemAtivos)) {
            String porcentagemAtivos = this.porcentagemAtivos
                    .replace("%", "")
                    .replace(".", "")
                    .replace(",", "");
            return Double.parseDouble(porcentagemAtivos);
        }
        return 0.0;
    }

    public Double getPorcetagemMaiorDiaDouble() {
        if(Uteis.notNullAndNotEmpty(this.porcentagemMaiorDia)) {
            String porcentagemMaiorDia = this.porcentagemMaiorDia
                    .replace("%", "")
                    .replace(".", "")
                    .replace(",", "");
            return Double.parseDouble(porcentagemMaiorDia);
        }
        return 0.0;
    }
}
