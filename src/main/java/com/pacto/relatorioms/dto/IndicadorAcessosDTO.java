package com.pacto.relatorioms.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;

@JsonInclude
public class IndicadorAcessosDTO {

    @JsonIgnore
    private String dataBusca;
    @JsonIgnore
    private String dataBuscaFinal;
    private Integer quantidade;

    private String mes;

    private Integer quantidadeAtivos;

    private Double hora;

    public IndicadorAcessosDTO(){
        inicializarDados();
    }

    public void inicializarDados() {
        setQuantidade(0);
    }

    public String getDataBusca() {
        return dataBusca;
    }

    public void setDataBusca(String dataBusca) {
        this.dataBusca = dataBusca;
    }

    public Integer getQuantidade() {
        return quantidade;
    }

    public void setQuantidade(Integer quantidade) {
        this.quantidade = quantidade;
    }

    public String getDataBuscaFinal() {
        return dataBuscaFinal;
    }

    public void setDataBuscaFinal(String dataBuscaFinal) {
        this.dataBuscaFinal = dataBuscaFinal;
    }

    public String getMes() {
        return mes;
    }

    public void setMes(String mes) {
        this.mes = mes;
    }

    public Integer getQuantidadeAtivos() {
        return quantidadeAtivos;
    }

    public void setQuantidadeAtivos(Integer quantidadeAtivos) {
        this.quantidadeAtivos = quantidadeAtivos;
    }

    public Double getHora() {
        return hora;
    }

    public void setHora(Double hora) {
        this.hora = hora;
    }
}
