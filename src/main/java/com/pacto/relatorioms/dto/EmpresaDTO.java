package com.pacto.relatorioms.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;

@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class EmpresaDTO {
    private Integer codigo;
    private String nome;
    private boolean ativa;
    private String moeda;
    private String fotoKey;
    private String cnpj;
    private String telComercial1;
    private String telComercial2;
    private String telComercial3;
    private String endereco;
    private String numero;
    private String complemento;
    private String setor;
    private String cep;
    private String site;
    private CidadeDTO cidade;
    private EstadoDTO estado;

    public EmpresaDTO() {
    }

    public EmpresaDTO(Integer codigo, String nome, boolean ativa) {
        this.codigo = codigo;
        this.nome = nome;
        this.ativa = ativa;

    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public boolean isAtiva() {
        return ativa;
    }

    public void setAtiva(boolean ativa) {
        this.ativa = ativa;
    }

    public String getMoeda() {
        return moeda;
    }

    public void setMoeda(String moeda) {
        this.moeda = moeda;
    }

    public String getFotoKey() {
        return fotoKey;
    }

    public void setFotoKey(String fotoKey) {
        this.fotoKey = fotoKey;
    }

    public String getCnpj() {
        return cnpj;
    }

    public void setCnpj(String cnpj) {
        this.cnpj = cnpj;
    }

    public String getTelComercial1() {
        return telComercial1;
    }

    public void setTelComercial1(String telComercial1) {
        this.telComercial1 = telComercial1;
    }

    public String getTelComercial2() {
        return telComercial2;
    }

    public void setTelComercial2(String telComercial2) {
        this.telComercial2 = telComercial2;
    }

    public String getTelComercial3() {
        return telComercial3;
    }

    public void setTelComercial3(String telComercial3) {
        this.telComercial3 = telComercial3;
    }

    public String getEndereco() {
        return endereco;
    }

    public void setEndereco(String endereco) {
        this.endereco = endereco;
    }

    public String getNumero() {
        return numero;
    }

    public void setNumero(String numero) {
        this.numero = numero;
    }

    public String getComplemento() {
        return complemento;
    }

    public void setComplemento(String complemento) {
        this.complemento = complemento;
    }

    public String getSetor() {
        return setor;
    }

    public void setSetor(String setor) {
        this.setor = setor;
    }

    public String getCep() {
        return cep;
    }

    public void setCep(String cep) {
        this.cep = cep;
    }

    public String getSite() {
        return site;
    }

    public void setSite(String site) {
        this.site = site;
    }

    public CidadeDTO getCidade() {
        return cidade;
    }

    public void setCidade(CidadeDTO cidade) {
        this.cidade = cidade;
    }

    public EstadoDTO getEstado() {
        return estado;
    }

    public void setEstado(EstadoDTO estado) {
        this.estado = estado;
    }
}
