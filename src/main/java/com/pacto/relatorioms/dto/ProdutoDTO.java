package com.pacto.relatorioms.dto;

import com.fasterxml.jackson.annotation.JsonInclude;

@JsonInclude
public class ProdutoDTO {

    private Integer codigo;
    private String descricao;
    private Double valorFinal;
    private Integer nrdiasVigencia;

    private String tipoProduto;

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public Double getValorFinal() {
        return valorFinal;
    }

    public void setValorFinal(Double valorFinal) {
        this.valorFinal = valorFinal;
    }

    public Integer getNrdiasVigencia() {
        return nrdiasVigencia;
    }

    public void setNrdiasVigencia(Integer nrdiasVigencia) {
        this.nrdiasVigencia = nrdiasVigencia;
    }

    public String getTipoProduto() {
        return tipoProduto;
    }

    public void setTipoProduto(String tipoProduto) {
        this.tipoProduto = tipoProduto;
    }
}
