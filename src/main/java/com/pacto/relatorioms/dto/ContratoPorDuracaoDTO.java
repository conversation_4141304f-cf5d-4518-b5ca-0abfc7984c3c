package com.pacto.relatorioms.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.pacto.config.utils.Uteis;

import java.util.List;

@JsonInclude
public class ContratoPorDuracaoDTO {

    protected Integer quantidade;
    protected Integer numeroMeses;
    protected String descricao;
    protected String situacao;
    private String percentual;
    private Double percentualDouble;
    private String nomeEmpresa;
    private EmpresaDTO empresa;
    private List<Integer> contratos;

    public Integer getQuantidade() {
        return quantidade;
    }

    public void setQuantidade(Integer quantidade) {
        this.quantidade = quantidade;
    }

    public Integer getNumeroMeses() {
        return numeroMeses;
    }

    public void setNumeroMeses(Integer numeroMeses) {
        this.numeroMeses = numeroMeses;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public String getSituacao() {
        return situacao;
    }

    public void setSituacao(String situacao) {
        this.situacao = situacao;
    }

    public String getPercentual() {
        return percentual;
    }

    public void setPercentual(String percentual) {
        this.percentual = percentual;
    }

    public String getNomeEmpresa() {
        return nomeEmpresa;
    }

    public void setNomeEmpresa(String nomeEmpresa) {
        this.nomeEmpresa = nomeEmpresa;
    }

    public EmpresaDTO getEmpresa() {
        return empresa;
    }

    public void setEmpresa(EmpresaDTO empresa) {
        this.empresa = empresa;
    }

    public List<Integer> getContratos() {
        return contratos;
    }

    public void setContratos(List<Integer> contratos) {
        this.contratos = contratos;
    }

    public Double getPercentualDouble() {
        return percentualDouble;
    }

    public void setPercentualDouble(Double percentualDouble) {
        this.percentualDouble = percentualDouble;
    }


}
