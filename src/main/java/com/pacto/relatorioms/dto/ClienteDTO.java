package com.pacto.relatorioms.dto;

import com.fasterxml.jackson.annotation.JsonInclude;

@JsonInclude
public class ClienteDTO {

    private Integer codigo;
    private Integer codigoMatricula;
    private String situacao;
    private PessoaDTO pessoa;
    private String matricula;
    private EmpresaDTO empresa;
    private CategoriaDTO categoria;

    public ClienteDTO() {
        inicializarDados();
    }

    public void inicializarDados() {
        this.pessoa = new PessoaDTO();
        this.empresa = new EmpresaDTO();
        this.categoria = new CategoriaDTO();
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Integer getCodigoMatricula() {
        return codigoMatricula;
    }

    public void setCodigoMatricula(Integer codigoMatricula) {
        this.codigoMatricula = codigoMatricula;
    }

    public String getSituacao() {
        return situacao;
    }

    public void setSituacao(String situacao) {
        this.situacao = situacao;
    }

    public PessoaDTO getPessoa() {
        return pessoa;
    }

    public void setPessoa(PessoaDTO pessoa) {
        this.pessoa = pessoa;
    }

    public String getMatricula() {
        return matricula;
    }

    public void setMatricula(String matricula) {
        this.matricula = matricula;
    }

    public EmpresaDTO getEmpresa() {
        return empresa;
    }

    public void setEmpresa(EmpresaDTO empresa) {
        this.empresa = empresa;
    }

    public CategoriaDTO getCategoria() {
        return categoria;
    }

    public void setCategoria(CategoriaDTO categoria) {
        this.categoria = categoria;
    }
}
