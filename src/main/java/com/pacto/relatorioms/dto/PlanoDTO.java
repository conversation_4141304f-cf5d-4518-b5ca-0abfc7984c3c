package com.pacto.relatorioms.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.pacto.relatorioms.entities.empresa.Empresa;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class PlanoDTO {
    private Integer codigo;
    private String descricao;
    private Integer empresa;

    public PlanoDTO() {
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public Integer getEmpresa() {
        return empresa;
    }

    public void setEmpresa(Integer empresa) {
        this.empresa = empresa;
    }
}
