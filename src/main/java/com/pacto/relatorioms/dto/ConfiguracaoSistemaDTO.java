package com.pacto.relatorioms.dto;

import com.fasterxml.jackson.annotation.JsonInclude;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class ConfiguracaoSistemaDTO {

    private Integer codigo;
    private Boolean habilitarGestaoArmarios;


    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Boolean getHabilitarGestaoArmarios() {
        return habilitarGestaoArmarios;
    }

    public void setHabilitarGestaoArmarios(Boolean habilitarGestaoArmarios) {
        this.habilitarGestaoArmarios = habilitarGestaoArmarios;
    }
}
