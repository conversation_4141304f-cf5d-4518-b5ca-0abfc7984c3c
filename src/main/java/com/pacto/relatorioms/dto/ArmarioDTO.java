package com.pacto.relatorioms.dto;

import com.fasterxml.jackson.annotation.JsonInclude;

@JsonInclude
public class ArmarioDTO {
    private Integer codigo;
    private String descricao;
    private String grupo;
    private TamanhoArmarioDTO tamanhoArmario;

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public String getGrupo() {
        return grupo;
    }

    public void setGrupo(String grupo) {
        this.grupo = grupo;
    }

    public TamanhoArmarioDTO getTamanhoArmario() {
        return tamanhoArmario;
    }

    public void setTamanhoArmario(TamanhoArmarioDTO tamanhoArmario) {
        this.tamanhoArmario = tamanhoArmario;
    }
}
