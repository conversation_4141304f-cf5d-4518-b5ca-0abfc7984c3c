package com.pacto.relatorioms.dto.base;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import org.json.JSONObject;

@JsonIgnoreProperties(ignoreUnknown = true)
public class EmpresaFinanceiroDTO {
    private Integer empresazw;
    private Integer codigoFinanceiro;
    private String nome;
    private String cidade;
    private String estado;
    private String pais;
    private Integer redeEmpresaId;
    private String chaveRede;

    public EmpresaFinanceiroDTO() {

    }

    public EmpresaFinanceiroDTO(JSONObject json) {
        this.empresazw = json.optInt("empresazw");
        this.codigoFinanceiro = json.optInt("codigoFinanceiro");
        this.nome = json.optString("nomefantasia");
        this.cidade = json.optString("cidade");
        this.estado = json.optString("estado");
        this.pais = json.optString("pais");
        this.redeEmpresaId = json.optInt("redeEmpresaId");
        this.chaveRede = json.optString("chaveRede");
    }

    public Integer getEmpresazw() {
        return empresazw;
    }

    public void setEmpresazw(Integer empresazw) {
        this.empresazw = empresazw;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public Integer getCodigoFinanceiro() {
        return codigoFinanceiro;
    }

    public void setCodigoFinanceiro(Integer codigoFinanceiro) {
        this.codigoFinanceiro = codigoFinanceiro;
    }

    public String getCidade() {
        return cidade;
    }

    public void setCidade(String cidade) {
        this.cidade = cidade;
    }

    public String getEstado() {
        return estado;
    }

    public void setEstado(String estado) {
        this.estado = estado;
    }

    public String getPais() {
        return pais;
    }

    public void setPais(String pais) {
        this.pais = pais;
    }

    public Integer getRedeEmpresaId() {
        return redeEmpresaId;
    }

    public void setRedeEmpresaId(Integer redeEmpresaId) {
        this.redeEmpresaId = redeEmpresaId;
    }

    public String getChaveRede() {
        return chaveRede;
    }

    public void setChaveRede(String chaveRede) {
        this.chaveRede = chaveRede;
    }
}
