package com.pacto.relatorioms.dto.base;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

@JsonIgnoreProperties(ignoreUnknown = true)
public class RedeDTO {

    private Integer idRede;
    private String chaveRede;
    private String chave;
    private String nomeFantasia;
    private String razaoSocial;
    private String zwUrl;
    private String planoMsUrl;
    private String[] modulosHabilitados;

    public Integer getIdRede() {
        return idRede;
    }

    public void setIdRede(Integer idRede) {
        this.idRede = idRede;
    }

    public String getChaveRede() {
        return chaveRede;
    }

    public void setChaveRede(String chaveRede) {
        this.chaveRede = chaveRede;
    }

    public String getChave() {
        return chave;
    }

    public void setChave(String chave) {
        this.chave = chave;
    }

    public String getZwUrl() {
        return zwUrl;
    }

    public void setZwUrl(String zwUrl) {
        this.zwUrl = zwUrl;
    }

    public String getPlanoMsUrl() {
        return planoMsUrl;
    }

    public void setPlanoMsUrl(String planoMsUrl) {
        this.planoMsUrl = planoMsUrl;
    }

    public String[] getModulosHabilitados() {
        return modulosHabilitados;
    }

    public void setModulosHabilitados(String[] modulosHabilitados) {
        this.modulosHabilitados = modulosHabilitados;
    }

    public String getNomeFantasia() {
        return nomeFantasia;
    }

    public void setNomeFantasia(String nomeFantasia) {
        this.nomeFantasia = nomeFantasia;
    }

    public String getRazaoSocial() {
        return razaoSocial;
    }

    public void setRazaoSocial(String razaoSocial) {
        this.razaoSocial = razaoSocial;
    }
}
