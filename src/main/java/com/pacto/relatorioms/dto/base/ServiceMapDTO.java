package com.pacto.relatorioms.dto.base;


import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

@JsonIgnoreProperties(ignoreUnknown = true)
public class ServiceMapDTO {

    /**
     * FIXME
     * @deprecated Agora é personagem.
     *             É preciso alterar os projeto que utilizam este atributo e remove-lo aqui
     */
    private String alunoMsUrl;
    /**
     * @deprecated Agora é autenticacao.
     *             É preciso alterar os projeto que utilizam este atributo e remove-lo aqui
     */
    private String loginMsUrl;
    /**
     * @deprecated Agora é personagem.
     *             É preciso alterar os projeto que utilizam este atributo e remove-lo aqui
     */
    private String colaboradorMsUrl;
    private String graduacaoMsUrl;
    /**
     * @deprecated Utilizar somente treinoUrl
     *             É preciso alterar os projeto que utilizam este atributo e remove-lo aqui
     */
    private String treinoApiUrl;
    private String treinoUrl;
    private String loginAppUrl;
    private String oamdUrl;
    private String zwUrl;
    private String personagemMsUrl;
    private String autenticacaoUrl;
    private String frontPersonal;
    private String planoMsUrl;
    private String produtoMsUrl;
    private String relatorioFull;
    private String sinteticoMsUrl;
    private String pactoPayDashUrl;
    private String cadastroAuxiliarUrl;
    private String zwFrontUrl;
    private String treinoFrontUrl;
    private String apiZwUrl;
    private String clubeVantagensMsUrl;
    private String relatorioMsUrl;
    private String midiaMsUrl;

    public String getPersonagemMsUrl() {
        return personagemMsUrl;
    }

    public void setPersonagemMsUrl(String personagemMsUrl) {
        this.personagemMsUrl = personagemMsUrl;
    }

    public String getAlunoMsUrl() {
        return alunoMsUrl;
    }

    public void setAlunoMsUrl(String alunoMsUrl) {
        this.alunoMsUrl = alunoMsUrl;
    }

    public String getLoginMsUrl() {
        return loginMsUrl;
    }

    public void setLoginMsUrl(String loginMsUrl) {
        this.loginMsUrl = loginMsUrl;
    }

    public String getColaboradorMsUrl() {
        return colaboradorMsUrl;
    }

    public void setColaboradorMsUrl(String colaboradorMsUrl) {
        this.colaboradorMsUrl = colaboradorMsUrl;
    }

    public String getGraduacaoMsUrl() {
        return graduacaoMsUrl;
    }

    public void setGraduacaoMsUrl(String graduacaoMsUrl) {
        this.graduacaoMsUrl = graduacaoMsUrl;
    }

    public String getTreinoApiUrl() {
        return treinoApiUrl;
    }

    public void setTreinoApiUrl(String treinoApiUrl) {
        this.treinoApiUrl = treinoApiUrl;
    }

    public String getLoginAppUrl() {
        return loginAppUrl;
    }

    public void setLoginAppUrl(String loginAppUrl) {
        this.loginAppUrl = loginAppUrl;
    }

    public String getOamdUrl() {
        return oamdUrl;
    }

    public void setOamdUrl(String oamdUrl) {
        this.oamdUrl = oamdUrl;
    }

    public String getZwUrl() {
        return zwUrl;
    }

    public void setZwUrl(String zwUrl) {
        this.zwUrl = zwUrl;
    }

    public String getAutenticacaoUrl() {
        return autenticacaoUrl;
    }

    public void setAutenticacaoUrl(String autenticacaoUrl) {
        this.autenticacaoUrl = autenticacaoUrl;
    }

    public String getTreinoUrl() {
        return treinoUrl;
    }

    public void setTreinoUrl(String treinoUrl) {
        this.treinoUrl = treinoUrl;
    }

    public String getFrontPersonal() {
        return frontPersonal;
    }

    public void setFrontPersonal(String frontPersonal) {
        this.frontPersonal = frontPersonal;
    }

    public String getPlanoMsUrl() {
        return planoMsUrl;
    }

    public void setPlanoMsUrl(String planoMsUrl) {
        this.planoMsUrl = planoMsUrl;
    }

    public String getProdutoMsUrl() {
        return produtoMsUrl;
    }

    public void setProdutoMsUrl(String produtoMsUrl) {
        this.produtoMsUrl = produtoMsUrl;
    }

    public String getRelatorioFull() {
        return relatorioFull;
    }

    public void setRelatorioFull(String relatorioFull) {
        this.relatorioFull = relatorioFull;
    }

    public String getSinteticoMsUrl() {
        return sinteticoMsUrl;
    }

    public void setSinteticoMsUrl(String sinteticoMsUrl) {
        this.sinteticoMsUrl = sinteticoMsUrl;
    }

    public String getPactoPayDashUrl() {
        return pactoPayDashUrl;
    }

    public void setPactoPayDashUrl(String pactoPayDashUrl) {
        this.pactoPayDashUrl = pactoPayDashUrl;
    }

    public String getCadastroAuxiliarUrl() {
        return cadastroAuxiliarUrl;
    }

    public void setCadastroAuxiliarUrl(String cadastroAuxiliarUrl) {
        this.cadastroAuxiliarUrl = cadastroAuxiliarUrl;
    }

    public String getZwFrontUrl() {
        return zwFrontUrl;
    }

    public void setZwFrontUrl(String zwFrontUrl) {
        this.zwFrontUrl = zwFrontUrl;
    }

    public String getTreinoFrontUrl() {
        return treinoFrontUrl;
    }

    public void setTreinoFrontUrl(String treinoFrontUrl) {
        this.treinoFrontUrl = treinoFrontUrl;
    }

    public String getApiZwUrl() {
        return apiZwUrl;
    }

    public void setApiZwUrl(String apiZwUrl) {
        this.apiZwUrl = apiZwUrl;
    }

    public String getClubeVantagensMsUrl() {
        return clubeVantagensMsUrl;
    }

    public void setClubeVantagensMsUrl(String clubeVantagensMsUrl) {
        this.clubeVantagensMsUrl = clubeVantagensMsUrl;
    }

    public String getRelatorioMsUrl() {
        return relatorioMsUrl;
    }

    public void setRelatorioMsUrl(String relatorioMsUrl) {
        this.relatorioMsUrl = relatorioMsUrl;
    }

    public String getMidiaMsUrl() {
        return midiaMsUrl;
    }

    public void setMidiaMsUrl(String midiaMsUrl) {
        this.midiaMsUrl = midiaMsUrl;
    }
}
