package com.pacto.relatorioms.dto.base;


/**
 * DTO de usuário simples, um DTO apenas com os dados de acesso de um usuário, objeto bem menor que a entidade
 * Usuario para permitir criar caches em memória e trafegar menos dados a cada
 * requisição, eliminando a necessidade da IDA ao banco a cada requisição.
 *
 */
public class UsuarioSimplesDTO {

    private Integer codZw;
    private Integer codTreino;
    private Integer perfilTreino;
    private Integer perfilZw;
    private String chave;
    private String username;
    private Integer idEmpresa;
    private String token;

    public UsuarioSimplesDTO() {
    }

    public Integer getCodZw() {
        return codZw;
    }

    public void setCodZw(Integer codZw) {
        this.codZw = codZw;
    }

    public Integer getCodTreino() {
        return codTreino;
    }

    public void setCodTreino(Integer codTreino) {
        this.codTreino = codTreino;
    }

    public Integer getPerfilTreino() {
        return perfilTreino;
    }

    public void setPerfilTreino(Integer perfilTreino) {
        this.perfilTreino = perfilTreino;
    }

    public Integer getPerfilZw() {
        return perfilZw;
    }

    public void setPerfilZw(Integer perfilZw) {
        this.perfilZw = perfilZw;
    }

    public String getChave() {
        return chave;
    }

    public void setChave(String chave) {
        this.chave = chave;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public Integer getIdEmpresa() {
        return idEmpresa;
    }

    public void setIdEmpresa(Integer idEmpresa) {
        this.idEmpresa = idEmpresa;
    }

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }
}
