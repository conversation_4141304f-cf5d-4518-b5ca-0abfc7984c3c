package com.pacto.relatorioms.dto;

import com.fasterxml.jackson.annotation.JsonInclude;

import java.util.Date;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class LogDTO {

    private Integer codigo;
    private String nomeEntidade;
    private String nomeEntidadeDescricao;
    private String chavePrimaria;
    private String chavePrimariaEntidadeSubordinada;
    private String nomeCampo;
    private String valorCampoAnterior;
    private String valorCampoAlterado;
    private Date dataAlteracao;
    private String responsavelAlteracao;
    private String operacao;
    private Integer pessoa;
    private Integer cliente;

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getNomeEntidade() {
        return nomeEntidade;
    }

    public void setNomeEntidade(String nomeEntidade) {
        this.nomeEntidade = nomeEntidade;
    }

    public String getNomeEntidadeDescricao() {
        return nomeEntidadeDescricao;
    }

    public void setNomeEntidadeDescricao(String nomeEntidadeDescricao) {
        this.nomeEntidadeDescricao = nomeEntidadeDescricao;
    }

    public String getChavePrimaria() {
        return chavePrimaria;
    }

    public void setChavePrimaria(String chavePrimaria) {
        this.chavePrimaria = chavePrimaria;
    }

    public String getChavePrimariaEntidadeSubordinada() {
        return chavePrimariaEntidadeSubordinada;
    }

    public void setChavePrimariaEntidadeSubordinada(String chavePrimariaEntidadeSubordinada) {
        this.chavePrimariaEntidadeSubordinada = chavePrimariaEntidadeSubordinada;
    }

    public String getNomeCampo() {
        return nomeCampo;
    }

    public void setNomeCampo(String nomeCampo) {
        this.nomeCampo = nomeCampo;
    }

    public String getValorCampoAnterior() {
        return valorCampoAnterior;
    }

    public void setValorCampoAnterior(String valorCampoAnterior) {
        this.valorCampoAnterior = valorCampoAnterior;
    }

    public String getValorCampoAlterado() {
        return valorCampoAlterado;
    }

    public void setValorCampoAlterado(String valorCampoAlterado) {
        this.valorCampoAlterado = valorCampoAlterado;
    }

    public Date getDataAlteracao() {
        return dataAlteracao;
    }

    public void setDataAlteracao(Date dataAlteracao) {
        this.dataAlteracao = dataAlteracao;
    }

    public String getResponsavelAlteracao() {
        return responsavelAlteracao;
    }

    public void setResponsavelAlteracao(String responsavelAlteracao) {
        this.responsavelAlteracao = responsavelAlteracao;
    }

    public String getOperacao() {
        return operacao;
    }

    public void setOperacao(String operacao) {
        this.operacao = operacao;
    }

    public Integer getPessoa() {
        return pessoa;
    }

    public void setPessoa(Integer pessoa) {
        this.pessoa = pessoa;
    }

    public Integer getCliente() {
        return cliente;
    }

    public void setCliente(Integer cliente) {
        this.cliente = cliente;
    }
}
