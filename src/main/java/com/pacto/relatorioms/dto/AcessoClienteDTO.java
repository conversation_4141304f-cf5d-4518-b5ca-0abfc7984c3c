package com.pacto.relatorioms.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.pacto.config.utils.UteisValidacao;
import com.pacto.relatorioms.enums.MeioIdentificacaoEnum;
import com.pacto.relatorioms.enums.SituacaoAcessoEnum;

import java.io.Serializable;
import java.util.Date;


@JsonInclude(JsonInclude.Include.NON_NULL)
public class AcessoClienteDTO {
    private Integer codigo;
    private String sentido;
    private SituacaoAcessoEnum situacao;
    private Date dataHoraEntrada;
    private Date dataHoraSaida;
    private Integer tipoAcesso;
    private Date dataRegistro;
    private String ticket;
    private MeioIdentificacaoEnum meioIdentificacaoEntrada;
    private MeioIdentificacaoEnum meioIdentificacaoSaida;
    private ClienteDTO cliente;
    private LocalAcessoDTO localAcesso;
    private ColetorDTO coletor;
    private UsuarioDTO usuario;
    private String intervaloDataHoras="";
    private String nomeCodEmpresaAcessou="";
    private String nomeCodEmpresaOrigem="";
    private String nomeCpfEmailClienteOrigem="";

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public ClienteDTO getCliente() {
        return cliente;
    }

    public void setCliente(ClienteDTO cliente) {
        this.cliente = cliente;
    }

    public String getSentido() {
        return sentido;
    }

    public void setSentido(String sentido) {
        this.sentido = sentido;
    }

    public SituacaoAcessoEnum getSituacao() {
        return situacao;
    }

    public void setSituacao(SituacaoAcessoEnum situacao) {
        this.situacao = situacao;
    }

    public LocalAcessoDTO getLocalAcesso() {
        return localAcesso;
    }

    public void setLocalAcesso(LocalAcessoDTO localAcesso) {
        this.localAcesso = localAcesso;
    }

    public ColetorDTO getColetor() {
        return coletor;
    }

    public void setColetor(ColetorDTO coletor) {
        this.coletor = coletor;
    }

    public UsuarioDTO getUsuario() {
        return usuario;
    }

    public void setUsuario(UsuarioDTO usuario) {
        this.usuario = usuario;
    }

    public Date getDataHoraEntrada() {
        return dataHoraEntrada;
    }

    public void setDataHoraEntrada(Date dataHoraEntrada) {
        this.dataHoraEntrada = dataHoraEntrada;
    }

    public Date getDataHoraSaida() {
        return dataHoraSaida;
    }

    public void setDataHoraSaida(Date dataHoraSaida) {
        this.dataHoraSaida = dataHoraSaida;
    }

    public MeioIdentificacaoEnum getMeioIdentificacaoEntrada() {
        return meioIdentificacaoEntrada;
    }

    public void setMeioIdentificacaoEntrada(MeioIdentificacaoEnum meioIdentificacaoEntrada) {
        this.meioIdentificacaoEntrada = meioIdentificacaoEntrada;
    }

    public MeioIdentificacaoEnum getMeioIdentificacaoSaida() {
        return meioIdentificacaoSaida;
    }

    public void setMeioIdentificacaoSaida(MeioIdentificacaoEnum meioIdentificacaoSaida) {
        this.meioIdentificacaoSaida = meioIdentificacaoSaida;
    }

    public Integer getTipoAcesso() {
        return tipoAcesso;
    }

    public void setTipoAcesso(Integer tipoAcesso) {
        this.tipoAcesso = tipoAcesso;
    }

    public Date getDataRegistro() {
        return dataRegistro;
    }

    public void setDataRegistro(Date dataRegistro) {
        this.dataRegistro = dataRegistro;
    }

    public String getTicket() {
        return ticket;
    }

    public void setTicket(String ticket) {
        this.ticket = ticket;
    }

    public String getIntervaloDataHoras() {
        return intervaloDataHoras;
    }

    public void setIntervaloDataHoras(String intervaloDataHoras) {
        this.intervaloDataHoras = intervaloDataHoras;
    }

    public String getNomeCodEmpresaAcessou() {
        return nomeCodEmpresaAcessou;
    }

    public void setNomeCodEmpresaAcessou(String nomeCodEmpresaAcessou) {
        this.nomeCodEmpresaAcessou = nomeCodEmpresaAcessou;
    }

    public String getNomeCodEmpresaOrigem() {
        if (UteisValidacao.emptyString(nomeCodEmpresaOrigem)) {
            return localAcesso.getEmpresa().getNome();
        }
        return nomeCodEmpresaOrigem;
    }

    public void setNomeCodEmpresaOrigem(String nomeCodEmpresaOrigem) {
        this.nomeCodEmpresaOrigem = nomeCodEmpresaOrigem;
    }

    public String getNomeCpfEmailClienteOrigem() {
        return nomeCpfEmailClienteOrigem;
    }

    public void setNomeCpfEmailClienteOrigem(String nomeCpfEmailClienteOrigem) {
        this.nomeCpfEmailClienteOrigem = nomeCpfEmailClienteOrigem;
    }

    public String getMatriculaClienteOrigem() {
        if (UteisValidacao.emptyString(nomeCpfEmailClienteOrigem)) return cliente.getMatricula();
        String[] matriculaNomeCpf = nomeCpfEmailClienteOrigem.split(";");
        return matriculaNomeCpf[0];
    }

    public String getNomeClienteOrigem() {
        if (UteisValidacao.emptyString(nomeCpfEmailClienteOrigem)) return cliente.getPessoa().getNome();
        String[] matriculaNomeCpf = nomeCpfEmailClienteOrigem.split(";");
        return matriculaNomeCpf[1];
    }

    public String getCpfClienteOrigem() {
        if (UteisValidacao.emptyString(nomeCpfEmailClienteOrigem)) {
            return cliente != null ? cliente.getPessoa().getCfp() : "";
        }

        String[] matriculaNomeCpf = nomeCpfEmailClienteOrigem.split(";");

        if (matriculaNomeCpf.length >= 3 && !UteisValidacao.emptyString(matriculaNomeCpf[2]) && !"null".equalsIgnoreCase(matriculaNomeCpf[2])) {
            return matriculaNomeCpf[2];
        }

        return cliente != null ? cliente.getPessoa().getCfp() : "";
    }
}
