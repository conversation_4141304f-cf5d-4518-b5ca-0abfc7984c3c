package com.pacto.relatorioms.dto;

import com.pacto.relatorioms.dto.base.MovParcelaDTO;


public class ItemRelatorioPersonalDTO {

    private String personal;
    private String aluno;
    private MovParcelaDTO parcela;
    private String produto;
    private Double valor;
    private Double desconto;
    private Double valorFinal;
    private String situacao;
    private Integer controleTaxaPersonal;

    public String getPersonal() {
        return personal;
    }

    public void setPersonal(String personal) {
        this.personal = personal;
    }

    public String getAluno() {
        return aluno;
    }

    public void setAluno(String aluno) {
        this.aluno = aluno;
    }

    public String getProduto() {
        return produto;
    }

    public void setProduto(String produto) {
        this.produto = produto;
    }

    public Double getValor() {
        return valor;
    }

    public void setValor(Double valor) {
        this.valor = valor;
    }

    public Double getDesconto() {
        return desconto;
    }

    public void setDesconto(Double desconto) {
        this.desconto = desconto;
    }

    public Double getValorFinal() {
        return valorFinal;
    }

    public void setValorFinal(Double valorFinal) {
        this.valorFinal = valorFinal;
    }

    public String getSituacao() {
        return situacao;
    }

    public void setSituacao(String situacao) {
        this.situacao = situacao;
    }

    public MovParcelaDTO getParcela() {
        return parcela;
    }

    public void setParcela(MovParcelaDTO parcela) {
        this.parcela = parcela;
    }

    public Integer getControleTaxaPersonal() {
        return controleTaxaPersonal;
    }

    public void setControleTaxaPersonal(Integer controleTaxaPersonal) {
        this.controleTaxaPersonal = controleTaxaPersonal;
    }
}
