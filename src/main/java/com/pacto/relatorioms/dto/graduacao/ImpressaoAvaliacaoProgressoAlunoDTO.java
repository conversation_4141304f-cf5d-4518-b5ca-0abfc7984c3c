package com.pacto.relatorioms.dto.graduacao;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;

import java.util.List;

@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ImpressaoAvaliacaoProgressoAlunoDTO {

    private Integer matricula;
    private String nomeAluno;
    private String urlFotoAluno;
    private String nomeEmpresa;
    private String professor;
    private String dataAvaliacao;
    private String nivel;
    private String horarios;
    private String dias;
    private String resultado;
    private String observacaoGeral;
    private List<ImpressaoAtividadeDTO> atividades;
    private List<InformacaoFrequenciaAlunoDTO> frequenciaAluno;
    private InformacaoHistoricoAlunoDTO avaliacaoAnterior;

    public ImpressaoAvaliacaoProgressoAlunoDTO() { }

    public ImpressaoAvaliacaoProgressoAlunoDTO(Integer matricula,
                                               String professor,
                                               String dataAvaliacao,
                                               String nivel,
                                               String horarios,
                                               String dias,
                                               String resultado,
                                               String observacaoGeral,
                                               List<ImpressaoAtividadeDTO> atividades,
                                               List<InformacaoFrequenciaAlunoDTO> frequenciaAluno,
                                               InformacaoHistoricoAlunoDTO avaliacaoAnterior) {
        this.matricula = matricula;
        this.professor = professor;
        this.dataAvaliacao = dataAvaliacao;
        this.nivel = nivel;
        this.horarios = horarios;
        this.dias = dias;
        this.resultado = resultado;
        this.observacaoGeral = observacaoGeral;
        this.atividades = atividades;
        this.frequenciaAluno = frequenciaAluno;
        this.avaliacaoAnterior = avaliacaoAnterior;
    }

    public Integer getMatricula() {
        return matricula;
    }

    public void setMatricula(Integer matricula) {
        this.matricula = matricula;
    }

    public String getNomeAluno() {
        return nomeAluno;
    }

    public void setNomeAluno(String nomeAluno) {
        this.nomeAluno = nomeAluno;
    }

    public String getUrlFotoAluno() {
        return urlFotoAluno;
    }

    public void setUrlFotoAluno(String urlFotoAluno) {
        this.urlFotoAluno = urlFotoAluno;
    }

    public String getNomeEmpresa() {
        return nomeEmpresa;
    }

    public void setNomeEmpresa(String nomeEmpresa) {
        this.nomeEmpresa = nomeEmpresa;
    }

    public String getProfessor() {
        return professor;
    }

    public void setProfessor(String professor) {
        this.professor = professor;
    }

    public String getDataAvaliacao() {
        return dataAvaliacao;
    }

    public void setDataAvaliacao(String dataAvaliacao) {
        this.dataAvaliacao = dataAvaliacao;
    }

    public String getNivel() {
        return nivel;
    }

    public void setNivel(String nivel) {
        this.nivel = nivel;
    }

    public String getHorarios() {
        return horarios;
    }

    public void setHorarios(String horarios) {
        this.horarios = horarios;
    }

    public String getDias() {
        return dias;
    }

    public void setDias(String dias) {
        this.dias = dias;
    }

    public String getResultado() {
        return resultado;
    }

    public void setResultado(String resultado) {
        this.resultado = resultado;
    }

    public String getObservacaoGeral() {
        return observacaoGeral;
    }

    public void setObservacaoGeral(String observacaoGeral) {
        this.observacaoGeral = observacaoGeral;
    }

    public List<ImpressaoAtividadeDTO> getAtividades() {
        return atividades;
    }

    public void setAtividades(List<ImpressaoAtividadeDTO> atividades) {
        this.atividades = atividades;
    }

    public List<InformacaoFrequenciaAlunoDTO> getFrequenciaAluno() {
        return frequenciaAluno;
    }

    public void setFrequenciaAluno(List<InformacaoFrequenciaAlunoDTO> frequenciaAluno) {
        this.frequenciaAluno = frequenciaAluno;
    }

    public InformacaoHistoricoAlunoDTO getAvaliacaoAnterior() {
        return avaliacaoAnterior;
    }

    public void setAvaliacaoAnterior(InformacaoHistoricoAlunoDTO avaliacaoAnterior) {
        this.avaliacaoAnterior = avaliacaoAnterior;
    }

}
