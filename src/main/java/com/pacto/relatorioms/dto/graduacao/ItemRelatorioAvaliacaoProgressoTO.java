package com.pacto.relatorioms.dto.graduacao;

import net.sf.jasperreports.engine.data.JRBeanCollectionDataSource;

import java.io.Serializable;

public class ItemRelatorioAvaliacaoProgressoTO implements Serializable {

    private String valor1;
    private String valor2;
    private String valor3;
    private String valor4;
    private String SUBREPORT_DIR;
    private boolean possuiSubAtividade;
    private JRBeanCollectionDataSource sub_atividadeJR;

    public ItemRelatorioAvaliacaoProgressoTO(String valor1, String valor2, String valor3) {
        this.valor1 = valor1;
        this.valor2 = valor2;
        this.valor3 = valor3;
    }

    public ItemRelatorioAvaliacaoProgressoTO(String valor1, String valor2, String valor3, String valor4, String SUBREPORT_DIR, JRBeanCollectionDataSource sub_atividadeJR) {
        this.valor1 = valor1;
        this.valor2 = valor2;
        this.valor3 = valor3;
        this.valor4 = valor4;
        this.SUBREPORT_DIR = SUBREPORT_DIR;
        this.sub_atividadeJR = sub_atividadeJR;
    }

    public String getValor1() {
        return valor1;
    }

    public void setValor1(String valor1) {
        this.valor1 = valor1;
    }

    public String getValor2() {
        return valor2;
    }

    public void setValor2(String valor2) {
        this.valor2 = valor2;
    }

    public String getValor3() {
        return valor3;
    }

    public void setValor3(String valor3) {
        this.valor3 = valor3;
    }

    public String getValor4() {
        return valor4;
    }

    public void setValor4(String valor4) {
        this.valor4 = valor4;
    }

    public String getSUBREPORT_DIR() {
        return SUBREPORT_DIR;
    }

    public void setSUBREPORT_DIR(String SUBREPORT_DIR) {
        this.SUBREPORT_DIR = SUBREPORT_DIR;
    }

    public JRBeanCollectionDataSource getSub_atividadeJR() {
        return sub_atividadeJR;
    }

    public void setSub_atividadeJR(JRBeanCollectionDataSource sub_atividadeJR) {
        this.sub_atividadeJR = sub_atividadeJR;
    }

    public boolean isPossuiSubAtividade() {
        return possuiSubAtividade;
    }

    public void setPossuiSubAtividade(boolean possuiSubAtividade) {
        this.possuiSubAtividade = possuiSubAtividade;
    }


}
