package com.pacto.relatorioms.dto.graduacao;

import com.pacto.config.exceptions.ServiceException;
import com.pacto.config.security.interfaces.RequestService;
import com.pacto.config.utils.UteisValidacao;
import com.pacto.relatorioms.services.interfaces.GraduacaoService;
import com.pacto.relatorioms.utils.VisualizadorRelatorio;
import net.sf.jasperreports.engine.data.JRBeanCollectionDataSource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import java.io.File;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public class GraduacaoServiceImpl implements GraduacaoService {

    @Autowired
    private RequestService requestService;

    public GraduacaoServiceImpl(RequestService requestService) {
        this.requestService = requestService;
    }


    @Override
    public String exportarAvaliacaoProgressoAluno(ImpressaoAvaliacaoProgressoAlunoDTO dadosImprimirDTO, HttpServletRequest request) throws ServiceException {
        try {
            String ctx = requestService.getUsuarioAtual().getChave();
            Map<String, Object> parameters = new HashMap<String, Object>();
            parameters.put("fotoAluno", dadosImprimirDTO.getUrlFotoAluno());
            parameters.put("nomeEmpresa", primeiraLetraMaiuscula(dadosImprimirDTO.getNomeEmpresa()));
            parameters.put("nomeAluno", primeiraLetraMaiuscula(dadosImprimirDTO.getNomeAluno()));
            parameters.put("dataAvaliacao", dadosImprimirDTO.getDataAvaliacao());
            parameters.put("horariosAulas", dadosImprimirDTO.getHorarios());
            parameters.put("nomeProfessor", primeiraLetraMaiuscula(dadosImprimirDTO.getProfessor()));
            parameters.put("nomeNivelAtual", primeiraLetraMaiuscula(dadosImprimirDTO.getNivel()));
            parameters.put("diasAulas", dadosImprimirDTO.getDias());
            parameters.put("resultado", dadosImprimirDTO.getResultado());
            parameters.put("SUBREPORT_DIR", getDesignIReportAvProgressoSubReport());

            List<ItemRelatorioAvaliacaoProgressoTO> item = new ArrayList<>();
            int countAtv = 1;
            for (ImpressaoAtividadeDTO atividade : dadosImprimirDTO.getAtividades()) {
                String urlFotoAtv =
                        UteisValidacao.emptyString(atividade.getImageUri())
                                ? getNoImage()
                                :  atividade.getImageUri();
                atividade.setImageUri(urlFotoAtv);

                List<ItemRelatorioAvaliacaoProgressoTO> subAtividades = montarSubAtividades(atividade);
                String obs = atividade.getPossuiSubAtividade() ? null : atividade.getObservacao();
                String resposta = atividade.getPossuiSubAtividade() ? null : atividade.getResposta();
                ItemRelatorioAvaliacaoProgressoTO atividadeTO = new ItemRelatorioAvaliacaoProgressoTO(
                            countAtv + ". " + atividade.getNome(),
                            resposta,
                            obs,
                            urlFotoAtv,
                            getDesignIReportAvProgressoSubReport(),
                            new JRBeanCollectionDataSource(subAtividades)
                    );
                    atividadeTO.setPossuiSubAtividade(atividade.getPossuiSubAtividade());
                    item.add(atividadeTO);

                    countAtv++;
                }


            parameters.put("atividadeJR", new JRBeanCollectionDataSource(item));

            parameters.put("jan", dadosImprimirDTO.getFrequenciaAluno().get(0).getPresenca().toString());
            parameters.put("fev", dadosImprimirDTO.getFrequenciaAluno().get(1).getPresenca().toString());
            parameters.put("mar", dadosImprimirDTO.getFrequenciaAluno().get(2).getPresenca().toString());
            parameters.put("abr", dadosImprimirDTO.getFrequenciaAluno().get(3).getPresenca().toString());
            parameters.put("mai", dadosImprimirDTO.getFrequenciaAluno().get(4).getPresenca().toString());
            parameters.put("jun", dadosImprimirDTO.getFrequenciaAluno().get(5).getPresenca().toString());
            parameters.put("jul", dadosImprimirDTO.getFrequenciaAluno().get(6).getPresenca().toString());
            parameters.put("ago", dadosImprimirDTO.getFrequenciaAluno().get(7).getPresenca().toString());
            parameters.put("set", dadosImprimirDTO.getFrequenciaAluno().get(8).getPresenca().toString());
            parameters.put("out", dadosImprimirDTO.getFrequenciaAluno().get(9).getPresenca().toString());
            parameters.put("nov", dadosImprimirDTO.getFrequenciaAluno().get(10).getPresenca().toString());
            parameters.put("dez", dadosImprimirDTO.getFrequenciaAluno().get(11).getPresenca().toString());

            if (dadosImprimirDTO.getAvaliacaoAnterior() != null) {
                parameters.put("mesAno", dadosImprimirDTO.getAvaliacaoAnterior().getMesAno());
                String professoresHistorico = "";
                for (String professor : dadosImprimirDTO.getAvaliacaoAnterior().getProfessores()) {
                    professoresHistorico += "; " + primeiraLetraMaiuscula(professor);
                }
                if (professoresHistorico.length() > 0) {
                    professoresHistorico = new StringBuilder(professoresHistorico).deleteCharAt(0).toString();
                }
                parameters.put("professorHistorico", professoresHistorico);
                parameters.put("nivelHistorico", dadosImprimirDTO.getAvaliacaoAnterior().getNivel());
                String horariosHistorico = "";
                for (String horario : dadosImprimirDTO.getAvaliacaoAnterior().getHorarios()) {
                    horariosHistorico += "; " + horario;
                }
                if (horariosHistorico.length() > 0) {
                    horariosHistorico = new StringBuilder(horariosHistorico).deleteCharAt(0).toString();
                }
                parameters.put("horarioHistorico", horariosHistorico);
                parameters.put("resultadoHistorico", dadosImprimirDTO.getAvaliacaoAnterior().getResultado());
                parameters.put("diasHistorico", dadosImprimirDTO.getAvaliacaoAnterior().getDias());
            } else {
                parameters.put("mesAno", "-");
                parameters.put("professorHistorico", "-");
                parameters.put("nivelHistorico", "-");
                parameters.put("horarioHistorico", "-");
                parameters.put("resultadoHistorico", "-");
                parameters.put("diasHistorico", "-");
            }

            parameters.put("observacaoGeral", dadosImprimirDTO.getObservacaoGeral() == null ? "" : dadosImprimirDTO.getObservacaoGeral());

            parameters.put("nomeDesignIReport", getDesignIReportAvProgresso());
            parameters.put("nomeRelatorio", "resultadoAvaliacaoProgresso");

            VisualizadorRelatorio visualizadorRelatorio = new VisualizadorRelatorio();
            String pdf = visualizadorRelatorio.exportarPDFSemLista(ctx, request, parameters);

            return pdf;
        } catch (Exception e) {
            e.printStackTrace();
            throw new ServiceException(e);
        }
    }

    private List<ItemRelatorioAvaliacaoProgressoTO> montarSubAtividades(ImpressaoAtividadeDTO atividade) {
        List<ItemRelatorioAvaliacaoProgressoTO> subAtividades = new ArrayList<>();
        if (atividade.getPossuiSubAtividade()) {
            char countSub = 'a';
            for (ImpressaoSubAtividadeDTO subAtividadeDTO : atividade.getSubAtividades()) {
                subAtividades.add(new ItemRelatorioAvaliacaoProgressoTO(
                        subAtividadeDTO.getResposta() != null ? subAtividadeDTO.getResposta() : "", // valor1 = Resposta
                        subAtividadeDTO.getObservacao() != null ? subAtividadeDTO.getObservacao() : "", // valor2 = Observação
                        countSub + ". " + subAtividadeDTO.getNome(), // valor3 = Nome da sub-atividade
                        "",
                        getDesignIReportAvProgressoSubReport(),
                        null
                ));
                countSub++;
            }
        } else {
            subAtividades.add(new ItemRelatorioAvaliacaoProgressoTO(
                    atividade.getResposta() != null ? atividade.getResposta() : "", // valor1 = Resposta
                    atividade.getObservacao() != null ? atividade.getObservacao() : "", // valor2 = Observação
                    atividade.getDescricao() != null ? atividade.getDescricao() : atividade.getNome(), // valor3 = Descrição/Nome
                    "",
                    getDesignIReportAvProgressoSubReport(),
                    null
            ));
        }

        return subAtividades;
    }

    private String primeiraLetraMaiuscula(String str) {
        StringBuilder sb = new StringBuilder();
        if (!str.equals("") && str != null) {
            String[] words = str.split("\\s");

            for(int i = 0; i < words.length; i++){
                sb.append(words[i].substring(0, 1).toUpperCase() + words[i].substring(1).toLowerCase());
                sb.append(" ");
            }
        }

        return sb.toString();
    }

    public String getDesignIReportAvProgresso() {
        return ("relatorio" + File.separator +
                "avaliacaoProgressoGraduacao" + File.separator +
                "avaliacao_progresso_aluno.jasper");
    }

    public String getDesignIReportAvProgressoSubReport() {
        return ("relatorio" + File.separator +
                "avaliacaoProgressoGraduacao" + File.separator);
    }

    public String getNoImage() {
        return ("relatorio" + File.separator +
                "avaliacaoProgressoGraduacao" + File.separator +
                "noImage.png");
    }

}
