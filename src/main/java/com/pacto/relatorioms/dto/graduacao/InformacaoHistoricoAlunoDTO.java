package com.pacto.relatorioms.dto.graduacao;

import java.util.List;

public class InformacaoHistoricoAlunoDTO {

    private String mesAno;
    private List<String> professores;
    private List<String> horarios;
    private String dias;
    private String nivel;
    private String resultado;

    public InformacaoHistoricoAlunoDTO() { }

    public InformacaoHistoricoAlunoDTO(String mesAno, List<String> professores, List<String> horarios, String dias, String nivel, String resultado) {
        this.mesAno = mesAno;
        this.professores = professores;
        this.horarios = horarios;
        this.dias = dias;
        this.nivel = nivel;
        this.resultado = resultado;
    }

    public String getMesAno() {
        return mesAno;
    }

    public void setMesAno(String mesAno) {
        this.mesAno = mesAno;
    }

    public List<String> getProfessores() {
        return professores;
    }

    public void setProfessores(List<String> professores) {
        this.professores = professores;
    }

    public List<String> getHorarios() {
        return horarios;
    }

    public void setHorarios(List<String> horarios) {
        this.horarios = horarios;
    }

    public String getDias() {
        return dias;
    }

    public void setDias(String dias) {
        this.dias = dias;
    }

    public String getNivel() {
        return nivel;
    }

    public void setNivel(String nivel) {
        this.nivel = nivel;
    }

    public String getResultado() {
        return resultado;
    }

    public void setResultado(String resultado) {
        this.resultado = resultado;
    }

}
