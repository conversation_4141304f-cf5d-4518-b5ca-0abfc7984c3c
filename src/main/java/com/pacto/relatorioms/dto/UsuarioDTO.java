package com.pacto.relatorioms.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.pacto.config.utils.Uteis;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class UsuarioDTO {
    private Integer codigo;
    private String nome;

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public String getPrimeiroNomeConcatenado() {
        if (nome == null) {
            nome = "";
        }
        return (Uteis.obterPrimeiroNomeConcatenadoSobreNome(nome));
    }
}
