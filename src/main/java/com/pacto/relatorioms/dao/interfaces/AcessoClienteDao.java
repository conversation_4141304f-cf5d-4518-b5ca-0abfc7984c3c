package com.pacto.relatorioms.dao.interfaces;

import com.pacto.config.dto.PaginadorDTO;
import com.pacto.relatorioms.entities.AcessoCliente;
import com.pacto.relatorioms.filter.FiltroListaAcessosJSON;

import java.util.List;

public interface AcessoClienteDao extends DaoGenerico<AcessoCliente, Integer>{

    List<AcessoCliente> consultarListaAcessosClientes(FiltroListaAcessosJSON filtros) throws Exception;

    List<AcessoCliente> consultarListaAcessosClientesOutrasUnidades(FiltroListaAcessosJSON filtros) throws Exception;

}
