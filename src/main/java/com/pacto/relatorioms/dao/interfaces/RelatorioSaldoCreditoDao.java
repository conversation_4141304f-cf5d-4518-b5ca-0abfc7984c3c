package com.pacto.relatorioms.dao.interfaces;


import com.pacto.relatorioms.dto.RelatorioSaldoCreditoDTO;
import com.pacto.config.dto.PaginadorDTO;
import com.pacto.relatorioms.filter.FiltroRelatorioSaldoCreditoJSON;
import com.pacto.relatorioms.filter.RelatorioSaldoCreditoFilter;

import java.util.List;

public interface RelatorioSaldoCreditoDao extends DaoGenerico<RelatorioSaldoCreditoFilter, Integer>{

    List<RelatorioSaldoCreditoDTO> consultarSaldoCredito(FiltroRelatorioSaldoCreditoJSON filtros, RelatorioSaldoCreditoFilter relatorioSaldoCreditoFilter, PaginadorDTO paginadorDTO) throws Exception;

}
