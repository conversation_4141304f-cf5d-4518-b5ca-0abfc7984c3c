package com.pacto.relatorioms.dao.interfaces;


import com.pacto.relatorioms.dto.RelatorioConvidadosDTO;
import com.pacto.config.dto.PaginadorDTO;
import com.pacto.relatorioms.filter.FiltroRelatorioConvidadosJSON;

import java.util.List;

public interface RelatorioConvidadosDao extends DaoGenerico<RelatorioConvidadosDTO, Integer>{

    List<RelatorioConvidadosDTO> consultarAlunosConvidados(FiltroRelatorioConvidadosJSON filtros, PaginadorDTO paginadorDTO) throws Exception;

}
