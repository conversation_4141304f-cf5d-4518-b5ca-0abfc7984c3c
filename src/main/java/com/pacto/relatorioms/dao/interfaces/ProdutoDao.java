package com.pacto.relatorioms.dao.interfaces;

import com.pacto.config.dto.PaginadorDTO;
import com.pacto.relatorioms.entities.Produto;
import com.pacto.relatorioms.filter.FiltroProdutoJSON;

import java.util.List;

public interface ProdutoDao extends DaoGenerico<Produto, Integer> {

    List<Produto> findAll(FiltroProdutoJSON filtros, PaginadorDTO paginadorDTO) throws Exception;

    List<Produto> findAllMin(FiltroProdutoJSON filtroProdutoJSON) throws Exception;
}
