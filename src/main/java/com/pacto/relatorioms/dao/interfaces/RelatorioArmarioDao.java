package com.pacto.relatorioms.dao.interfaces;


import com.pacto.config.dto.PaginadorDTO;
import com.pacto.relatorioms.entities.AluguelArmario;
import com.pacto.relatorioms.filter.RelatorioArmarioFiltroJSON;

import java.util.List;

public interface RelatorioArmarioDao extends DaoGenerico<AluguelArmario, Integer>{

    List<Object[]> consultarArmarios(RelatorioArmarioFiltroJSON filtros, PaginadorDTO paginadorDTO) throws Exception;

}
