package com.pacto.relatorioms.dao.interfaces;

import com.pacto.config.dto.PaginadorDTO;
import com.pacto.relatorioms.entities.ItemTaxaPersonal;
import com.pacto.config.exceptions.ServiceException;

import java.util.List;

public interface ItemTaxaPersonalDao extends DaoGenerico<ItemTaxaPersonal, Integer> {

    List<ItemTaxaPersonal> consultarPorControleTaxaPersonal(Integer controleTaxaPersonal, PaginadorDTO paginadorDTO) throws ServiceException;
}
