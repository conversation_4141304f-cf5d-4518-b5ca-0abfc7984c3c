package com.pacto.relatorioms.dao.interfaces;

import com.pacto.config.dto.PaginadorDTO;
import com.pacto.relatorioms.entities.Cliente;
import com.pacto.relatorioms.filter.FiltroClienteJSON;
import com.pacto.relatorioms.filter.FiltroRelatorioVisitantesJSON;

import java.util.List;

public interface ClienteDao extends DaoGenerico<Cliente, Integer> {

    List<Cliente> findAllRelatorioVisitante(FiltroRelatorioVisitantesJSON filtros, PaginadorDTO paginadorDTO) throws Exception;

    List<Cliente> findAll(FiltroClienteJSON filtros, PaginadorDTO paginadorDTO) throws Exception;

}
