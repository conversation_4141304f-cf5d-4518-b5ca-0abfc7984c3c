package com.pacto.relatorioms.dao.interfaces;


import com.pacto.config.dto.PaginadorDTO;
import com.pacto.relatorioms.dto.RelatorioGogoodDTO;
import com.pacto.relatorioms.filter.FiltroRelatorioGogoodJSON;
import com.pacto.relatorioms.filter.RelatorioGogoodFilter;

import java.util.List;


public interface RelatorioGogoodDao extends DaoGenerico<RelatorioGogoodFilter, Integer>{

    List<RelatorioGogoodDTO> consultarAlunosGogood(FiltroRelatorioGogoodJSON filtros, RelatorioGogoodFilter relatorioGympassFilter, PaginadorDTO paginadorDTO) throws Exception;

}
