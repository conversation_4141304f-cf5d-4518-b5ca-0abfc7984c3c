package com.pacto.relatorioms.dao.interfaces;


import com.pacto.relatorioms.dto.RelatorioGympassDTO;
import com.pacto.config.dto.PaginadorDTO;
import com.pacto.relatorioms.filter.FiltroRelatorioGympassJSON;
import com.pacto.relatorioms.filter.RelatorioGympassFilter;

import java.util.List;


public interface RelatorioGympassDao extends DaoGenerico<RelatorioGympassFilter, Integer>{

    List<RelatorioGympassDTO> consultarAlunosGympass(FiltroRelatorioGympassJSON filtros, RelatorioGympassFilter relatorioGympassFilter, PaginadorDTO paginadorDTO) throws Exception;

}
