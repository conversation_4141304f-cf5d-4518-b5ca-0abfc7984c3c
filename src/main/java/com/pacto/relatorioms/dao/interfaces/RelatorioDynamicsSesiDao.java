package com.pacto.relatorioms.dao.interfaces;

import com.pacto.relatorioms.filter.FiltroRelatorioDynamicsSesiJSON;
import com.pacto.relatorioms.filter.RelatorioDynamicsSesiFilter;

import java.util.List;

public interface RelatorioDynamicsSesiDao extends DaoGenerico<RelatorioDynamicsSesiFilter, Integer>{

    List<String> gerarLinhasDoArquivoTexto(FiltroRelatorioDynamicsSesiJSON filtros, RelatorioDynamicsSesiFilter relatorioDynamicsSesiFilter) throws Exception;
    List<String> validarSeProdutoNaoContemCentroResponsabilidade(FiltroRelatorioDynamicsSesiJSON filtros,
                                                                 RelatorioDynamicsSesiFilter relatorioDynamicsSesiFilter) throws Exception;
}
