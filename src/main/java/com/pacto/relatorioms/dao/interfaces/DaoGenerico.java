package com.pacto.relatorioms.dao.interfaces;

import org.hibernate.Session;
import org.hibernate.engine.spi.SessionImplementor;

import java.io.Serializable;
import java.sql.Connection;
import java.sql.ResultSet;
import java.util.List;
import java.util.Map;

public interface DaoGenerico<T, ID extends Serializable> {

    Session getCurrentSession();

    T findById(ID id) throws Exception;

    T findByName(String nome) throws Exception;

    T findByDescription(String nome) throws Exception;

    T findByType(String tipo) throws Exception;

    T findByParam(String[] atributos, Object[] valores) throws Exception;

    List<T> findAll() throws Exception;

    List<T> findAll(Long limit, Long offset) throws Exception;

    List<T> findByParam(final StringBuilder whereClause, Map<String, Object> params) throws Exception;

    List<T> findByParam(final StringBuilder whereClause, Map<String, Object> params, int max, int index) throws Exception;

    T save(T obj) throws Exception;

    T update(T obj) throws Exception;

    void delete(T obj) throws Exception;

    void delete(ID id) throws Exception;

    void deleteComParam(String[] atributos, Object[] valores) throws Exception;

    Number countWithParam(final String atributoCount, final StringBuilder whereClause, Map<String, Object> params) throws Exception;

    Number countWithParam(final String atributoCount, final StringBuilder joinClause, final StringBuilder whereClause, Map<String, Object> params) throws Exception;

    SessionImplementor createSessionCurrentWork() throws Exception;

    ResultSet createStatement(final Connection connection, final String sql) throws Exception;

}
