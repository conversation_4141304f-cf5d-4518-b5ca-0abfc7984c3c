package com.pacto.relatorioms.dao.interfaces;

import com.pacto.config.dto.PaginadorDTO;
import com.pacto.relatorioms.entities.PeriodoAcessoCliente;
import com.pacto.config.exceptions.ServiceException;

import java.util.List;

public interface PeriodoAcessoClienteDao extends DaoGenerico<PeriodoAcessoCliente, Integer> {

    List<PeriodoAcessoCliente> findAllByMatriculaRelGympass(Integer matricula, PaginadorDTO paginadorDTO) throws ServiceException;

    List<PeriodoAcessoCliente> findAllByMatriculaRelGogood(Integer matricula, PaginadorDTO paginadorDTO) throws ServiceException;
}
