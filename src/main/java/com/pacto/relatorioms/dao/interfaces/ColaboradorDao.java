package com.pacto.relatorioms.dao.interfaces;

import com.pacto.relatorioms.dto.ItemRelatorioPersonalDTO;
import com.pacto.config.dto.PaginadorDTO;
import com.pacto.relatorioms.entities.Colaborador;
import com.pacto.relatorioms.filter.FiltroColaboradorJSON;
import com.pacto.relatorioms.filter.FiltroRelatorioPersonalJSON;

import java.util.List;

public interface ColaboradorDao extends DaoGenerico<Colaborador, Integer> {

    List<Colaborador> findAll(FiltroColaboradorJSON filtros, PaginadorDTO paginadorDTO) throws Exception;

    List<Object[]> consultarRelatorioPersonal(FiltroRelatorioPersonalJSON filtros, PaginadorDTO paginadorDTO) throws Exception;

    Double consultarTotalLancado(FiltroRelatorioPersonalJSON filtros);

    Double consultarTotalPago(FiltroRelatorioPersonalJSON filtros);

    List<Colaborador> findAllByEmpresaId(Integer empresaId, FiltroColaboradorJSON filtros);

    List<Colaborador> findAll(FiltroColaboradorJSON filtros);
}
