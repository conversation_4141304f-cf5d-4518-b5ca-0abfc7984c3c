package com.pacto.relatorioms.dao.implementations;

import com.pacto.relatorioms.dao.interfaces.ItemTaxaPersonalDao;
import com.pacto.config.dto.PaginadorDTO;
import com.pacto.relatorioms.entities.ItemTaxaPersonal;
import com.pacto.config.exceptions.ServiceException;
import org.springframework.stereotype.Repository;

import javax.persistence.Query;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Repository
public class ItemTaxaPersonalDaoImpl extends DaoGenericoImpl<ItemTaxaPersonal, Integer> implements ItemTaxaPersonalDao {

    private static final int MAXIMO_RESULTADOS = 5;

    @Override
    public List<ItemTaxaPersonal> consultarPorControleTaxaPersonal(Integer controleTaxaPersonal, PaginadorDTO paginadorDTO) throws ServiceException {
        getCurrentSession().clear();

        int maxResults = MAXIMO_RESULTADOS;
        int indiceInicial = 0;

        if (paginadorDTO != null) {
            maxResults = paginadorDTO.getSize() == null ? MAXIMO_RESULTADOS : paginadorDTO.getSize().intValue();
            indiceInicial = paginadorDTO.getPage() == null ? indiceInicial : paginadorDTO.getPage().intValue() * maxResults;
        }

        Map<String, Object> params = new HashMap<>();
        StringBuilder hql = new StringBuilder();
        StringBuilder join = new StringBuilder();
        StringBuilder where = new StringBuilder();

        try{
            hql.append("SELECT obj FROM ItemTaxaPersonal obj ");
            where.append(" WHERE obj.controle.codigo = :controle ");
            params.put("controle", controleTaxaPersonal);

            if (paginadorDTO != null) {
                paginadorDTO.setQuantidadeTotalElementos(countWithParamClass("codigo", "ItemTaxaPersonal", join, where, params).longValue());
                if(paginadorDTO.getSort() != null) {
                    String sortColumn = paginadorDTO.getSort().split(",")[0].toLowerCase();
                    String sortDirection = paginadorDTO.getSort().split(",")[1];
                    if (sortColumn.equals("aluno")) {
                        where.append(" ORDER BY obj.aluno.pessoa.nome " + sortDirection);
                    } else if (sortColumn.equals("produto")) {
                        where.append(" ORDER BY obj.produto.descricao " + sortDirection);
                    } else if (sortColumn.equals("mesreferencia")) {
                        where.append(" ORDER BY obj.movProduto.mesReferencia " + sortDirection);
                    } else if (sortColumn.equals("valor")) {
                        where.append(" ORDER BY obj.produto.valorFinal " + sortDirection);
                    } else if (sortColumn.equals("desconto")) {
                        where.append(" ORDER BY obj.movProduto.valorDesconto " + sortDirection);
                    } else if (sortColumn.equals("valorfinal")) {
                        where.append(" ORDER BY obj.movProduto.totalFinal " + sortDirection);
                    }
                } else {
                    where.append(" ORDER BY obj.aluno.pessoa.nome ASC ");
                }
                paginadorDTO.setSize((long) maxResults);
                paginadorDTO.setPage((long) indiceInicial);
            }

            hql.append(join != null ? join.toString() : "");
            hql.append(where != null ? where.toString() : "");
            Query q = getCurrentSession().createQuery(hql.toString());

            if (params != null) {
                for (String p : params.keySet()) {
                    q.setParameter(p, params.get(p));
                }
            }

            if (maxResults != 0) {
                q.setMaxResults(maxResults);
            }
            if (indiceInicial != 0) {
                q.setFirstResult(indiceInicial);
            }

            return q.getResultList();
        } catch (Exception e) {
            throw new ServiceException(e);
        }
    }
}
