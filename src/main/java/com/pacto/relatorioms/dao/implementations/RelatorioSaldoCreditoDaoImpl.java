package com.pacto.relatorioms.dao.implementations;

import com.pacto.relatorioms.dao.interfaces.RelatorioSaldoCreditoDao;
import com.pacto.relatorioms.dto.RelatorioSaldoCreditoDTO;
import com.pacto.config.dto.PaginadorDTO;
import com.pacto.relatorioms.filter.FiltroRelatorioSaldoCreditoJSON;
import com.pacto.relatorioms.filter.RelatorioSaldoCreditoFilter;
import com.pacto.config.utils.Uteis;
import org.hibernate.query.Query;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Repository
public class RelatorioSaldoCreditoDaoImpl extends DaoGenericoImpl<RelatorioSaldoCreditoFilter, Integer> implements RelatorioSaldoCreditoDao {
    private static final int MAXIMO_RESULTADOS = 10;

    @Override
    public List<RelatorioSaldoCreditoDTO> consultarSaldoCredito(FiltroRelatorioSaldoCreditoJSON filtros, RelatorioSaldoCreditoFilter relatorioSaldoCreditoFilter, PaginadorDTO paginadorDTO) throws Exception {
        getCurrentSession().clear();

        int maxResults = MAXIMO_RESULTADOS;
        int indiceInicial = 0;

        if (paginadorDTO != null) {
            maxResults = paginadorDTO.getSize() == null ? MAXIMO_RESULTADOS : paginadorDTO.getSize().intValue();
            indiceInicial = paginadorDTO.getPage() == null ? indiceInicial : paginadorDTO.getPage().intValue() * maxResults;
        }

        StringBuilder select = new StringBuilder();
        StringBuilder join = new StringBuilder();
        StringBuilder where = new StringBuilder();
        Map<String, Object> params = new HashMap<>();

        select.append(" select obj.codigoCliente, obj.matricula, obj.nomeCliente, obj.situacao, obj.saldoCreditoTreino, obj.dataVigenciaAte, obj.telefonesCliente,  ");
        select.append(" empresa.nome as nomeEmpresa ");
        select.append(" from SituacaoClienteSinteticodw obj ");
        join.append(" inner join obj.empresa empresa");
        where.append(" where 1=1");
        where.append(" and obj.saldoCreditoTreino >= 0");
        where.append(" and (obj.saldoCreditoTreino >= 0 AND obj.saldoCreditoTreino <= 999)");
        where.append(" and (obj.situacao = 'AT' or obj.situacao = 'IN' or obj.situacao = 'VI')");

        if (filtros != null) {
            where.append(" and empresa.codigo = :empresa ");
            params.put("empresa", filtros.getEmpresa());
            where.append(" and obj.saldoCreditoTreino >= ").append(filtros.getValorInicialCreditos());
            where.append(" and obj.saldoCreditoTreino <=").append(filtros.getValorFinalCreditos());
            where.append(" and upper(obj.nomeCliente) like CONCAT ('%',:nome, '%')\n");
                params.put("nome", filtros.getQuickSearchValue());

            if (filtros.getQuickSearchValue().matches("\\d+")){
                where.append(" OR obj.matricula =:codigo\n");
                where.append(" OR obj.saldoCreditoTreino =:codigo\n");
                params.put("codigo", new Integer(filtros.getQuickSearchValue()));
            }

            if (filtros.isAtivo() & filtros.isInativo()){
                where.append(" and obj.situacao = 'AT' or obj.situacao = 'IN'");
            } else if (filtros.isAtivo()) {
                where.append(" and obj.situacao = 'AT' ");
            } else if(filtros.isInativo()) {
                where.append(" and obj.situacao = 'IN'");
            }
            }

        if (paginadorDTO != null) {
            paginadorDTO.setQuantidadeTotalElementos(countWithParamClass("codigo", "SituacaoClienteSinteticodw", join, where, params).longValue());
            if(paginadorDTO.getSort() != null) {
                if (paginadorDTO.getSort().contains("matricula")) {
                    where.append(" ORDER BY obj.matricula " + paginadorDTO.getSort().split(",")[1]);
                } else if (paginadorDTO.getSort().toLowerCase().contains("nome")) {
                    where.append(" ORDER BY obj.nomeCliente " + paginadorDTO.getSort().split(",")[1]);
                } else if (paginadorDTO.getSort().contains("saldo")) {
                    where.append(" ORDER BY obj.saldoCreditoTreino " + paginadorDTO.getSort().split(",")[1]);
                } else if (paginadorDTO.getSort().toLowerCase().contains("data")) {
                    where.append(" ORDER BY obj.dataVigenciaAte " + paginadorDTO.getSort().split(",")[1]);
                } else if (paginadorDTO.getSort().toLowerCase().contains("situacao")) {
                    where.append(" ORDER BY obj.situacao " + paginadorDTO.getSort().split(",")[1]);
                }else if (paginadorDTO.getSort().toLowerCase().contains("telefone")) {
                    where.append(" ORDER BY obj.telefonesCliente " + paginadorDTO.getSort().split(",")[1]);
                }
                else if (paginadorDTO.getSort().toLowerCase().contains("nomeempresa")) {
                    where.append(" ORDER BY empresa.nome " + paginadorDTO.getSort().split(",")[1]);
                }

            }

            paginadorDTO.setSize((long) maxResults);
            paginadorDTO.setPage((long) indiceInicial);
        }

        select.append(join);
        select.append(where);

         Query query = getCurrentSession().createQuery(select.toString());

        if (params != null) {
            for (String p : params.keySet()) {
                query.setParameter(p, params.get(p));
            }
        }

        if (maxResults > 0)
            query.setMaxResults(maxResults);
        if (indiceInicial != 0)
            query.setFirstResult(indiceInicial);

        List<Object[]> result = query.getResultList();
        List<RelatorioSaldoCreditoDTO> relatorioSaldoCreditoDTOS = new ArrayList<>();

        for (Object[] line : result) {
            String codigoCliente = line[0].toString();
            String matricula = line[1].toString();
            String nome = line[2].toString();
            String situacao = line[3].toString();
            String saldoCreditos = line[4].toString();
            String dataVigenciaAte =  Uteis.getData((Date) line[5], "br");
            String telefone = line[6].toString();
            String empresa = line[7].toString();


            RelatorioSaldoCreditoDTO relSaldoCreditoDTO = new RelatorioSaldoCreditoDTO();

            relSaldoCreditoDTO.setCodigoCliente(codigoCliente);
            relSaldoCreditoDTO.setMatricula(matricula);
            relSaldoCreditoDTO.setNome(nome);
            relSaldoCreditoDTO.setSituacao(situacao);
            relSaldoCreditoDTO.setSaldoCreditos(saldoCreditos);
            relSaldoCreditoDTO.setDataVigenciaAte(dataVigenciaAte);
            relSaldoCreditoDTO.setTelefone(telefone);
            relSaldoCreditoDTO.setEmpresa(empresa);

            relatorioSaldoCreditoDTOS.add(relSaldoCreditoDTO);
        }
        return relatorioSaldoCreditoDTOS;
    }

}
