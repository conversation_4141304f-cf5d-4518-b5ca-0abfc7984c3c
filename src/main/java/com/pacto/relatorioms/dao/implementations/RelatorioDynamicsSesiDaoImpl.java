package com.pacto.relatorioms.dao.implementations;

import com.pacto.config.utils.Uteis;
import com.pacto.relatorioms.dao.interfaces.RelatorioDynamicsSesiDao;
import com.pacto.relatorioms.filter.FiltroRelatorioDynamicsSesiJSON;
import com.pacto.relatorioms.filter.RelatorioDynamicsSesiFilter;
import org.hibernate.query.Query;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Repository
public class RelatorioDynamicsSesiDaoImpl extends DaoGenericoImpl<RelatorioDynamicsSesiFilter, Integer> implements RelatorioDynamicsSesiDao {

    @Override
    public List<String> gerarLinhasDoArquivoTexto(FiltroRelatorioDynamicsSesiJSON filtros, RelatorioDynamicsSesiFilter relatorioDynamicsSesiFilter) throws Exception {
        getCurrentSession().clear();

        StringBuilder select = new StringBuilder();
        Map<String, Object> params = new HashMap<>();

        select.append("with ");
        select.append("recebiveis as (select concat(cast(codexternounidadesesi as varchar), ' \\t ') as CodigoEmpresa,");
        select.append("		  		      concat(cast(codunidadeorganizacionalsesi as varchar), ' \\t ') as CodigoUnidadeOrganizacionalSesi,");
        select.append("		              concat(cast(coalesce(crsesi, 0) as varchar), ' \\t ') as CodigoCentroResponsabilidade,");
        select.append("		              codigocontacontabilsesi as ContaContabil,");
        select.append("		              concat(to_char(coalesce(sum(valorpago), 0), 'FM99999999990.00'), ' \\t ') as ValorLancamento,");
        select.append("		              concat(to_char(case");
        select.append("                     when (tipoformapagamento in ('AV','CD','CC','BB','CO','TB','PX')) then datapagamento");
        select.append("                     when (tipoformapagamento in ('CH')) then datacompensacaocheque");
        select.append("                     when (tipoformapagamento in ('CA')) then datacompensacaocartao");
        select.append("                   end, 'DD/MM/YYYY'), ' \\t ') as DataCompensacao,");
        select.append("		              concat('999', ' \\t ') as CodigoNaoIdentificado,");
        select.append("		              concat(to_char(case");
        select.append("                     when (tipoformapagamento in ('AV','CD','CC','BB','CO','TB','PX')) then datapagamento");
        select.append("                     when (tipoformapagamento in ('CH')) then datacompensacaocheque");
        select.append("                     when (tipoformapagamento in ('CA')) then datacompensacaocartao");
        select.append("                   end, 'MMYYYY'), ' \\t ') as CodigoHistorico,");
        select.append("		              concat(string_agg(distinct descricao, ' - ' order by descricao), ' \\t ') as DescricaoHistorico");
        select.append("              from (select e.codexternounidadesesi, e.codunidadeorganizacionalsesi, pr.crsesi, pr.codigocontacontabilsesi,");
        select.append("                           fp.tipoformapagamento, mp.datapagamento, ch.datacompesancao as datacompensacaocheque,");
        select.append("                           cc.datacompesancao as datacompensacaocartao, pr.descricao,");
        select.append("                           sum(round(cast(mpp.valorpago / (select sum(mpps.valorpago) from movprodutoparcela mpps where mpps.recibopagamento = mp.recibopagamento) * case ");
        select.append("                                 when (fp.tipoformapagamento in ('CA')) then cc.valortotal");
        select.append("                                 else mp.valortotal");
        select.append("                               end as numeric), 4)) as valorpago");
        select.append("		                 from movpagamento mp");
        select.append("		                 join formapagamento fp on (fp.codigo = mp.formapagamento)");
        select.append("		                 join empresa e on (e.codigo = mp.empresa)");
        select.append("		                 join movprodutoparcela mpp on (mpp.recibopagamento = mp.recibopagamento)");
        select.append("		                 join movproduto mpr on (mpr.codigo = mpp.movproduto)");
        select.append("		                 join produto pr on (pr.codigo = mpr.produto)");
        select.append("                      left join cheque ch on (mp.codigo = ch.movpagamento and ch.situacao not in ('CA', 'DV'))");
        select.append("                      left join cartaocredito cc on (mp.codigo = cc.movpagamento and cc.situacao not in ('CA'))");
        select.append("		                where (mp.recibopagamento is not null or mp.credito = 't') and (mp.valortotal > 0 or fp.tipoformapagamento = 'CC')");

        if (filtros != null) {
            select.append("                    and mp.empresa = :empresa");
            params.put("empresa", filtros.getEmpresa());

            if(filtros.getPeriodoPesquisa() != null) {
                select.append("		                  and (   (    to_char(mp.datapagamento, 'MM/YYYY') = :periodoPesquisa");
                select.append("                                and fp.tipoformapagamento in ('AV','CD','CC','BB','CO','TB','PX'))");
                select.append("                            or (    to_char(ch.datacompesancao, 'MM/YYYY') = :periodoPesquisa");
                select.append("                                and fp.tipoformapagamento in ('CH'))");
                select.append("                            or (    to_char(cc.datacompesancao, 'MM/YYYY') = :periodoPesquisa");
                select.append("                                and fp.tipoformapagamento in ('CA')))");

                params.put("periodoPesquisa", Uteis.getMesReferenciaData(filtros.getPeriodoPesquisa()));
            }
        }

        select.append("		                group by e.codexternounidadesesi, e.codunidadeorganizacionalsesi, pr.crsesi, pr.codigocontacontabilsesi,");
        select.append("		                         fp.tipoformapagamento, mp.datapagamento, ch.datacompesancao,");
        select.append("                              cc.datacompesancao, pr.descricao, mp.codigo) sub");
        select.append("		                where valorpago > 0");
        select.append("              group by codexternounidadesesi, codunidadeorganizacionalsesi, coalesce(crsesi, 0), codigocontacontabilsesi,");
        select.append("		                  to_char(case when (tipoformapagamento in ('AV','CD','CC','BB','CO','TB','PX')) then datapagamento");
        select.append("                                 when (tipoformapagamento in ('CH')) then datacompensacaocheque");
        select.append("                                 when (tipoformapagamento in ('CA')) then datacompensacaocartao");
        select.append("                               end, 'DD/MM/YYYY'),");
        select.append("		                  to_char(case when (tipoformapagamento in ('AV','CD','CC','BB','CO','TB','PX')) then datapagamento");
        select.append("                                 when (tipoformapagamento in ('CH')) then datacompensacaocheque");
        select.append("                                 when (tipoformapagamento in ('CA')) then datacompensacaocartao");
        select.append("                               end, 'MMYYYY'))");
        select.append("select concat(CodigoEmpresa, CodigoUnidadeOrganizacionalSesi, CodigoCentroResponsabilidade, CodigoContaContabil,");
        select.append("			  TipoLancamento, ValorLancamento, DataCompensacao, CodigoEmpresa, CodigoNaoIdentificado,");
        select.append("			  CodigoHistorico, DescricaoHistorico) as linha");
        select.append("  from (select rc.*,");
        select.append("               concat('C', ' \\t ') as TipoLancamento,");
        select.append("               concat(rc.ContaContabil, ' \\t ') as CodigoContaContabil");
        select.append("          from recebiveis rc");
        select.append("        union all");
        select.append("        select rd.*,");
        select.append("               concat('D', ' \\t ') as TipoLancamento,");
        select.append("               concat('11030101001', ' \\t ') as CodigoContaContabil");
        select.append("          from recebiveis rd) tm");
        select.append(" group by concat(CodigoEmpresa, CodigoUnidadeOrganizacionalSesi, CodigoCentroResponsabilidade, CodigoContaContabil,");
        select.append("                 TipoLancamento, ValorLancamento, DataCompensacao, CodigoEmpresa, CodigoNaoIdentificado,");
        select.append("                 CodigoHistorico, DescricaoHistorico),");
        select.append("          CodigoEmpresa, CodigoUnidadeOrganizacionalSesi, CodigoCentroResponsabilidade, DataCompensacao, DescricaoHistorico, TipoLancamento");
        select.append(" order by CodigoEmpresa, CodigoUnidadeOrganizacionalSesi, CodigoCentroResponsabilidade, DataCompensacao, DescricaoHistorico, TipoLancamento");

        Query query = getCurrentSession().createNativeQuery(select.toString());

        if (params != null) {
            for (String p : params.keySet()) {
                query.setParameter(p, params.get(p));
            }
        }

        List<String> relatorioDTOS = query.getResultList();

        return relatorioDTOS;
    }

    @Override
    public List<String> validarSeProdutoNaoContemCentroResponsabilidade(FiltroRelatorioDynamicsSesiJSON filtros,
                                                                        RelatorioDynamicsSesiFilter relatorioDynamicsSesiFilter) throws Exception {
        getCurrentSession().clear();

        List<String> validacaoDTOs = new ArrayList<>();
        StringBuilder select = new StringBuilder();
        Map<String, Object> params = new HashMap<>();

        select.append("select distinct(concat('O produto ', pr.codigo, ' - ', pr.descricao, ' deve preencher o campo CR Sesi')) as validacao");
        select.append("  from movpagamento mp");
        select.append("  join formapagamento fp on (fp.codigo = mp.formapagamento)");
        select.append("	 join empresa e on (e.codigo = mp.empresa)");
        select.append("	 join movprodutoparcela mpp on (mpp.recibopagamento = mp.recibopagamento)");
        select.append("	 join movproduto mpr on (mpr.codigo = mpp.movproduto)");
        select.append("	 join produto pr on (pr.codigo = mpr.produto)");
        select.append("  left join cheque ch on (mp.codigo = ch.movpagamento and ch.situacao not in ('CA', 'DV'))");
        select.append("  left join cartaocredito cc on (mp.codigo = cc.movpagamento and cc.situacao not in ('CA'))");
        select.append(" where (pr.crsesi is null or pr.crsesi = 0)");
        select.append("   and (mp.recibopagamento is not null or mp.credito = 't') and (mp.valor > 0 or fp.tipoformapagamento = 'CC')");

        if (filtros != null) {
            select.append("   and mp.empresa = :empresa");
            params.put("empresa", filtros.getEmpresa());

            if(filtros.getPeriodoPesquisa() != null) {
                select.append("   and (   (    to_char(mp.datapagamento, 'MM/YYYY') = :periodoPesquisa");
                select.append("            and fp.tipoformapagamento in ('AV','CD','CC','BB','CO','TB','PX'))");
                select.append("        or (    to_char(ch.datacompesancao, 'MM/YYYY') = :periodoPesquisa");
                select.append("            and fp.tipoformapagamento in ('CH'))");
                select.append("        or (    to_char(cc.datacompesancao, 'MM/YYYY') = :periodoPesquisa");
                select.append("            and fp.tipoformapagamento in ('CA')))");

                params.put("periodoPesquisa", Uteis.getMesReferenciaData(filtros.getPeriodoPesquisa()));
            }
        }

        Query query = getCurrentSession().createNativeQuery(select.toString());

        if (params != null) {
            for (String p : params.keySet()) {
                query.setParameter(p, params.get(p));
            }
        }

        if (query.getResultList() != null) {
            validacaoDTOs = query.getResultList();
        }

        return validacaoDTOs;
    }
}
