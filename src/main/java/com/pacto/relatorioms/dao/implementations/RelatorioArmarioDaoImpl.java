package com.pacto.relatorioms.dao.implementations;

import com.pacto.config.utils.UteisValidacao;
import com.pacto.relatorioms.dao.interfaces.RelatorioArmarioDao;
import com.pacto.config.dto.PaginadorDTO;
import com.pacto.relatorioms.entities.AluguelArmario;
import com.pacto.relatorioms.filter.RelatorioArmarioFiltroJSON;
import com.pacto.config.utils.Uteis;
import org.hibernate.query.Query;
import org.springframework.stereotype.Repository;

import java.sql.Connection;
import java.util.*;

@Repository
public class RelatorioArmarioDaoImpl extends DaoGenericoImpl<AluguelArmario, Integer> implements RelatorioArmarioDao {
    private static final int MAXIMO_RESULTADOS = 10;
    protected transient Connection con = null;
    private String parametro;

    @Override
    public List<Object[]> consultarArmarios(RelatorioArmarioFiltroJSON filtros, PaginadorDTO paginadorDTO) throws Exception {
        getCurrentSession().clear();

        int maxResults = MAXIMO_RESULTADOS;
        int indiceInicial = 0;

        if (paginadorDTO != null) {
            maxResults = paginadorDTO.getSize() == null ? MAXIMO_RESULTADOS : paginadorDTO.getSize().intValue();
            indiceInicial = paginadorDTO.getPage() == null ? indiceInicial : paginadorDTO.getPage().intValue() * maxResults;
        }

        StringBuilder where = new StringBuilder();
        StringBuilder select = new StringBuilder();
        StringBuilder count = new StringBuilder("SELECT count(*)\n");
        Map<String, Object> params = new HashMap<>();

        select.append("SELECT obj, contrato ");
        where.append("FROM AluguelArmario obj ");
        where.append("INNER JOIN obj.cliente cliente\n");
        where.append("INNER JOIN cliente.empresa empresa\n");
        where.append("INNER JOIN cliente.pessoa pessoa\n");
        where.append("LEFT JOIN pessoa.emails email\n");
        where.append("LEFT JOIN pessoa.telefones telefone\n");
        where.append("INNER JOIN obj.armario armario\n");
        where.append("INNER JOIN armario.tamanhoArmario tamanhoArmario\n");
        where.append("INNER JOIN obj.movproduto movproduto\n");
        where.append("INNER JOIN movproduto.produto produto\n");
        where.append("LEFT JOIN Contrato contrato ON contrato.codigo = (select situ.codigoContrato from SituacaoClienteSinteticodw situ WHERE situ.codigoPessoa = pessoa.codigo)\n");
        where.append("LEFT JOIN contrato.plano plano\n");
        if (filtros.getSomenteParcelasAtrasadas()) {
            where.append("INNER JOIN MovParcela parcela ON parcela.vendaavulsa = obj.vendaavulsa\n");
        }
        where.append(" WHERE 1=1");
        if (filtros.getPeriodoLocacaoDe() != null) {
            where.append(" AND obj.dataInicio >= '").append(Uteis.getData(filtros.getPeriodoLocacaoDe(), "bd")).append(" 00:00:00' ");
        }
        if (filtros.getPeriodoLocacaoAte() != null) {
            where.append(" AND obj.dataInicio <= '").append(Uteis.getData(filtros.getPeriodoLocacaoAte(), "bd")).append(" 23:59:59' ");
        }

        if (filtros.getPeriodoLocacaoDe() != null) {
            where.append(" AND obj.dataCadastro >= '").append(Uteis.getData(filtros.getPeriodoLocacaoDe(), "bd")).append(" 00:00:00' ");
        }
        if (filtros.getPeriodoLocacaoAte() != null) {
            where.append(" AND obj.dataCadastro <= '").append(Uteis.getData(filtros.getPeriodoLocacaoAte(), "bd")).append(" 23:59:59' ");
        }

        if (filtros.getPeriodoRenovacaoDe() != null) {
            where.append(" AND (obj.dataCadastro >= '").append(Uteis.getData(filtros.getPeriodoRenovacaoDe(), "bd")).append(" 00:00:00' ");
            where.append(" or obj.relacionamentoRenovacao != 0)\n");
        }
        if (filtros.getPeriodoRenovacaoAte() != null) {
            where.append(" AND (obj.dataCadastro <= '").append(Uteis.getData(filtros.getPeriodoRenovacaoAte(), "bd")).append(" 23:59:59' ");
            where.append(" or obj.relacionamentoRenovacao != 0)\n");
        }

        if (filtros.getPeriodoVencimentoDe() != null) {
            where.append(" AND movproduto.dataFinalVigencia >= '").append(Uteis.getData(filtros.getPeriodoVencimentoDe(), "bd")).append(" 00:00:00' ");
        }
        if (filtros.getPeriodoVencimentoAte() != null) {
            where.append(" AND movproduto.dataFinalVigencia <= '").append(Uteis.getData(filtros.getPeriodoVencimentoAte(), "bd")).append(" 23:59:59' ");
        }

        if (!UteisValidacao.emptyNumber(filtros.getPlanoLocacao()) && filtros.getPlanoLocacao() > 0) {
            where.append(" AND produto.codigo = ").append(filtros.getPlanoLocacao()).append("\n");
        }
        if (!UteisValidacao.emptyString(filtros.getTipoArmarioSelecionado()) && !filtros.getTipoArmarioSelecionado().equals("A")) {
            where.append(" AND armario.grupo = '").append(filtros.getTipoArmarioSelecionado()).append("' \n");
        }
        if (!UteisValidacao.emptyNumber(filtros.getTamanhoArmarioSelecionado()) && filtros.getTamanhoArmarioSelecionado() > 0) {
            where.append(" AND armario.tamanhoArmario.codigo = ").append(filtros.getTamanhoArmarioSelecionado()).append("\n");
        }
        if (!UteisValidacao.emptyString(filtros.getNumeroArmario())) {
            where.append(" AND armario.descricao = '").append(filtros.getNumeroArmario()).append("'\n");
        }
        if (filtros.getContratoAssinado() != null) {
            where.append(" AND obj.contratoAssinado = ").append(filtros.getContratoAssinado()).append("\n");
        }
        if (filtros.getSomenteParcelasAtrasadas() != null && filtros.getSomenteParcelasAtrasadas()) {
            where.append(" AND (parcela.situacao = 'EA' AND parcela.datavencimento > now())\n");
        }

        where.append(" AND ( email.codigo is null OR email.codigo = (SELECT MAX(em.codigo) FROM email em WHERE em.pessoa = pessoa.codigo))\n");
        where.append(" AND ( telefone.codigo is null OR telefone.codigo = (SELECT MAX(tel.codigo) FROM telefone tel WHERE tel.pessoa = pessoa.codigo))\n");

        count.append(where);
        if (paginadorDTO != null) {
            paginadorDTO.setQuantidadeTotalElementos(maxResult(count.toString()));
            where.append((paginadorDTO.getSQLOrderByUse()));
            paginadorDTO.setSize((long) maxResults);
            paginadorDTO.setPage((long) indiceInicial);
        }

        select.append(where);
        Query query = getCurrentSession().createQuery(select.toString());

        if (params != null) {
            for (String p : params.keySet()) {
                query.setParameter(p, params.get(p));
            }
        }

        if (maxResults != 0)
            query.setMaxResults(maxResults);
        if (indiceInicial != 0)
            query.setFirstResult(indiceInicial);

        List<Object[]> result = query.getResultList();

        return result;
    }

    public Long maxResult(String count) {
        return (Long) getCurrentSession().createQuery(count).getSingleResult();
    }

}
