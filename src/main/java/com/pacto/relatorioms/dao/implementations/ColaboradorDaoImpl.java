package com.pacto.relatorioms.dao.implementations;

import com.pacto.relatorioms.dao.interfaces.ColaboradorDao;
import com.pacto.config.dto.PaginadorDTO;
import com.pacto.relatorioms.entities.Colaborador;
import com.pacto.relatorioms.filter.FiltroColaboradorJSON;
import com.pacto.relatorioms.filter.FiltroRelatorioPersonalJSON;
import com.pacto.config.utils.Uteis;
import org.hibernate.query.Query;
import org.springframework.stereotype.Repository;

import java.util.*;

@Repository
public class ColaboradorDaoImpl extends DaoGenericoImpl<Colaborador, Integer> implements ColaboradorDao {

    private static final int MAXIMO_RESULTADOS = 16;

    @Override
    public List<Colaborador> findAll(FiltroColaboradorJSON filtros, PaginadorDTO paginadorDTO) throws Exception {
        getCurrentSession().clear();

        int maxResults = MAXIMO_RESULTADOS;
        int indiceInicial = 0;

        if (paginadorDTO != null) {
            maxResults = paginadorDTO.getSize() == null ? MAXIMO_RESULTADOS : paginadorDTO.getSize().intValue();
            indiceInicial = paginadorDTO.getPage() == null ? indiceInicial : paginadorDTO.getPage().intValue() * maxResults;
        }

        StringBuilder hql = new StringBuilder();
        StringBuilder join = new StringBuilder();
        StringBuilder where = new StringBuilder();
        Map<String, Object> params = new HashMap<>();

        try {
            hql.append("SELECT distinct obj  FROM Colaborador obj");
            join.append(" LEFT JOIN Vinculo v ON v.colaborador.codigo = obj.codigo ");
            join.append(" LEFT JOIN TipoColaborador tc ON tc.colaborador.codigo = obj.codigo ");
            where.append(" WHERE 1=1");
            if (filtros.getTipoColaborador().size() > 0) {
                where.append(" AND (tc.descricao in (:tipo) OR v.tipoVinculo in (:tipo))");
                params.put("tipo", filtros.getTipoColaborador());
            }
            where.append(" AND obj.situacao = 'AT'");
            if (filtros.getEmpresa() > 0) {
                where.append(" AND obj.empresa.codigo = :empresaid ");
                params.put("empresaid", filtros.getEmpresa());
            }
            if (!filtros.getParametroBusca().isEmpty()) {
                where.append(" AND lower(obj.pessoa.nome) like :busca ");
                params.put("busca", "%" + filtros.getParametroBusca().toLowerCase(Locale.ROOT) + "%");
            }
            if (paginadorDTO != null) {
                paginadorDTO.setQuantidadeTotalElementos(countWithParam("codigo", join, where, params).longValue());
                paginadorDTO.setSize((long) maxResults);
                paginadorDTO.setPage((long) indiceInicial);
            }

            hql.append(join);
            hql.append(where);
            Query query = getCurrentSession().createQuery(hql.toString());
            if (params != null) {
                for (String p : params.keySet()) {
                    query.setParameter(p, params.get(p));
                }
            }

            if (maxResults > 0) {
                query.setMaxResults(maxResults);
            }
            if (indiceInicial != 0) {
                query.setFirstResult(indiceInicial);
            }

            return query.getResultList();
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        return null;
    }


    @Override
    public List<Object[]> consultarRelatorioPersonal(FiltroRelatorioPersonalJSON filtros, PaginadorDTO paginadorDTO) throws Exception {
        getCurrentSession().clear();

        int maxResults = MAXIMO_RESULTADOS;
        int indiceInicial = 0;

        if (paginadorDTO != null) {
            maxResults = paginadorDTO.getSize() == null ? MAXIMO_RESULTADOS : paginadorDTO.getSize().intValue();
            indiceInicial = paginadorDTO.getPage() == null ? indiceInicial : paginadorDTO.getPage().intValue() * maxResults;
        }

        StringBuilder hql = new StringBuilder();
        StringBuilder hqlWhere = new StringBuilder();
        Map<String, Object> params = new HashMap<>();

        try {
            hql.append("SELECT DISTINCT obj, hv.cliente, itp, obj.pessoa, hv.cliente.pessoa ");
            hql.append(" FROM Colaborador obj ");
            hqlWhere.append(" INNER JOIN HistoricoVinculo hv ON hv.colaborador.codigo = obj.codigo ");
            hqlWhere.append(" LEFT JOIN ItemTaxaPersonal itp ON itp.codigo = ");
            hqlWhere.append(" (SELECT max(itp2.codigo) FROM ItemTaxaPersonal itp2");
            hqlWhere.append(" INNER JOIN itp2.movProduto mpro ");
            hqlWhere.append(" WHERE itp2.controle.personal.codigo = obj.codigo ");
            hqlWhere.append(" AND obj.codigo IN (SELECT tp.colaborador FROM TipoColaborador tp WHERE tp.descricao in ('PT', 'PI', 'PE')) ");
            hqlWhere.append(" AND itp2.aluno.codigo = hv.cliente.codigo ");
            hqlWhere.append(" AND mpro.mesReferencia = '").append(filtros.getMesReferenciaStr()).append("' ");
            hqlWhere.append(" GROUP BY itp2.controle.personal.codigo, itp2.aluno.codigo) ");
            hqlWhere.append(" LEFT JOIN MovProduto mpro ON mpro.codigo = itp.movProduto.codigo ");
            hqlWhere.append(" LEFT JOIN Desconto d ON d.codigo = itp.desconto.codigo ");
            hqlWhere.append(" LEFT JOIN Produto pro ON pro.codigo = mpro.produto.codigo ");
            hqlWhere.append(" WHERE 1 = 1 ");
            hqlWhere.append(" AND hv.tipoColaborador IN ('PT','PI','PE') ");
            if (filtros.getEmpresa() > 0) {
                hqlWhere.append(" AND obj.empresa.codigo = :empresaid ");
                params.put("empresaid", filtros.getEmpresa());
            }
            // filtrar vinculos do personal com os alunos em determinado mês
            if(filtros.getMesReferencia() != null) {
                hqlWhere.append(" AND (hv.dataRegistro <= '").append(Uteis.getData(filtros.getMesReferencia(), "bd")).append(" 23:59:59' ");
                hqlWhere.append(" AND hv.tipoHistoricoVinculo = 'EN' ) ");
            }
            // Filtros negociado, pago, vencido e livre
            if(filtros.filtraPorSituacao()) {
                // Filtrar pelas situações desmarcadas, se estiver desmarcada, então as taxas com essa situação não aparecerão na consulta
                if (!filtros.getNegociado()) {
                    hqlWhere.append(" AND mpro.codigo NOT IN (SELECT mpp.movProduto.codigo FROM MovProdutoParcela mpp WHERE mpp.movProduto.codigo = mpro.codigo AND mpp.movProduto.situacao = 'EA' AND mpp.movParcela.dataVencimento >= CURRENT_DATE) ");
                }
                if (!filtros.getVencido()) {
                    hqlWhere.append(" AND mpro.codigo NOT IN (SELECT mpp.movProduto.codigo FROM MovProdutoParcela mpp WHERE mpp.movProduto.codigo = mpro.codigo AND mpp.movProduto.situacao = 'EA' AND mpp.movParcela.dataVencimento < CURRENT_DATE) ");
                }
                if (!filtros.getPago()) {
                    hqlWhere.append(" AND mpro.codigo NOT IN (SELECT mpro2.codigo FROM MovProduto mpro2 WHERE mpro2.codigo = mpro.codigo AND mpro2.situacao = 'PG') ");
                }
                if (!filtros.getLivre()) {
                    hqlWhere.append(" AND itp.movProduto.codigo > 0 ");
                }
            }

            hqlWhere.append(" AND hv.cliente.codigo NOT IN (SELECT hv2.cliente.codigo FROM HistoricoVinculo hv2 WHERE hv2.colaborador.codigo = hv.colaborador.codigo AND hv2.tipoHistoricoVinculo = 'SD' AND hv2.codigo > hv.codigo and hv2.tipoColaborador = hv.tipoColaborador) ");

            if(filtros.getPersonais().size() > 0) {
                hqlWhere.append(" AND obj.codigo IN (:personais) ");
                params.put("personais", filtros.getPersonais());
            }

            if (paginadorDTO != null) {
                paginadorDTO.setQuantidadeTotalElementos(countWithParam("obj.codigo", hqlWhere, params).longValue());
                paginadorDTO.setSize((long) maxResults);
                paginadorDTO.setPage((long) indiceInicial);
                if(paginadorDTO.getSort() != null) {
                    String sortColumn = paginadorDTO.getSort().split(",")[0].toLowerCase();
                    String sortDirection = paginadorDTO.getSort().split(",")[1];
                    if (sortColumn.equals("personal")) {
                        hqlWhere.append(" ORDER BY obj.pessoa.nome " + sortDirection);
                    } else if (sortColumn.equals("aluno")) {
                        hqlWhere.append(" ORDER BY hv.cliente.pessoa.nome " + sortDirection);
                    } else if (sortColumn.equals("produto")) {
                        hqlWhere.append(" ORDER BY pro.descricao " + sortDirection);
                    } else if (sortColumn.equals("valor")) {
                        hqlWhere.append(" ORDER BY pro.valorFinal " + sortDirection);
                    } else if (sortColumn.equals("desconto")) {
                        hqlWhere.append(" ORDER BY mpro.valorDesconto " + sortDirection);
                    } else if (sortColumn.equals("valorfinal")) {
                        hqlWhere.append(" ORDER BY mpro.totalFinal " + sortDirection);
                    }
                } else {
                    hqlWhere.append(" ORDER BY obj.pessoa.nome ASC ");
                }
            }

            hql.append(hqlWhere);
            Query query = getCurrentSession().createQuery(hql.toString());
            if (params != null) {
                for (String p : params.keySet()) {
                    query.setParameter(p, params.get(p));
                }
            }

            if (maxResults > 0) {
                query.setMaxResults(maxResults);
            }
            if (indiceInicial != 0) {
                query.setFirstResult(indiceInicial);
            }

            return query.getResultList();
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        return null;
    }

    @Override
    public Double consultarTotalLancado(FiltroRelatorioPersonalJSON filtros) {
        getCurrentSession().clear();

        StringBuilder hql = new StringBuilder();
        StringBuilder hqlWhere = new StringBuilder();
        Map<String, Object> params = new HashMap<>();

        try {
            hql.append("SELECT sum(COALESCE(mpro.totalFinal,0)) ");
            hql.append(" FROM Colaborador obj ");
            hqlWhere.append(" INNER JOIN HistoricoVinculo hv ON hv.colaborador.codigo = obj.codigo ");
            hqlWhere.append(" LEFT JOIN ItemTaxaPersonal itp ON itp.codigo = ");
            hqlWhere.append(" (SELECT max(itp2.codigo) FROM ItemTaxaPersonal itp2");
            hqlWhere.append(" INNER JOIN itp2.movProduto mpro ");
            hqlWhere.append(" WHERE itp2.controle.personal.codigo = obj.codigo ");
            hqlWhere.append(" AND obj.codigo IN (SELECT tp.colaborador FROM TipoColaborador tp WHERE tp.descricao in ('PT', 'PI', 'PE')) ");
            hqlWhere.append(" AND itp2.aluno.codigo = hv.cliente.codigo ");
            hqlWhere.append(" AND mpro.mesReferencia = '").append(filtros.getMesReferenciaStr()).append("' ");
            hqlWhere.append(" GROUP BY itp2.controle.personal.codigo, itp2.aluno.codigo) ");
            hqlWhere.append(" LEFT JOIN MovProduto mpro ON mpro.codigo = itp.movProduto.codigo ");
            hqlWhere.append(" LEFT JOIN Desconto d ON d.codigo = itp.desconto.codigo ");
            hqlWhere.append(" LEFT JOIN Produto pro ON pro.codigo = mpro.produto.codigo ");
            hqlWhere.append(" WHERE 1 = 1 ");
            hqlWhere.append(" AND hv.tipoColaborador IN ('PT','PI','PE') ");
            if (filtros.getEmpresa() > 0) {
                hqlWhere.append(" AND obj.empresa.codigo = :empresaid ");
                params.put("empresaid", filtros.getEmpresa());
            }
            // filtrar vinculos do personal com os alunos em determinado mês
            if(filtros.getMesReferencia() != null) {
                hqlWhere.append(" AND (hv.dataRegistro <= '").append(Uteis.getData(filtros.getMesReferencia(), "bd")).append(" 23:59:59' ");
                hqlWhere.append(" AND hv.tipoHistoricoVinculo = 'EN' )");
            }
            // Filtros negociado, pago, vencido e livre
            if(filtros.filtraPorSituacao()) {
                // Filtrar pelas situações desmarcadas, se estiver desmarcada, então as taxas com essa situação não aparecerão na consulta
                if (!filtros.getNegociado()) {
                    hqlWhere.append(" AND mpro.codigo NOT IN (SELECT mpp.movProduto.codigo FROM MovProdutoParcela mpp WHERE mpp.movProduto.codigo = mpro.codigo AND mpp.movProduto.situacao = 'EA' AND mpp.movParcela.dataVencimento >= CURRENT_DATE) ");
                }
                if (!filtros.getVencido()) {
                    hqlWhere.append(" AND mpro.codigo NOT IN (SELECT mpp.movProduto.codigo FROM MovProdutoParcela mpp WHERE mpp.movProduto.codigo = mpro.codigo AND mpp.movProduto.situacao = 'EA' AND mpp.movParcela.dataVencimento < CURRENT_DATE) ");
                }
                if (!filtros.getPago()) {
                    hqlWhere.append(" AND mpro.codigo NOT IN (SELECT mpro2.codigo FROM MovProduto mpro2 WHERE mpro2.codigo = mpro.codigo AND mpro2.situacao = 'PG') ");
                }
                if (!filtros.getLivre()) {
                    hqlWhere.append(" AND itp.movProduto.codigo > 0 ");
                }
            }

            // Filtrar alunos que não tiveram historico de saída da carteira do personal
            hqlWhere.append(" AND hv.cliente.codigo NOT IN (");
            hqlWhere.append(" SELECT hv2.cliente.codigo FROM HistoricoVinculo hv2 ");
            hqlWhere.append(" WHERE hv2.colaborador = obj.codigo ");
            hqlWhere.append(" AND hv2.tipoHistoricoVinculo = 'SD' ");
            hqlWhere.append(" AND hv2.codigo > hv.codigo ");
            hqlWhere.append(" AND hv2.tipoColaborador = hv.tipoColaborador ");
            hqlWhere.append(")");
            if(filtros.getPersonais().size() > 0) {
                hqlWhere.append(" AND obj.codigo IN (:personais) ");
                params.put("personais", filtros.getPersonais());
            }

            hql.append(hqlWhere);
            Query query = getCurrentSession().createQuery(hql.toString());
            if (params != null) {
                for (String p : params.keySet()) {
                    query.setParameter(p, params.get(p));
                }
            }

            return (Double) query.getSingleResult();
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        return 0.0;
    }

    @Override
    public Double consultarTotalPago(FiltroRelatorioPersonalJSON filtros) {
        getCurrentSession().clear();

        StringBuilder hql = new StringBuilder();
        StringBuilder hqlWhere = new StringBuilder();
        Map<String, Object> params = new HashMap<>();

        if(filtros.filtraPorSituacao()) {
            if(!filtros.getPago()) {
                return 0.0;
            }
        }

        try {
            hql.append("SELECT sum(COALESCE(mpro.totalFinal,0)) ");
            hql.append(" FROM Colaborador obj ");
            hqlWhere.append(" INNER JOIN HistoricoVinculo hv ON hv.colaborador.codigo = obj.codigo ");
            hqlWhere.append(" INNER JOIN ItemTaxaPersonal itp ON itp.codigo = ");
            hqlWhere.append(" (SELECT max(itp2.codigo) FROM ItemTaxaPersonal itp2");
            hqlWhere.append(" INNER JOIN itp2.movProduto mpro ");
            hqlWhere.append(" WHERE itp2.controle.personal.codigo = obj.codigo ");
            hqlWhere.append(" AND obj.codigo IN (SELECT tp.colaborador FROM TipoColaborador tp WHERE tp.descricao in ('PT', 'PI', 'PE')) ");
            hqlWhere.append(" AND itp2.aluno.codigo = hv.cliente.codigo ");
            hqlWhere.append(" AND mpro.mesReferencia = '").append(filtros.getMesReferenciaStr()).append("' ");
            hqlWhere.append(" GROUP BY itp2.controle.personal.codigo, itp2.aluno.codigo) ");
            hqlWhere.append(" INNER JOIN MovProduto mpro ON mpro.codigo = itp.movProduto.codigo ");
            hqlWhere.append(" LEFT JOIN Desconto d ON d.codigo = itp.desconto.codigo ");
            hqlWhere.append(" INNER JOIN Produto pro ON pro.codigo = mpro.produto.codigo ");
            hqlWhere.append(" WHERE 1 = 1 ");
            hqlWhere.append(" AND mpro.situacao = 'PG' ");
            hqlWhere.append(" AND hv.tipoColaborador IN ('PT','PI','PE') ");
            if (filtros.getEmpresa() > 0) {
                hqlWhere.append(" AND obj.empresa.codigo = :empresaid ");
                params.put("empresaid", filtros.getEmpresa());
            }
            // filtrar vinculos do personal com os alunos em determinado mês
            if(filtros.getMesReferencia() != null) {
                hqlWhere.append(" AND (hv.dataRegistro <= '").append(Uteis.getData(filtros.getMesReferencia(), "bd")).append(" 23:59:59' ");
                hqlWhere.append(" AND hv.tipoHistoricoVinculo = 'EN' )");
            }
            // e que não tiveram historico de saída da carteira do personal
            hqlWhere.append(" AND hv.cliente.codigo NOT IN (");
            hqlWhere.append(" SELECT hv2.cliente.codigo FROM HistoricoVinculo hv2 ");
            hqlWhere.append(" WHERE hv2.colaborador = obj.codigo ");
            hqlWhere.append(" AND hv2.tipoHistoricoVinculo = 'SD' ");
            hqlWhere.append(" AND hv2.codigo > hv.codigo ");
            hqlWhere.append(" AND hv2.tipoColaborador = hv.tipoColaborador ");
            hqlWhere.append(")");
            if(filtros.getPersonais().size() > 0) {
                hqlWhere.append(" AND obj.codigo IN (:personais) ");
                params.put("personais", filtros.getPersonais());
            }

            hql.append(hqlWhere);
            Query query = getCurrentSession().createQuery(hql.toString());
            if (params != null) {
                for (String p : params.keySet()) {
                    query.setParameter(p, params.get(p));
                }
            }

            return (Double) query.getSingleResult();
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        return 0.0;
    }

    @Override
    public List<Colaborador> findAllByEmpresaId(Integer empresaId, FiltroColaboradorJSON filtros) {
        getCurrentSession().clear();

        StringBuilder hql = new StringBuilder();
        StringBuilder where = new StringBuilder();
        Map<String, Object> params = new HashMap<>();

        try {
            hql.append("SELECT distinct obj  FROM Colaborador obj");
            where.append(" WHERE obj.empresa.codigo = :empresaid ");
            params.put("empresaid", empresaId);
            if (!filtros.getParametroBusca().isEmpty()) {
                where.append(" AND lower(obj.pessoa.nome) like :busca ");
                params.put("busca", "%" + filtros.getParametroBusca().toLowerCase(Locale.ROOT) + "%");
            }
            hql.append(where);

            Query query = getCurrentSession().createQuery(hql.toString());
            if (params != null) {
                for (String p : params.keySet()) {
                    query.setParameter(p, params.get(p));
                }
            }

            return query.getResultList();
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        return null;
    }

    @Override
    public List<Colaborador> findAll(FiltroColaboradorJSON filtros) {
        getCurrentSession().clear();

        StringBuilder hql = new StringBuilder();
        StringBuilder where = new StringBuilder();
        StringBuilder join = new StringBuilder();
        Map<String, Object> params = new HashMap<>();

        try {
            hql.append("SELECT distinct obj  FROM Colaborador obj");
            join.append(" left join TipoColaborador tc on tc.colaborador.codigo = obj.codigo ");
            where.append(" WHERE 1 = 1 ");
            if (!filtros.getTipoColaborador().isEmpty()) {
                where.append(" AND upper(tc.descricao) in (:tipocolaborador) ");
                params.put("tipocolaborador", filtros.getTipoColaborador());
            }
            if(filtros.getEmpresa() != null && filtros.getEmpresa() > 0) {
                where.append(" and obj.empresa.codigo = :empresaid ");
                params.put("empresaid", filtros.getEmpresa());
            }
            if (!filtros.getParametroBusca().isEmpty()) {
                where.append(" and lower(obj.pessoa.nome) like :busca ");
                params.put("busca", "%" + filtros.getParametroBusca().toLowerCase(Locale.ROOT) + "%");
            }
            hql.append(join);
            hql.append(where);

            Query query = getCurrentSession().createQuery(hql.toString());
            if (params != null) {
                for (String p : params.keySet()) {
                    query.setParameter(p, params.get(p));
                }
            }

            return query.getResultList();
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        return null;
    }
}
