package com.pacto.relatorioms.dao.implementations;

import com.pacto.relatorioms.dao.interfaces.PeriodoAcessoClienteDao;
import com.pacto.config.dto.PaginadorDTO;
import com.pacto.relatorioms.entities.PeriodoAcessoCliente;
import com.pacto.config.exceptions.ServiceException;
import org.springframework.stereotype.Repository;

import javax.persistence.Query;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;

@Repository
public class PeriodoAcessoClienteDaoImpl extends DaoGenericoImpl<PeriodoAcessoCliente, Integer> implements PeriodoAcessoClienteDao {

    private static final int MAXIMO_RESULTADOS = 5;

    @Override
    public List<PeriodoAcessoCliente> findAllByMatriculaRelGympass(Integer matricula, PaginadorDTO paginadorDTO) throws ServiceException {
        getCurrentSession().clear();

        int maxResults = MAXIMO_RESULTADOS;
        int indiceInicial = 0;

        if (paginadorDTO != null) {
            maxResults = paginadorDTO.getSize() == null ? MAXIMO_RESULTADOS : paginadorDTO.getSize().intValue();
            indiceInicial = paginadorDTO.getPage() == null ? indiceInicial : paginadorDTO.getPage().intValue() * maxResults;
        }

        Map<String, Object> params = new HashMap<>();
        StringBuilder hql = new StringBuilder();
        StringBuilder join = new StringBuilder();
        StringBuilder where = new StringBuilder();

        try{
            hql.append("select obj from PeriodoAcessoCliente obj ");
            join.append(" inner join Cliente cli ON cli.pessoa.codigo = obj.pessoa.codigo ");
            where.append(" where cli.codigoMatricula = :matricula ");
            where.append(" and coalesce(obj.tokenGymPass,'') <> '' ");
            where.append(" and obj.tipoAcesso = 'PL' ");

            params.put("matricula", matricula);

            if (paginadorDTO != null) {
                paginadorDTO.setQuantidadeTotalElementos(countWithParamClass("codigo", "PeriodoAcessoCliente", join, where, params).longValue());
                if(paginadorDTO.getSort() != null) {
                    if (paginadorDTO.getSort().contains("dataInicioAcesso")) {
                        where.append(" ORDER BY obj.dataInicioAcesso " + paginadorDTO.getSort().split(",")[1]);
                    } else if (paginadorDTO.getSort().contains("dataFinalAcesso")) {
                        where.append(" ORDER BY obj.dataFinalAcesso " + paginadorDTO.getSort().split(",")[1]);
                    } else if (paginadorDTO.getSort().contains("descricaoTipoGymPass")) {
                        where.append(" ORDER BY obj.tokenGymPass " + paginadorDTO.getSort().split(",")[1]);
                    }
                } else {
                    where.append(" ORDER BY obj.dataFinalAcesso DESC ");
                }
                paginadorDTO.setSize((long) maxResults);
                paginadorDTO.setPage((long) indiceInicial);
            }

            hql.append(join != null ? join.toString() : "");
            hql.append(where != null ? where.toString() : "");
            Query q = getCurrentSession().createQuery(hql.toString());

            if (params != null) {
                for (String p : params.keySet()) {
                    q.setParameter(p, params.get(p));
                }
            }

            if (maxResults != 0) {
                q.setMaxResults(maxResults);
            }
            if (indiceInicial != 0) {
                q.setFirstResult(indiceInicial);
            }

            return q.getResultList();
        } catch (Exception e) {
            throw new ServiceException(e);
        }
    }

    @Override
    public List<PeriodoAcessoCliente> findAllByMatriculaRelGogood(Integer matricula, PaginadorDTO paginadorDTO) throws ServiceException {
        getCurrentSession().clear();

        int maxResults = MAXIMO_RESULTADOS;
        int indiceInicial = 0;

        if (paginadorDTO != null) {
            maxResults = paginadorDTO.getSize() == null ? MAXIMO_RESULTADOS : paginadorDTO.getSize().intValue();
            indiceInicial = paginadorDTO.getPage() == null ? indiceInicial : paginadorDTO.getPage().intValue() * maxResults;
        }

        Map<String, Object> params = new HashMap<>();
        StringBuilder hql = new StringBuilder();
        StringBuilder join = new StringBuilder();
        StringBuilder where = new StringBuilder();

        try{
            hql.append("select obj from PeriodoAcessoCliente obj ");
            join.append(" inner join Cliente cli ON cli.pessoa.codigo = obj.pessoa.codigo ");
            where.append(" where cli.codigoMatricula = :matricula ");
            where.append(" and coalesce(obj.tokenGogood,'') <> '' ");
            where.append(" and obj.tipoAcesso = 'PL' ");

            params.put("matricula", matricula);

            if (paginadorDTO != null) {
                paginadorDTO.setQuantidadeTotalElementos(countWithParamClass("codigo", "PeriodoAcessoCliente", join, where, params).longValue());
                if(paginadorDTO.getSort() != null) {
                    if (paginadorDTO.getSort().contains("dataInicioAcesso")) {
                        where.append(" ORDER BY obj.dataInicioAcesso " + paginadorDTO.getSort().split(",")[1]);
                    } else if (paginadorDTO.getSort().contains("dataFinalAcesso")) {
                        where.append(" ORDER BY obj.dataFinalAcesso " + paginadorDTO.getSort().split(",")[1]);
                    } else if (paginadorDTO.getSort().contains("descricaoTipoGogood")) {
                        where.append(" ORDER BY obj.tokenGogood " + paginadorDTO.getSort().split(",")[1]);
                    }
                } else {
                    where.append(" ORDER BY obj.dataFinalAcesso DESC ");
                }
                paginadorDTO.setSize((long) maxResults);
                paginadorDTO.setPage((long) indiceInicial);
            }

            hql.append(join != null ? join.toString() : "");
            hql.append(where != null ? where.toString() : "");
            Query q = getCurrentSession().createQuery(hql.toString());

            if (params != null) {
                for (String p : params.keySet()) {
                    q.setParameter(p, params.get(p));
                }
            }

            if (maxResults != 0) {
                q.setMaxResults(maxResults);
            }
            if (indiceInicial != 0) {
                q.setFirstResult(indiceInicial);
            }

            return q.getResultList();
        } catch (Exception e) {
            throw new ServiceException(e);
        }
    }
}
