package com.pacto.relatorioms.dao.implementations;

import com.pacto.relatorioms.dao.interfaces.RelatorioSMDDao;
import com.pacto.relatorioms.filter.FiltroRelatorioSMDJSON;
import com.pacto.relatorioms.filter.RelatorioSMDFilter;
import com.pacto.config.utils.Uteis;
import org.hibernate.query.Query;
import org.springframework.stereotype.Repository;

import java.util.*;

@Repository
public class RelatorioSMDDaoImpl extends DaoGenericoImpl<RelatorioSMDFilter, Integer> implements RelatorioSMDDao {

    @Override
    public List<String> gerarLinhasDoArquivoTexto(FiltroRelatorioSMDJSON filtros, RelatorioSMDFilter relatorioSMDFilter) throws Exception {
        getCurrentSession().clear();

        StringBuilder select = new StringBuilder();
        StringBuilder join = new StringBuilder();
        StringBuilder where = new StringBuilder();
        Map<String, Object> params = new HashMap<>();

        select.append("select CONCAT(CodUnidadeOperacional, CodProduto, CodFormulario, CodCompetencia,");
        select.append("              CentroResponsabilidade, CodFinanciamento, CodClientela, CodProjeto,");
        select.append("              CodModalidade, CodSerie, CodCargaHoraria, CnpjCeiCliente,");
        select.append("              CodUnidadeControle, MesProducao, QuantidadeProducao, Codigo, CPF,");
        select.append("              Nome, DataNascimento, ResponsavelLegal) as linha ");
        select.append("  from (select LPAD(cast(e.codexternounidadesesi as varchar), 7, '0') as CodUnidadeOperacional,");
        select.append("               LPAD(cast(coalesce(pr.idprodutosmd, 0) as varchar), 7, '0') as CodProduto,");
        select.append("               LPAD(cast(coalesce(pr.codigoformulario, 0) as varchar), 7, '0') as CodFormulario,");
        select.append("               '0002050' as CodCompetencia,");
        select.append("               LPAD(cast(coalesce(pr.crsesi, 0) as varchar), 9, '0') as CentroResponsabilidade,");
        select.append("               '0000000' as CodFinanciamento,");
        select.append("               LPAD(coalesce(ca.codigoclientela, '0'), 7, '0') as CodClientela,");
        select.append("               '0000000' as CodProjeto,");
        select.append("               '0000000' as CodModalidade,");
        select.append("               '0000000' as CodSerie,");
        select.append("               '0000000' as CodCargaHoraria,");
        select.append("               LPAD(coalesce(REGEXP_REPLACE(f.cnpj, '[^0-9]', '', 'g'), '0'), 14, '0') as CnpjCeiCliente,");
        select.append("               '0000102' as CodUnidadeControle,");
        select.append("               CASE EXTRACT(MONTH FROM to_date(m.mesreferencia, 'MM/YYYY'))");
        select.append("                 WHEN 1 THEN 'JAN'");
        select.append("                 WHEN 2 THEN 'FEV'");
        select.append("                 WHEN 3 THEN 'MAR'");
        select.append("                 WHEN 4 THEN 'ABR'");
        select.append("                 WHEN 5 THEN 'MAI'");
        select.append("                 WHEN 6 THEN 'JUN'");
        select.append("                 WHEN 7 THEN 'JUL'");
        select.append("                 WHEN 8 THEN 'AUG'");
        select.append("                 WHEN 9 THEN 'SET'");
        select.append("                 WHEN 10 THEN 'OUT'");
        select.append("                 WHEN 11 THEN 'NOV'");
        select.append("                 WHEN 12 THEN 'DEZ'");
        select.append("               END as MesProducao,");
        select.append("               '0000001' as QuantidadeProducao,");
        select.append("               '000000000' as Codigo,");
        select.append("               LPAD(coalesce(REGEXP_REPLACE(pe.cfp, '[^0-9]', '', 'g'), '0'), 11, '0') as CPF,");
        select.append("               LPAD(coalesce(pe.nome, '0'), 60, '0') as Nome,");
        select.append("               to_char(pe.datanasc, 'DDMMYYYY') as DataNascimento,");
        select.append("               LPAD(coalesce(case ");
        select.append("                              when (pe.nomerespfinanceiro is null or pe.nomerespfinanceiro = '') then coalesce(pe.nome, '0') ");
        select.append("                              else coalesce(pe.nomerespfinanceiro, '0') ");
        select.append("                             end), 60, '0') as ResponsavelLegal ");
        select.append("          from movproduto m");
        join.append("            join empresa e on (e.codigo = m.empresa)");
        join.append("            join produto pr on (pr.codigo = m.produto)");
        join.append("            join pessoa pe on (pe.codigo = m.pessoa)");
        join.append("            join cliente cl on (cl.pessoa = m.pessoa)");
        join.append("            join categoria ca on (ca.codigo = cl.categoria)");
        join.append("            left join fornecedor f on (f.codigo = cl.empresafornecedor)");
        where.append("          where coalesce(pr.exibirrelatoriosmd, false) = true");

        if (filtros != null) {
            where.append("            and m.empresa = :empresa");
            params.put("empresa", filtros.getEmpresa());

            if(filtros.getPeriodoPesquisaInicial() != null) {
                where.append("            and cast((date_trunc('month', to_date(m.mesreferencia, 'MM/YYYY') + interval '1 month') - interval '1 day') as date) >= '")
                    .append(filtros.getPeriodoPesquisaInicial()).append("'");
            }
            if(filtros.getPeriodoPesquisaFinal() != null) {
                where.append("            and cast((date_trunc('month', to_date(m.mesreferencia, 'MM/YYYY') + interval '1 month') - interval '1 day') as date) <= '")
                    .append(Uteis.getData(filtros.getPeriodoPesquisaFinal(), "bd")).append("' ");
            }
        }

        //Fechamento do subselect, após inserir os filtros, e agrupamento de linhas duplicadas
        where.append(") tm");
        where.append(" group by CONCAT(CodUnidadeOperacional, CodProduto, CodFormulario, CodCompetencia,");
        where.append("          CentroResponsabilidade, CodFinanciamento, CodClientela, CodProjeto,");
        where.append("          CodModalidade, CodSerie, CodCargaHoraria, CnpjCeiCliente,");
        where.append("          CodUnidadeControle, MesProducao, QuantidadeProducao, Codigo, CPF,");
        where.append("          Nome, DataNascimento, ResponsavelLegal)");

        select.append(join.toString());
        select.append(where.toString());

        Query query = getCurrentSession().createNativeQuery(select.toString());

        if (params != null) {
            for (String p : params.keySet()) {
                query.setParameter(p, params.get(p));
            }
        }

        List<String> relatorioSMDDTOS = query.getResultList();

        return relatorioSMDDTOS;
    }
}
