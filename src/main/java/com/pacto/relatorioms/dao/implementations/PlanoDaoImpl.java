package com.pacto.relatorioms.dao.implementations;

import com.pacto.relatorioms.dao.interfaces.PlanoDao;
import com.pacto.relatorioms.entities.Plano;
import com.pacto.relatorioms.filter.FiltroPlanoJSON;
import org.hibernate.query.Query;
import org.springframework.stereotype.Repository;

import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;

@Repository
public class PlanoDaoImpl extends DaoGenericoImpl<Plano, Integer> implements PlanoDao {

    @Override
    public List<Plano> findAll(FiltroPlanoJSON filtros) {
        getCurrentSession().clear();

        StringBuilder hql = new StringBuilder();
        StringBuilder where = new StringBuilder();
        Map<String, Object> params = new HashMap<>();

        try {
            hql.append("SELECT distinct obj  FROM Plano obj");
            where.append(" WHERE 1 = 1 ");
            if (filtros.getEmpresa() != null && filtros.getEmpresa() > 0) {
                where.append(" AND obj.empresa = :empresaid ");
                params.put("empresaid", filtros.getEmpresa());
            }
            if (!filtros.getParametro().isEmpty()) {
                where.append(" AND lower(obj.descricao) like :busca ");
                params.put("busca", "%" + filtros.getParametro().toLowerCase(Locale.ROOT) + "%");
            }
            hql.append(where);

            Query query = getCurrentSession().createQuery(hql.toString());
            if (params != null) {
                for (String p : params.keySet()) {
                    query.setParameter(p, params.get(p));
                }
            }

            return query.getResultList();
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        return null;
    }
}
