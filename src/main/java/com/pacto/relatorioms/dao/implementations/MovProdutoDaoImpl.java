package com.pacto.relatorioms.dao.implementations;

import com.pacto.relatorioms.dao.interfaces.MovProdutoDao;
import com.pacto.config.dto.PaginadorDTO;
import com.pacto.relatorioms.entities.MovProduto;
import org.springframework.stereotype.Repository;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Repository
public class MovProdutoDaoImpl extends DaoGenericoImpl<MovProduto, Integer> implements MovProdutoDao {

    @Override
    public List<MovProduto> findAllByPessoa(Integer codPessoa, PaginadorDTO paginadorDTO) throws Exception {
        getCurrentSession().clear();

        StringBuilder where = new StringBuilder();
        Map<String, Object> params = new HashMap<>();

        return findByParam(where, params);
    }

}
