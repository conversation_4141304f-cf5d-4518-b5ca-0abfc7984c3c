package com.pacto.relatorioms.dao.implementations;

import com.pacto.relatorioms.dao.interfaces.ClienteDao;
import com.pacto.config.dto.PaginadorDTO;
import com.pacto.relatorioms.entities.Cliente;
import com.pacto.config.exceptions.ServiceException;
import com.pacto.relatorioms.filter.FiltroClienteJSON;
import com.pacto.relatorioms.filter.FiltroRelatorioVisitantesJSON;
import org.springframework.stereotype.Repository;

import javax.persistence.Query;
import java.util.*;

@Repository
public class ClienteDaoImpl extends DaoGenericoImpl<Cliente, Integer> implements ClienteDao {

    private static final int MAXIMO_RESULTADOS = 10;

    @Override
    public List<Cliente> findAllRelatorioVisitante(FiltroRelatorioVisitantesJSON filtros, PaginadorDTO paginadorDTO) throws Exception {
        getCurrentSession().clear();

        int maxResults = MAXIMO_RESULTADOS;
        int indiceInicial = 0;

        if (paginadorDTO != null) {
            maxResults = paginadorDTO.getSize() == null ? MAXIMO_RESULTADOS : paginadorDTO.getSize().intValue();
            indiceInicial = paginadorDTO.getPage() == null ? indiceInicial : paginadorDTO.getPage().intValue() * maxResults;
        }

        StringBuilder sqlSubQuery = new StringBuilder();
        Map<String, Object> params = new HashMap<>();

        try{
            sqlSubQuery.append(" select obj.codigo from Cliente obj ");
            sqlSubQuery.append(" left join obj.vinculos vi ");
            sqlSubQuery.append(" left join obj.questionarioClientes qc ");
            sqlSubQuery.append(" where obj.situacao like 'VI' ");
            if(filtros.getEmpresa() != null & filtros.getEmpresa() > 0) {
                sqlSubQuery.append("and obj.empresa.codigo = :codigoempresa ");
                params.put("codigoempresa", filtros.getEmpresa());
            }
            if(filtros.getDataInicioBv() != null){
                sqlSubQuery.append(" AND qc.data >= '").append(filtros.getDataInicioBv()).append(" 00:00:00' ");
            }
            if(filtros.getDataFimBv() != null) {
                sqlSubQuery.append(" AND qc.data <= '").append(filtros.getDataFimBv()).append(" 23:59:59' ");

            }
            if(filtros.getBvCompIncomp() !=null && filtros.getBvCompIncomp().equals("C")) {
                sqlSubQuery.append(" AND qc.codigo is not null and not EXISTS(select cm from ClienteMensagem cm where cm.tipoMensagem like 'BP' and cm.cliente.codigo = obj.codigo)");
            } else if(filtros.getBvCompIncomp().equals("BP")) {
                sqlSubQuery.append(" AND qc.codigo is not null and EXISTS(select cm from ClienteMensagem cm where cm.tipoMensagem like 'BP' and cm.cliente.codigo = obj.codigo)");
            }
            if(filtros.getConsultores().size() > 0){
                String consultoresCodigos = "";
                for(Integer c: filtros.getConsultores()){
                    consultoresCodigos  += "," + c;
                }
                consultoresCodigos = consultoresCodigos.replaceFirst(",", "");
                sqlSubQuery.append(" AND vi.colaborador.codigo IN (").append(consultoresCodigos).append(") ");
            }

            StringBuilder sql = new StringBuilder();
            StringBuilder where = new StringBuilder();
            sql.append("select obj from Cliente obj ");
            sql.append(" left join Email e on e.codigo = (select min(e2.codigo) from Email e2 where e2.pessoa = obj.pessoa.codigo) ");
            sql.append(" left join Telefone t on t.codigo = (select min(t2.codigo) from Telefone t2 where t2.pessoa = obj.pessoa.codigo) ");
            where.append(" where obj.codigo in (").append(sqlSubQuery.toString()).append(")");

            if (paginadorDTO != null) {
                paginadorDTO.setQuantidadeTotalElementos(countWithParam("codigo", null, where, params).longValue());
                if(paginadorDTO.getSort() != null) {
                    if (paginadorDTO.getSort().contains("nome")) {
                        where.append(" ORDER BY obj.pessoa.nome " + paginadorDTO.getSort().split(",")[1]);
                    } else if (paginadorDTO.getSort().contains("nascimento")) {
                        where.append(" ORDER BY obj.pessoa.dataNasc " + paginadorDTO.getSort().split(",")[1]);
                    } else if (paginadorDTO.getSort().contains("cadastro")) {
                        where.append(" ORDER BY obj.pessoa.dataCadastro " + paginadorDTO.getSort().split(",")[1]);
                    } else if (paginadorDTO.getSort().contains("empresa")) {
                        where.append(" ORDER BY obj.empresa.nome " + paginadorDTO.getSort().split(",")[1]);
                    } else if (paginadorDTO.getSort().contains("email")) {
                        where.append(" ORDER BY e.email " + paginadorDTO.getSort().split(",")[1]);
                    } else if (paginadorDTO.getSort().contains("telefone")) {
                        where.append(" ORDER BY t.numero " + paginadorDTO.getSort().split(",")[1]);
                    }
                }
                paginadorDTO.setSize((long) maxResults);
                paginadorDTO.setPage((long) indiceInicial);
            }

            sql.append(where != null ? where.toString() : "");
            Query q = getCurrentSession().createQuery(sql.toString());
            if (params != null) {
                for (String p : params.keySet()) {
                    q.setParameter(p, params.get(p));
                }
            }

            if (maxResults != 0) {
                q.setMaxResults(maxResults);
            }
            if (indiceInicial != 0) {
                q.setFirstResult(indiceInicial);
            }

            return q.getResultList();
        } catch (Exception e) {
            throw new ServiceException(e);
        }
    }

    @Override
    public List<Cliente> findAll(FiltroClienteJSON filtros, PaginadorDTO paginadorDTO) throws Exception {
        getCurrentSession().clear();

        int maxResults = MAXIMO_RESULTADOS;
        int indiceInicial = 0;

        if (paginadorDTO != null) {
            maxResults = paginadorDTO.getSize() == null ? MAXIMO_RESULTADOS : paginadorDTO.getSize().intValue();
            indiceInicial = paginadorDTO.getPage() == null ? indiceInicial : paginadorDTO.getPage().intValue() * maxResults;
        }

        StringBuilder sql = new StringBuilder();
        StringBuilder where = new StringBuilder();
        Map<String, Object> params = new HashMap<>();

        try{

            sql.append("select obj from Cliente obj ");
            where.append(" where 1 = 1 ");
            if(filtros.getCodigoEmpresa() != null && filtros.getCodigoEmpresa() > 0) {
                where.append(" and obj.empresa.codigo = :empresa ");
                params.put("empresa", filtros.getCodigoEmpresa());
            }
            if (filtros.getParametro().matches("\\d+")) {
                where.append(" AND obj.codigoMatricula = :matricula\n");
                params.put("matricula", Integer.parseInt(filtros.getParametro()));
            } else if(!filtros.getParametro().isEmpty()) {
                where.append(" AND lower(obj.pessoa.nome) like :busca ");
                params.put("busca", "%" +filtros.getParametro().toLowerCase(Locale.ROOT)+ "%");
            }


            if (paginadorDTO != null) {
                paginadorDTO.setQuantidadeTotalElementos(countWithParam("codigo", null, where, params).longValue());
                if(paginadorDTO.getSort() != null) {
                    if (paginadorDTO.getSort().contains("nome")) {
                        where.append(" ORDER BY obj.pessoa.nome " + paginadorDTO.getSort().split(",")[1]);
                    } else if (paginadorDTO.getSort().contains("matricula")) {
                        where.append(" ORDER BY obj.codigoMatricula " + paginadorDTO.getSort().split(",")[1]);
                    } else {
                        where.append(" ORDER BY obj.pessoa.nome ASC ");
                    }
                }
                paginadorDTO.setSize((long) maxResults);
                paginadorDTO.setPage((long) indiceInicial);
            } else {
                where.append(" ORDER BY obj.pessoa.nome ASC ");
            }

            sql.append(where != null ? where.toString() : "");
            Query q = getCurrentSession().createQuery(sql.toString());
            if (params != null) {
                for (String p : params.keySet()) {
                    q.setParameter(p, params.get(p));
                }
            }

            if (maxResults != 0) {
                q.setMaxResults(maxResults);
            }
            if (indiceInicial != 0) {
                q.setFirstResult(indiceInicial);
            }

            return q.getResultList();
        } catch (Exception e) {
            throw new ServiceException(e);
        }
    }
}
