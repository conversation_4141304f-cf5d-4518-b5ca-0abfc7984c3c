package com.pacto.relatorioms.dao.implementations;

import com.pacto.relatorioms.dao.interfaces.CidadeDao;
import com.pacto.relatorioms.dao.interfaces.UsuarioDao;
import com.pacto.relatorioms.entities.Usuario;
import com.pacto.relatorioms.entities.empresa.Cidade;
import org.springframework.stereotype.Repository;

@Repository
public class CidadeDaoImpl extends DaoGenericoImpl<Cidade, Integer> implements CidadeDao {
}
