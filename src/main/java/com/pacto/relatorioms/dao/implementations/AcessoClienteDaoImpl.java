package com.pacto.relatorioms.dao.implementations;

import com.pacto.config.utils.UteisValidacao;
import com.pacto.relatorioms.dao.interfaces.AcessoClienteDao;
import com.pacto.relatorioms.entities.AcessoCliente;
import com.pacto.relatorioms.filter.FiltroListaAcessosJSON;
import com.pacto.config.utils.Uteis;
import org.hibernate.query.Query;
import org.springframework.stereotype.Repository;

import java.util.*;

@Repository
public class AcessoClienteDaoImpl extends DaoGenericoImpl<AcessoCliente, Integer> implements AcessoClienteDao {

    @Override
    public List<AcessoCliente> consultarListaAcessosClientes(FiltroListaAcessosJSON filtros) throws Exception {
        getCurrentSession().clear();

        StringBuilder select = new StringBuilder();
        StringBuilder join = new StringBuilder();
        StringBuilder where = new StringBuilder();
        StringBuilder order = new StringBuilder();
        StringBuilder hqlFinal = new StringBuilder();
        Map<String, Object> params = new HashMap<>();

        select.append("SELECT ");
        select.append(" ac.codigo ");
        select.append(" FROM AcessoCliente ac ");
        join.append("inner join Cliente cli ON cli.codigo = ac.cliente.codigo ");
        join.append("inner join Empresa emp ON emp.codigo = cli.empresa.codigo ");
        join.append("inner join SituacaoClienteSinteticodw sit ON sit.codigoCliente = cli.codigo ");
        join.append("inner join LocalAcesso loc ON loc.codigo = ac.localAcesso.codigo ");
        join.append("inner join Pessoa pes ON pes.codigo = cli.pessoa.codigo ");
        join.append("left join Email em ON em.codigo = (SELECT max(em2.codigo) FROM Email em2 WHERE em2.pessoa = pes.codigo) ");
        join.append("inner join Coletor ct ON ct.codigo = ac.coletor.codigo ");
        join.append("left join Usuario us ON us.codigo = ac.usuario.codigo ");
        where.append("where  1 = 1 ");

        if (filtros != null) {
            //Empresa
            if (!UteisValidacao.emptyNumber(filtros.getEmpresa())) {
                where.append(" and emp.codigo = :empresa ");
                params.put("empresa", filtros.getEmpresa());
            }
            // Data e hora entrada
            if (filtros.getPeriodoPesquisaInicial() != null) {
                where.append(" and ac.dtHrEntrada >= '").append(Uteis.getData(filtros.getPeriodoPesquisaInicial(), "bd"));
                where.append(" 00:00:00'");
            }
            // Data e hora saida
            if (filtros.getPeriodoPesquisaFinal() != null) {
                where.append(" and ac.dtHrEntrada <= '").append(Uteis.getData(filtros.getPeriodoPesquisaFinal(), "bd"));
                where.append(" 23:59:59'");
            }
            // Faixa horaria
            if (!filtros.getFaixaHorariaInicial().isEmpty()) {
                where.append(" and (extract(hour FROM ac.dtHrEntrada) * 60 + extract(minutes FROM ac.dtHrEntrada)) ")
                        .append(" between ").append(filtros.getFaixaHorariaInicialEmMinutos())
                        .append(" and ").append(filtros.getFaixaHorariaFinalEmMinutos());
            }
            // Cliente
            if (!UteisValidacao.emptyNumber(filtros.getCliente())) {
                where.append(" and ac.cliente.codigo = :cliente ");
                params.put("cliente", filtros.getCliente());
            }
            // Plano e Modalidade
            if (!UteisValidacao.emptyNumber(filtros.getPlano()) || !UteisValidacao.emptyNumber(filtros.getModalidade())) {
                if (!filtros.getUltimoContratoCliente()) {
                    join.append(" inner join Contrato con ON con.pessoa.codigo = pes.codigo \n");
                } else {
                    join.append(" inner join Contrato con on con.codigo = sit.codigoContrato \n");
                }
                if (!UteisValidacao.emptyNumber(filtros.getPlano())) {
                    join.append(" inner join Plano pla on pla.codigo = con.plano.codigo ");
                    where.append(" and pla.codigo = :plano ");
                    params.put("plano", filtros.getPlano());
                }
                if (!UteisValidacao.emptyNumber(filtros.getModalidade())) {
                    join.append(" inner join ContratoModalidade cm on cm.contrato = con.codigo ");
                    where.append(" and cm.modalidade = :modalidade ");
                    params.put("modalidade", filtros.getModalidade());
                }
            }
            // Professor turma
            if (!UteisValidacao.emptyNumber(filtros.getProfessorTurma())) {
                join.append(" inner join MatriculaAlunoHorarioTurma matr on matr.pessoa = sit.codigoPessoa ");
                join.append(" inner join HorarioTurma ht on ht.codigo = matr.horarioTurma ");
                where.append(" and ht.professor = :professorturma ");
                params.put("professorturma", filtros.getProfessorTurma());
            }
            // Professor Vinculo
            if (!UteisValidacao.emptyNumber(filtros.getProfessorVinculo())) {
                join.append("inner join Vinculo vipr on vipr.cliente.codigo = cli.codigo and vipr.tipoVinculo = 'PR' ");
                where.append("and vipr.colaborador.codigo = :professorvinculo ");
                params.put("professorvinculo", filtros.getProfessorVinculo());
            }
            // Professor Treino
            if (!UteisValidacao.emptyNumber(filtros.getProfessorTreino())) {
                join.append("inner join Vinculo vitw on vitw.cliente.codigo = cli.codigo and vitw.tipoVinculo = 'TW' ");
                where.append("and vitw.colaborador.codigo = :professortwvinculo ");
                params.put("professortwvinculo", filtros.getProfessorTreino());
            }
            // Grupo
            if (!UteisValidacao.emptyNumber(filtros.getGrupo())) {
                join.append("inner join ClienteGrupo cligp on cligp.cliente = cli.codigo ");
                where.append("and cligp.grupo = :grupo ");
                params.put("grupo", filtros.getGrupo());
            }

            // Exibir somente o primeiro acesso por dia
            if (filtros.getExibirSomentePrimeiroAcessoPorDia()) {
                where.append(" and ac.codigo in ( select min(ac2.codigo) from AcessoCliente ac2 where ac2.cliente = ac.cliente.codigo group by to_char(ac2.dtHrEntrada, 'DD/MM/YYYY')) ");
            }

            //Não exibir acessos bloqueados/tentativa de acesso
            if (!filtros.getExibirAcessosBloqueados()) {
                where.append(" and ac.situacao not like 'RV_BLOQ%' ");
            }
        }
        hqlFinal.append(" select ac from AcessoCliente ac where ac.codigo in (");
        hqlFinal.append(select);
        hqlFinal.append(join);
        hqlFinal.append(where);
        hqlFinal.append(") ");
        hqlFinal.append(order);

        Query q = getCurrentSession().createQuery(hqlFinal.toString());
        if (params != null) {
            for (String p : params.keySet()) {
                q.setParameter(p, params.get(p));
            }
        }

        return q.getResultList();
    }

    @Override
    public List<AcessoCliente> consultarListaAcessosClientesOutrasUnidades(FiltroListaAcessosJSON filtros) throws Exception {
        getCurrentSession().clear();

        StringBuilder select = new StringBuilder();
        StringBuilder join = new StringBuilder();
        StringBuilder where = new StringBuilder();
        StringBuilder order = new StringBuilder();
        StringBuilder hqlFinal = new StringBuilder();
        Map<String, Object> params = new HashMap<>();

        select.append("SELECT ");
        select.append(" ac.codigo ");
        select.append(" FROM AcessoCliente ac ");
        join.append("inner join LocalAcesso loc ON loc.codigo = ac.localAcesso.codigo ");
        join.append("inner join Coletor ct ON ct.codigo = ac.coletor.codigo ");
        join.append("left join Usuario us ON us.codigo = ac.usuario.codigo ");
        where.append("where ac.nomeCodEmpresaOrigem is not null and ac.nomeCodEmpresaOrigem <> '' ");
        where.append("and ac.nomeCodEmpresaOrigem <> ac.nomeCodEmpresaAcessou ");
        where.append("and ac.nomeCpfEmailClienteOrigem is not null and ac.nomeCpfEmailClienteOrigem <> '' ");

        if (filtros != null) {
            //Empresa
            if (!UteisValidacao.emptyNumber(filtros.getEmpresa())) {
                where.append(" and ac.nomeCodEmpresaAcessou LIKE CONCAT(:empresa, ' - %') ");
                params.put("empresa", filtros.getEmpresa());
            }

            // Data e hora entrada
            if (filtros.getPeriodoPesquisaInicial() != null) {
                where.append(" and ac.dtHrEntrada >= '").append(Uteis.getData(filtros.getPeriodoPesquisaInicial(), "bd"));
                where.append(" 00:00:00'");
            }
            // Data e hora saida
            if (filtros.getPeriodoPesquisaFinal() != null) {
                where.append(" and ac.dtHrEntrada <= '").append(Uteis.getData(filtros.getPeriodoPesquisaFinal(), "bd"));
                where.append(" 23:59:59'");
            }
            // Faixa horaria
            if (!filtros.getFaixaHorariaInicial().isEmpty()) {
                where.append(" and (extract(hour FROM ac.dtHrEntrada) * 60 + extract(minutes FROM ac.dtHrEntrada)) ")
                        .append(" between ").append(filtros.getFaixaHorariaInicialEmMinutos())
                        .append(" and ").append(filtros.getFaixaHorariaFinalEmMinutos());
            }

            //Não exibir acessos bloqueados/tentativa de acesso
            if (!filtros.getExibirAcessosBloqueados()) {
                where.append(" and ac.situacao not like 'RV_BLOQ%' ");
            }
        }
        hqlFinal.append(" select ac from AcessoCliente ac where ac.codigo in (");
        hqlFinal.append(select);
        hqlFinal.append(join);
        hqlFinal.append(where);
        hqlFinal.append(") ");
        hqlFinal.append(order);

        Query q = getCurrentSession().createQuery(hqlFinal.toString());
        if (params != null) {
            for (String p : params.keySet()) {
                q.setParameter(p, params.get(p));
            }
        }

        return q.getResultList();
    }

    public Long countResults(StringBuilder count, Map<String, Object> params) {
        Query query = getCurrentSession().createQuery(count.toString());
        if (params != null) {
            for (String p : params.keySet()) {
                query.setParameter(p, params.get(p));
            }
        }
        return (Long) query.getSingleResult();
    }
}
