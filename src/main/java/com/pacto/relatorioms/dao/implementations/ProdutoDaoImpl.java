package com.pacto.relatorioms.dao.implementations;


import com.pacto.relatorioms.dao.interfaces.ProdutoDao;
import com.pacto.config.dto.PaginadorDTO;
import com.pacto.relatorioms.entities.Produto;
import com.pacto.relatorioms.filter.FiltroProdutoJSON;
import org.hibernate.query.Query;
import org.springframework.stereotype.Repository;
import org.springframework.util.StringUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Repository
public class ProdutoDaoImpl extends DaoGenericoImpl<Produto, Integer> implements ProdutoDao {

    private static final int MAXIMO_RESULTADOS = 16;

    @Override
    public List<Produto> findAll(FiltroProdutoJSON filtros, PaginadorDTO paginadorDTO) throws Exception {
        getCurrentSession().clear();

        int maxResults = MAXIMO_RESULTADOS;
        int indiceInicial = 0;

        if (paginadorDTO != null) {
            maxResults = paginadorDTO.getSize() == null ? MAXIMO_RESULTADOS : paginadorDTO.getSize().intValue();
            indiceInicial = paginadorDTO.getPage() == null ? indiceInicial : paginadorDTO.getPage().intValue() * maxResults;
        }

        StringBuilder hql = new StringBuilder();
        StringBuilder where = new StringBuilder();
        Map<String, Object> params = new HashMap<>();

        try {
            hql.append("SELECT distinct obj FROM Produto obj");
            where.append(" WHERE 1=1");
            if (filtros.getTipoProduto().equalsIgnoreCase("SE") || filtros.getTipoProduto().equalsIgnoreCase("AT") || filtros.getTipoProduto().equalsIgnoreCase("DS")) {
                where.append(" AND obj.tipoProduto = '").append(filtros.getTipoProduto()).append("' \n");
            }

            if (paginadorDTO != null) {
                maxResults = paginadorDTO.getSize() == null ? MAXIMO_RESULTADOS : paginadorDTO.getSize().intValue();
                indiceInicial = paginadorDTO.getPage() == null ? indiceInicial : paginadorDTO.getPage().intValue() * maxResults;
            }

            hql.append(where);
            Query query = getCurrentSession().createQuery(hql.toString());
            if (params != null) {
                for (String p : params.keySet()) {
                    query.setParameter(p, params.get(p));
                }
            }

            if (maxResults > 0) {
                query.setMaxResults(maxResults);
            }
            if (indiceInicial != 0) {
                query.setFirstResult(indiceInicial);
            }

            return query.getResultList();
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        return null;
    }

    @Override
    public List<Produto> findAllMin(FiltroProdutoJSON filtroProdutoJSON) throws Exception {

        StringBuilder hql = new StringBuilder();

        hql.append(" select new Produto(p.codigo,p.descricao,p.nrdiasVigencia,p.tipoProduto) from Produto p");
        if(!StringUtils.isEmpty(filtroProdutoJSON.getParametroBusca())){
            hql.append("\n WHERE UPPER(p.descricao) like :descricao \n");
            if (filtroProdutoJSON.getTipoProduto().equalsIgnoreCase("SE") || filtroProdutoJSON.getTipoProduto().equalsIgnoreCase("AT") || filtroProdutoJSON.getTipoProduto().equalsIgnoreCase("DS")) {
                hql.append(" AND p.tipoProduto = :tipoProduto \n");
            }
        }else if (filtroProdutoJSON.getTipoProduto().equalsIgnoreCase("SE") || filtroProdutoJSON.getTipoProduto().equalsIgnoreCase("AT") || filtroProdutoJSON.getTipoProduto().equalsIgnoreCase("DS")) {
            hql.append(" WHERE p.tipoProduto = :tipoProduto \n");
        }

        hql.append("order by p.descricao");

        Query query = getCurrentSession().createQuery(hql.toString());

        if(!StringUtils.isEmpty(filtroProdutoJSON.getParametroBusca())){
            query.setParameter("descricao", "%"+filtroProdutoJSON.getParametroBusca().toUpperCase()+"%");
            if (filtroProdutoJSON.getTipoProduto().equalsIgnoreCase("SE") || filtroProdutoJSON.getTipoProduto().equalsIgnoreCase("AT") || filtroProdutoJSON.getTipoProduto().equalsIgnoreCase("DS")) {
                query.setParameter("tipoProduto",filtroProdutoJSON.getTipoProduto().toUpperCase());
            }
        }else if (filtroProdutoJSON.getTipoProduto().equalsIgnoreCase("SE") || filtroProdutoJSON.getTipoProduto().equalsIgnoreCase("AT") || filtroProdutoJSON.getTipoProduto().equalsIgnoreCase("DS")) {
            query.setParameter("tipoProduto",filtroProdutoJSON.getTipoProduto().toUpperCase());
        }

        return query.getResultList();
    }

}
