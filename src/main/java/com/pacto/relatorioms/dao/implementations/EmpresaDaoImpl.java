package com.pacto.relatorioms.dao.implementations;

import com.pacto.relatorioms.dao.interfaces.EmpresaDao;
import com.pacto.relatorioms.entities.empresa.Empresa;
import com.pacto.relatorioms.filter.FiltroEmpresaJSON;
import org.springframework.stereotype.Repository;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Repository
public class EmpresaDaoImpl extends DaoGenericoImpl<Empresa, Integer> implements EmpresaDao {

    @Override
    public List<Empresa> findAllActives(FiltroEmpresaJSON filtros) {
        try {
            Map<String, Object> params = new HashMap<>();
            StringBuilder hql = new StringBuilder();
            hql.append("WHERE obj.ativa = :ativa");
            params.put("ativa", true);
            if(filtros.getParametro() != null && !filtros.getParametro().isEmpty()) {
                hql.append(" AND lower(obj.nome) like :term ");
                params.put("term", "%" + filtros.getParametro().toLowerCase().trim() + "%");
            }
            return findByParam(hql, params);
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        return null;
    }
}
