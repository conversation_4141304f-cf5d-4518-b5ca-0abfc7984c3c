package com.pacto.relatorioms.dao.implementations;

import com.pacto.config.utils.UteisValidacao;
import com.pacto.relatorioms.dao.interfaces.AcessoColaboradorDao;
import com.pacto.relatorioms.entities.AcessoColaborador;
import com.pacto.relatorioms.filter.FiltroListaAcessosJSON;
import com.pacto.config.utils.Uteis;
import org.hibernate.query.Query;
import org.springframework.stereotype.Repository;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Repository
public class AcessoColaboradorDaoImpl extends DaoGenericoImpl<AcessoColaborador, Integer> implements AcessoColaboradorDao {

    @Override
    public List<AcessoColaborador> consultarListaAcessosColaboradores(FiltroListaAcessosJSON filtros) throws Exception {
        getCurrentSession().clear();

        StringBuilder select = new StringBuilder();
        StringBuilder count = new StringBuilder(" SELECT count(ac) FROM AcessoColaborador ac ");
        StringBuilder join = new StringBuilder();
        StringBuilder where = new StringBuilder();
        StringBuilder order = new StringBuilder();
        StringBuilder hqlFinal = new StringBuilder();
        Map<String, Object> params = new HashMap<>();

        select.append("SELECT ");
        select.append(" distinct ac ");
        select.append(" FROM AcessoColaborador ac ");
        join.append("inner join Colaborador col ON col.codigo = ac.colaborador.codigo ");
        join.append("inner join Pessoa pes ON pes.codigo = col.pessoa.codigo ");
        join.append("inner join LocalAcesso loc ON loc.codigo = ac.localAcesso.codigo ");
        join.append("inner join Empresa emp ON emp.codigo = loc.empresa.codigo ");
        join.append("left join Email em ON em.codigo = (SELECT max(em2.codigo) FROM Email em2 WHERE em2.pessoa = pes.codigo) ");
        join.append("inner join Coletor ct ON ct.codigo = ac.coletor.codigo ");
        where.append("where  1 = 1 ");

        if (filtros != null) {
            //Empresa
            if (!UteisValidacao.emptyNumber(filtros.getEmpresa())) {
                where.append(" and emp.codigo = :empresa ");
                params.put("empresa", filtros.getEmpresa());
            }
            // Data e hora entrada
            if (filtros.getPeriodoPesquisaInicial() != null) {
                where.append(" and ac.dtHrEntrada >= '").append(Uteis.getData(filtros.getPeriodoPesquisaInicial(), "bd"));
                where.append(" 00:00:00'");
            }
            // Data e hora saida
            if (filtros.getPeriodoPesquisaFinal() != null) {
                where.append(" and ac.dtHrEntrada <= '").append(Uteis.getData(filtros.getPeriodoPesquisaFinal(), "bd"));
                where.append(" 23:59:59'");
            }
            // Faixa horaria
            if (!filtros.getFaixaHorariaInicial().isEmpty()) {
                where.append(" and (extract(hour FROM ac.dtHrEntrada) * 60 + extract(minutes FROM ac.dtHrEntrada)) ")
                        .append(" between ").append(filtros.getFaixaHorariaInicialEmMinutos())
                        .append(" and ").append(filtros.getFaixaHorariaFinalEmMinutos());
            }
            // Colaborador
            if (!UteisValidacao.emptyNumber(filtros.getColaborador())) {
                where.append(" and (ac.colaborador.codigo = :colaborador OR pes.codigo = (SELECT pessoa FROM Colaborador WHERE codigo = :colaborador))");
                params.put("colaborador", filtros.getColaborador());
            }

            //Ordenar por
            if (filtros.getOrdenadoPor() != null) {
                if (filtros.getOrdenadoPor().equals("dthrentrada")) {
                    order.append(" ORDER BY ac.dtHrEntrada ASC ");
                } else {
                    order.append(" ORDER BY ac.colaborador.pessoa.nome, ac.dtHrEntrada ASC ");
                }
            }

            // Exibir somente o primeiro acesso por dia
            if (filtros.getExibirSomentePrimeiroAcessoPorDia()) {
                where.append(" and ac.codigo in ( select min(ac2.codigo) from AcessoColaborador ac2 where ac2.colaborador.codigo = ac.colaborador.codigo group by to_char(ac2.dtHrEntrada, 'DD/MM/YYYY')) ");
            }
        }

        hqlFinal.append(" select ac from AcessoColaborador ac where ac.codigo in (");
        hqlFinal.append(select);
        hqlFinal.append(join);
        hqlFinal.append(where);
        hqlFinal.append(") ");
        hqlFinal.append(order);

        Query q = getCurrentSession().createQuery(hqlFinal.toString());
        if (params != null) {
            for (String p : params.keySet()) {
                q.setParameter(p, params.get(p));
            }
        }

        return q.getResultList();
    }

    public Long countResults(StringBuilder count, Map<String, Object> params) {
        Query query = getCurrentSession().createQuery(count.toString());
        if (params != null) {
            for (String p : params.keySet()) {
                query.setParameter(p, params.get(p));
            }
        }
        return (Long) query.getSingleResult();
    }
}
