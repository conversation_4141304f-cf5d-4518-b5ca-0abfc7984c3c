package com.pacto.relatorioms.dao.implementations;

import com.pacto.config.dto.PaginadorDTO;
import com.pacto.config.utils.Uteis;
import com.pacto.relatorioms.dao.interfaces.RelatorioGogoodDao;
import com.pacto.relatorioms.dto.RelatorioGogoodDTO;
import com.pacto.relatorioms.filter.FiltroRelatorioGogoodJSON;
import com.pacto.relatorioms.filter.RelatorioGogoodFilter;
import org.hibernate.query.Query;
import org.springframework.stereotype.Repository;

import java.util.*;

@Repository
public class RelatorioGogoodDaoImpl extends DaoGenericoImpl<RelatorioGogoodFilter, Integer> implements RelatorioGogoodDao {
    private static final int MAXIMO_RESULTADOS = 10;

    @Override
    public List<RelatorioGogoodDTO> consultarAlunosGogood(FiltroRelatorioGogoodJSON filtros, RelatorioGogoodFilter relatorioGogoodFilter, PaginadorDTO paginadorDTO) throws Exception {
        getCurrentSession().clear();

        int maxResults = MAXIMO_RESULTADOS;
        int indiceInicial = 0;

        if (paginadorDTO != null) {
            maxResults = paginadorDTO.getSize() == null ? MAXIMO_RESULTADOS : paginadorDTO.getSize().intValue();
            indiceInicial = paginadorDTO.getPage() == null ? indiceInicial : paginadorDTO.getPage().intValue() * maxResults;
        }

        StringBuilder select = new StringBuilder();
        StringBuilder join = new StringBuilder();
        StringBuilder where = new StringBuilder();
        Map<String, Object> params = new HashMap<>();

        select.append(" select obj.codigo, obj.codigoMatricula, obj.pessoa.codigo as codigopessoa, obj.pessoa.nome,");
        select.append(" obj.pessoa.dataNasc, em.email, tel.numero, obj.empresa.codigo as codigoempresa, obj.empresa.nome as nomeempresa, ");
        select.append("  pa.dataInicioAcesso, pa.tokenGogood ");
        select.append(" from Cliente obj ");
        join.append(" inner join PeriodoAcessoCliente pa on pa.pessoa.codigo = obj.pessoa.codigo ");
        join.append(" inner join obj.empresa emp ");
        join.append(" inner join obj.pessoa pessoa ");
        join.append(" left join Email em on em.codigo = (select min(e2.codigo) from Email e2 where e2.pessoa = obj.pessoa.codigo) ");
        join.append(" left join Telefone tel on tel.codigo = (select min(t2.codigo) from Telefone t2 where t2.pessoa = obj.pessoa.codigo) ");
        where.append(" where coalesce(pa.tokenGogood,'') <> '' ");
        where.append(" and pa.tipoAcesso = 'PL' ");

        if (filtros != null) {
            where.append(" and obj.empresa.codigo = :empresa ");
            params.put("empresa", filtros.getEmpresa());
            if(filtros.getPeriodoPesquisaInicial() != null) {
                where.append(" and pa.dataInicioAcesso >= '").append(Uteis.getData(filtros.getPeriodoPesquisaInicial(), "bd")).append("' ");
            }
            if(filtros.getPeriodoPesquisaFinal() != null) {
                where.append(" and pa.dataFinalAcesso <= '").append(Uteis.getData(filtros.getPeriodoPesquisaFinal(), "bd")).append("' ");
            }
        }

        if (paginadorDTO != null) {
            paginadorDTO.setQuantidadeTotalElementos(countWithParamClass("codigo", "Cliente", join, where, params).longValue());
            if(paginadorDTO.getSort() != null) {
                if (paginadorDTO.getSort().contains("matricula")) {
                    where.append(" ORDER BY obj.codigoMatricula " + paginadorDTO.getSort().split(",")[1]);
                } else if (paginadorDTO.getSort().contains("nome")) {
                    where.append(" ORDER BY obj.pessoa.nome " + paginadorDTO.getSort().split(",")[1]);
                } else if (paginadorDTO.getSort().contains("dataRegistro")) {
                    where.append(" ORDER BY pa.dataInicioAcesso " + paginadorDTO.getSort().split(",")[1]);
                } else if (paginadorDTO.getSort().contains("token")) {
                    where.append(" ORDER BY pa.tokenGogood " + paginadorDTO.getSort().split(",")[1]);
                } else {
                    where.append(" ORDER BY obj.codigoMatricula " + paginadorDTO.getSort().split(",")[1]);
                }
            } else {
                where.append(" ORDER BY obj.pessoa.nome ");
            }
            paginadorDTO.setSize((long) maxResults);
            paginadorDTO.setPage((long) indiceInicial);
        }

        select.append(join.toString());
        select.append(where.toString());

         Query query = getCurrentSession().createQuery(select.toString());

        if (params != null) {
            for (String p : params.keySet()) {
                query.setParameter(p, params.get(p));
            }
        }




        if (maxResults != 0)
            query.setMaxResults(maxResults);
        if (indiceInicial != 0)
            query.setFirstResult(indiceInicial);

        List<Object[]> result = query.getResultList();
        List<RelatorioGogoodDTO> relatorioGogoodDTOS = new ArrayList<>();

        for (Object[] line : result) {
            String codigoCliente = line[0].toString();
            String codigoMatricula = line[1].toString();
            String codigoPessoa = line[2].toString();
            String nome = line[3].toString();
            String dataNasc = Uteis.getData((Date) line[4], "br");
            String email = line[5] != null ? line[5].toString() : "";
            String telefone = line[6] != null ? line[6].toString() : "";
            String codigoEmpresa = line[7].toString();
            String nomeEmpresa = line[8].toString();
            String dataInicioAcesso = Uteis.getData((Date) line[9], "br");
            String tokenGogood = line[10].toString();

            RelatorioGogoodDTO relGogoodDTO = new RelatorioGogoodDTO();
            // Dados para a lista de resultados do Rel Gympass
            relGogoodDTO.setMatricula(codigoMatricula);
            relGogoodDTO.setNome(nome);
            relGogoodDTO.setDataNasc(dataNasc);
            relGogoodDTO.setDataRegistro(dataInicioAcesso);
            relGogoodDTO.setToken(tokenGogood);
            relGogoodDTO.setEmail(email);
            relGogoodDTO.setTelefone(telefone);
            relGogoodDTO.setEmpresa(nomeEmpresa);

            relatorioGogoodDTOS.add(relGogoodDTO);
        }

        return relatorioGogoodDTOS;
    }

    public Long maxResult(String count) {
        return (Long) getCurrentSession().createQuery(count).getSingleResult();
    }

}
