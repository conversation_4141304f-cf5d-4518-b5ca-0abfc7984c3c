package com.pacto.relatorioms.dao.implementations;

import com.pacto.relatorioms.dao.interfaces.RelatorioConvidadosDao;
import com.pacto.relatorioms.dto.RelatorioConvidadosDTO;
import com.pacto.config.dto.PaginadorDTO;
import com.pacto.relatorioms.filter.FiltroRelatorioConvidadosJSON;
import com.pacto.config.utils.Uteis;
import org.hibernate.query.Query;
import org.springframework.stereotype.Repository;

import java.util.*;

@Repository
public class RelatorioConvidadosDaoImpl extends DaoGenericoImpl<RelatorioConvidadosDTO, Integer> implements RelatorioConvidadosDao {
    private static final int MAXIMO_RESULTADOS = 10;

    @Override
    public List<RelatorioConvidadosDTO> consultarAlunosConvidados(FiltroRelatorioConvidadosJSON filtros, PaginadorDTO paginadorDTO) throws Exception {
        getCurrentSession().clear();

        int maxResults = MAXIMO_RESULTADOS;
        int indiceInicial = 0;

        if (paginadorDTO != null) {
            maxResults = paginadorDTO.getSize() == null ? MAXIMO_RESULTADOS : paginadorDTO.getSize().intValue();
            indiceInicial = paginadorDTO.getPage() == null ? indiceInicial : paginadorDTO.getPage().intValue() * maxResults;
        }

        StringBuilder select = new StringBuilder();
        StringBuilder join = new StringBuilder();
        StringBuilder where = new StringBuilder();
        Map<String, Object> params = new HashMap<>();

        select.append("select obj.codigo, obj.convidou.pessoa.nome as convidou, ");
        select.append("obj.convidado.pessoa.nome as convidado, obj.dia, obj.convidado.situacao, ");
        select.append("obj.convidado.codigoMatricula, obj.convidado.pessoa.dataNasc, ");
        select.append("ac.dtHrEntrada, tel.numero, em.email, obj.convidado.pessoa.dataCadastro, obj.convidado.empresa.nome");
        select.append(" from Convite obj ");
        join.append(" inner join obj.convidou ");
        join.append(" inner join obj.convidado ");
        join.append(" left join AcessoCliente ac on ac.codigo = (select max(ac2.codigo) from AcessoCliente ac2 where ac2.cliente = obj.convidado.codigo and ac2.sentido = 'E') ");
        join.append(" left join Telefone tel on tel.codigo = (select max(tel2.codigo) from Telefone tel2 where tel2.pessoa = obj.convidado.pessoa.codigo) ");
        join.append(" left join Email em on em.codigo = (select max(em2.codigo) from Email em2 where em2.pessoa = obj.convidado.pessoa.codigo) ");
        where.append(" where 1 = 1 ");

        if (filtros != null) {
            if(filtros.getEmpresa() > 0) {
                where.append(" and obj.convidou.empresa.codigo = :empresa ");
                params.put("empresa", filtros.getEmpresa());
            }
            if(filtros.getPeriodoPesquisaInicial() != null) {
                where.append(" and obj.dia >= '").append(Uteis.getData(filtros.getPeriodoPesquisaInicial(), "bd")).append("' ");
            }
            if(filtros.getPeriodoPesquisaFinal() != null) {
                where.append(" and obj.dia <= '").append(Uteis.getData(filtros.getPeriodoPesquisaFinal(), "bd")).append("' ");
            }
        }

        if (paginadorDTO != null) {
            paginadorDTO.setQuantidadeTotalElementos(countWithParamClass("codigo", "Convite", join, where, params).longValue());
            if(paginadorDTO.getSort() != null) {
                if (paginadorDTO.getSort().contains("codigo")) {
                    where.append(" ORDER BY obj.codigo " + paginadorDTO.getSort().split(",")[1]);
                } else if (paginadorDTO.getSort().toLowerCase().contains("convidou")) {
                    where.append(" ORDER BY obj.convidou.pessoa.nome " + paginadorDTO.getSort().split(",")[1]);
                } else if (paginadorDTO.getSort().toLowerCase().contains("convidado")) {
                    where.append(" ORDER BY obj.convidado.pessoa.nome " + paginadorDTO.getSort().split(",")[1]);
                } else if (paginadorDTO.getSort().toLowerCase().contains("dia")) {
                    where.append(" ORDER BY obj.dia " + paginadorDTO.getSort().split(",")[1]);
                } else if (paginadorDTO.getSort().toLowerCase().contains("situacao")) {
                    where.append(" ORDER BY obj.convidado.situacao " + paginadorDTO.getSort().split(",")[1]);
                }
            } else {
                where.append(" ORDER BY obj.codigo DESC ");
            }
            paginadorDTO.setSize((long) maxResults);
            paginadorDTO.setPage((long) indiceInicial);
        }

        select.append(join.toString());
        select.append(where.toString());

         Query query = getCurrentSession().createQuery(select.toString());

        if (params != null) {
            for (String p : params.keySet()) {
                query.setParameter(p, params.get(p));
            }
        }

        if (maxResults != 0)
            query.setMaxResults(maxResults);
        if (indiceInicial != 0)
            query.setFirstResult(indiceInicial);

        List<Object[]> result = query.getResultList();
        List<RelatorioConvidadosDTO> relatorioConvidadosDTOS = new ArrayList<>();

        for (Object[] line : result) {
            String codigo = line[0] != null ? line[0].toString() : "";
            String convidou = line[1] != null ? line[1].toString() : "";
            String convidado = line[1] != null ? line[2].toString() : "";
            String diaConvite = line[3] != null ? Uteis.getData((Date) line[3], "br") : "";
            String situacao = line[4] != null ? line[4].toString() : "";
            String matricula = line[5] != null ? line[5].toString() : "";
            String dataNasc = line[6] != null ? Uteis.getData((Date) line[6], "br") : "";
            String ultimoAcesso = line[7] != null ? Uteis.getData((Date) line[7], "br"): "";
            String telefone = line[8] != null ? line[8].toString() : "";
            String email = line[9] != null ? line[9].toString() : "";
            String dataCadastro = line[10] != null ? Uteis.getData((Date) line[10], "br") : "";
            String empresa = line[11] != null ? line[11].toString() : "";


            RelatorioConvidadosDTO relConvidadosDto = new RelatorioConvidadosDTO();
            relConvidadosDto.setCodigo(Integer.parseInt(codigo));
            relConvidadosDto.setAlunoQueConvidou(convidou);
            relConvidadosDto.setConvidado(convidado);
            relConvidadosDto.setDiaConvite(diaConvite);
            relConvidadosDto.setSituacao(situacao);
            relConvidadosDto.setMatricula(matricula);
            relConvidadosDto.setNome(relConvidadosDto.getConvidado());
            relConvidadosDto.setDataNasc(dataNasc);
            relConvidadosDto.setUltimoAcesso(ultimoAcesso);
            relConvidadosDto.setTelefone(telefone);
            relConvidadosDto.setEmail(email);
            relConvidadosDto.setDataCadastro(dataCadastro);
            relConvidadosDto.setEmpresa(empresa);
            relatorioConvidadosDTOS.add(relConvidadosDto);
        }

        return relatorioConvidadosDTOS;
    }

    public Long maxResult(String count) {
        return (Long) getCurrentSession().createQuery(count).getSingleResult();
    }

}
