package com.pacto.relatorioms.dao.implementations;

import com.pacto.relatorioms.dao.interfaces.ModalidadeDao;
import com.pacto.relatorioms.entities.Modalidade;
import com.pacto.relatorioms.filter.FiltroModalidadeJSON;
import org.hibernate.query.Query;
import org.springframework.stereotype.Repository;

import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;

@Repository
public class ModalidadeDaoImpl extends DaoGenericoImpl<Modalidade, Integer> implements ModalidadeDao {

    @Override
    public List<Modalidade> findAll(FiltroModalidadeJSON filtros) {
        getCurrentSession().clear();

        StringBuilder hql = new StringBuilder();
        StringBuilder where = new StringBuilder();
        StringBuilder join = new StringBuilder();
        Map<String, Object> params = new HashMap<>();

        try {
            hql.append("SELECT distinct obj  FROM Modalidade obj");
            join.append(" inner join ModalidadeEmpresa me ON me.modalidade = obj.codigo ");
            where.append(" WHERE 1 = 1 ");
            if (filtros.getEmpresa() != null && filtros.getEmpresa() > 0) {
                where.append(" AND me.empresa = :empresaid ");
                params.put("empresaid", filtros.getEmpresa());
            }
            if (!filtros.getParametro().isEmpty()) {
                where.append(" AND lower(obj.nome) like :busca ");
                params.put("busca", "%" + filtros.getParametro().toLowerCase(Locale.ROOT) + "%");
            }
            hql.append(join);
            hql.append(where);

            Query query = getCurrentSession().createQuery(hql.toString());
            if (params != null) {
                for (String p : params.keySet()) {
                    query.setParameter(p, params.get(p));
                }
            }

            return query.getResultList();
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        return null;
    }
}
