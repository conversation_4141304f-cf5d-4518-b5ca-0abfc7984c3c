package com.pacto.relatorioms.adapters;

import com.pacto.relatorioms.dto.ColetorDTO;
import com.pacto.relatorioms.entities.Coletor;
import org.springframework.stereotype.Component;

@Component
public class ColetorAdapter implements AdapterInterface<Coletor, ColetorDTO> {

    public ColetorAdapter() {
    }

    @Override
    public ColetorDTO toDto(Coletor coletor) {
        if (coletor != null) {
            ColetorDTO coletorDTO = new ColetorDTO();
            coletorDTO.setCodigo(coletor.getCodigo());
            coletorDTO.setDescricao(coletor.getDescricao());
            return coletorDTO;
        }
        return null;
    }
}
