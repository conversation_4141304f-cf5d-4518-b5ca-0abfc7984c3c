package com.pacto.relatorioms.adapters;

import com.pacto.relatorioms.dto.ArmarioDTO;
import com.pacto.relatorioms.entities.Armario;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class ArmarioAdapter implements AdapterInterface<Armario, ArmarioDTO> {

    @Autowired
    TamanhoArmarioAdapter tamanhoArmarioAdapter;

    @Override
    public ArmarioDTO toDto(Armario armario) {
        if (armario != null) {
            ArmarioDTO armarioDTO = new ArmarioDTO();
            armarioDTO.setCodigo(armarioDTO.getCodigo());
            armarioDTO.setDescricao(armario.getDescricao());
            armarioDTO.setGrupo(armario.getGrupo());
            armarioDTO.setTamanhoArmario(tamanhoArmarioAdapter.toDto(armario.getTamanhoArmario()));
            return armarioDTO;
        }
        return null;
    }
}
