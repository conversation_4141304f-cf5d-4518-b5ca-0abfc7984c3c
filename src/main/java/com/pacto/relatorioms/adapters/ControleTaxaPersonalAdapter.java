package com.pacto.relatorioms.adapters;

import com.pacto.relatorioms.dto.ControleTaxaPersonalDTO;
import com.pacto.relatorioms.entities.ControleTaxaPersonal;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class ControleTaxaPersonalAdapter implements AdapterInterface<ControleTaxaPersonal, ControleTaxaPersonalDTO> {

    @Autowired
    ColaboradorAdapter colaboradorAdapter;

    @Override
    public ControleTaxaPersonalDTO toDto(ControleTaxaPersonal controleTaxaPersonal) {
        if (controleTaxaPersonal != null) {
            ControleTaxaPersonalDTO controleTaxaPersonalDTO = new ControleTaxaPersonalDTO();
            controleTaxaPersonalDTO.setCodigo(controleTaxaPersonal.getCodigo());
            controleTaxaPersonalDTO.setEmpresa(controleTaxaPersonal.getEmpresa());
            controleTaxaPersonalDTO.setDataRegistro(controleTaxaPersonal.getDataRegistro());
            controleTaxaPersonalDTO.setDataInicioVigenciaPlano(controleTaxaPersonal.getDataInicioVigenciaPlano());
            controleTaxaPersonalDTO.setDataFimVigenciaPlano(controleTaxaPersonal.getDataFimVigenciaPlano());
            controleTaxaPersonalDTO.setCancelado(controleTaxaPersonal.getCancelado());
            controleTaxaPersonalDTO.setDataCancelamento(controleTaxaPersonal.getDataCancelamento());
            controleTaxaPersonalDTO.setResponsavelCancelamento(controleTaxaPersonal.getResponsavelCancelamento());
            controleTaxaPersonalDTO.setPersonal(colaboradorAdapter.toDto(controleTaxaPersonal.getPersonal()));
            return controleTaxaPersonalDTO;
        }
        return null;
    }
}
