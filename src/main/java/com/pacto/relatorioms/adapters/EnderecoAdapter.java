package com.pacto.relatorioms.adapters;

import com.pacto.relatorioms.dto.EnderecoDTO;
import com.pacto.relatorioms.entities.Endereco;
import org.springframework.stereotype.Component;

@Component
public class EnderecoAdapter implements AdapterInterface<Endereco, EnderecoDTO> {

    @Override
    public EnderecoDTO toDto(Endereco end) {
        if (end != null) {
            EnderecoDTO endDTO = new EnderecoDTO();
            endDTO.setCodigo(end.getCodigo());
            endDTO.setEndereco(end.getEndereco());
            endDTO.setBairro(end.getBairro());
            endDTO.setNumero(end.getNumero());
            endDTO.setComplemento(end.getComplemento());
            endDTO.setCep(end.getCep());
            endDTO.setPessoa(end.getPessoa().getCodigo());
            return endDTO;
        }
        return null;
    }
}
