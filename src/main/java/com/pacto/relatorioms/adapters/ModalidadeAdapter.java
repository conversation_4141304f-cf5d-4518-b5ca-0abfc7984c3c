package com.pacto.relatorioms.adapters;

import com.pacto.relatorioms.dto.ModalidadeDTO;
import com.pacto.relatorioms.entities.Modalidade;
import org.springframework.stereotype.Component;

@Component
public class ModalidadeAdapter implements AdapterInterface<Modalidade, ModalidadeDTO> {


    public ModalidadeAdapter() {
    }

    @Override
    public ModalidadeDTO toDto(Modalidade modalidade) {
        if (modalidade != null) {
            ModalidadeDTO modalidadeDTO = new ModalidadeDTO();
            modalidadeDTO.setCodigo(modalidade.getCodigo());
            modalidadeDTO.setNome(modalidade.getNome());
            return modalidadeDTO;
        }
        return null;
    }

    @Override
    public Modalidade toEntity(ModalidadeDTO modalidadeDTO) {
        if(modalidadeDTO != null) {
            Modalidade modalidade = new Modalidade();
            modalidade.setCodigo(modalidadeDTO.getCodigo());
            modalidade.setNome(modalidadeDTO.getNome());
            return modalidade;
        }
        return null;
    }
}
