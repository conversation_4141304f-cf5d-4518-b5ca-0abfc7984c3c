package com.pacto.relatorioms.adapters;

import com.pacto.relatorioms.dto.TelefoneDTO;
import com.pacto.relatorioms.entities.Telefone;
import org.springframework.stereotype.Component;

@Component
public class TelefonesAdapter implements AdapterInterface<Telefone, TelefoneDTO> {

    @Override
    public TelefoneDTO toDto(Telefone telefone) {
        if (telefone != null) {
            TelefoneDTO telefoneDTO = new TelefoneDTO();
            telefoneDTO.setCodigo(telefone.getCodigo());
            telefoneDTO.setNumero(telefone.getNumero());
            telefoneDTO.setDescricao(telefone.getDescricao());
            telefoneDTO.setTipoTelefone(telefone.getTipoTelefone());
            telefoneDTO.setPessoa(telefone.getPessoa().getCodigo());
            return telefoneDTO;
        }
        return null;
    }
}
