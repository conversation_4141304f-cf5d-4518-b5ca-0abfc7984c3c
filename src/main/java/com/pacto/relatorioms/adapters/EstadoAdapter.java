package com.pacto.relatorioms.adapters;


import com.pacto.relatorioms.dto.EstadoDTO;
import com.pacto.relatorioms.entities.empresa.Estado;
import org.springframework.stereotype.Component;

@Component
public class EstadoAdapter implements AdapterInterface<Estado, EstadoDTO> {

    @Override
    public EstadoDTO toDto(Estado estado) {
        if (estado != null) {
            EstadoDTO dto = new EstadoDTO();
            dto.setCodigo(estado.getCodigo());
            dto.setNome(estado.getNome());
            dto.setSigla(estado.getSigla());
            return dto;
        }
        return null;
    }

    @Override
    public Estado toEntity(EstadoDTO estadoDTO) {
        if (estadoDTO != null) {
            Estado estado = new Estado();
            estado.setCodigo(estadoDTO.getCodigo());
            estado.setNome(estadoDTO.getNome());
            estado.setSigla(estadoDTO.getSigla());
            return estado;
        }
        return null;
    }
}
