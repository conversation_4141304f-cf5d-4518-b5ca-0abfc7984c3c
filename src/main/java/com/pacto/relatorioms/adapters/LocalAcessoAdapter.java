package com.pacto.relatorioms.adapters;

import com.pacto.relatorioms.dto.LocalAcessoDTO;
import com.pacto.relatorioms.entities.LocalAcesso;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class LocalAcessoAdapter implements AdapterInterface<LocalAcesso, LocalAcessoDTO> {

    @Autowired
    EmpresaAdapter empresaAdapter;

    public LocalAcessoAdapter() {
    }

    @Override
    public LocalAcessoDTO toDto(LocalAcesso localAcesso) {
        if (localAcesso != null) {
            LocalAcessoDTO localAcessoDTO = new LocalAcessoDTO();
            localAcessoDTO.setCodigo(localAcesso.getCodigo());
            localAcessoDTO.setDescricao(localAcesso.getDescricao());
            localAcessoDTO.setEmpresa(empresaAdapter.toDto(localAcesso.getEmpresa()));
            return localAcessoDTO;
        }
        return null;
    }
}
