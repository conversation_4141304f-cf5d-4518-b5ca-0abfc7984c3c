package com.pacto.relatorioms.adapters;

import com.pacto.relatorioms.dto.ColaboradorDTO;
import com.pacto.relatorioms.entities.Colaborador;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class ColaboradorAdapter implements AdapterInterface<Colaborador, ColaboradorDTO> {

    @Autowired
    private EmpresaAdapter empresaAdapter;
    @Autowired
    private PessoaAdapter pessoaAdapter;

    @Override
    public ColaboradorDTO toDto(Colaborador colaborador) {
        ColaboradorDTO colaboradorDTO = new ColaboradorDTO();
        colaboradorDTO.setCodigo(colaborador.getCodigo());
        colaboradorDTO.setEmpresa(empresaAdapter.toDto(colaborador.getEmpresa()));
        colaboradorDTO.setSituacao(colaborador.getSituacao());
        colaboradorDTO.setPessoa(pessoaAdapter.toDto(colaborador.getPessoa()));
        colaboradorDTO.setDiaVencimento(colaborador.getDiaVencimento());
        colaboradorDTO.setNome(colaborador.getPessoa().getNome());
        return colaboradorDTO;
    }

    @Override
    public Colaborador toEntity(ColaboradorDTO colaboradorDTO) {
        Colaborador colaborador = new Colaborador();
        colaborador.setCodigo(colaboradorDTO.getCodigo());
        colaborador.setEmpresa(empresaAdapter.toEntity(colaboradorDTO.getEmpresa()));
        colaborador.setSituacao(colaboradorDTO.getSituacao());
        colaborador.setPessoa(pessoaAdapter.toEntity(colaboradorDTO.getPessoa()));

        return colaborador;
    }
}
