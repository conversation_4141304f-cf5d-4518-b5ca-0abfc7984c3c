package com.pacto.relatorioms.adapters;

import com.pacto.relatorioms.dto.ProfissaoDTO;
import com.pacto.relatorioms.entities.Profissao;
import org.springframework.stereotype.Component;

@Component
public class ProfissaoAdapter implements AdapterInterface<Profissao, ProfissaoDTO> {

    @Override
    public ProfissaoDTO toDto(Profissao profissao) {
        if (profissao != null) {
            ProfissaoDTO profissaoDTO = new ProfissaoDTO();
            profissaoDTO.setCodigo(profissao.getCodigo());
            profissaoDTO.setDescricao(profissao.getDescricao());
            return profissaoDTO;
        }
        return null;
    }
}
