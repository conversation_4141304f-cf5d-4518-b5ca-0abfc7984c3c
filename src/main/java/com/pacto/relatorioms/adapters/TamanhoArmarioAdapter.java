package com.pacto.relatorioms.adapters;

import com.pacto.relatorioms.dto.TamanhoArmarioDTO;
import com.pacto.relatorioms.entities.TamanhoArmario;
import org.springframework.stereotype.Component;

@Component
public class TamanhoArmarioAdapter implements AdapterInterface<TamanhoArmario, TamanhoArmarioDTO> {

    @Override
    public TamanhoArmarioDTO toDto(TamanhoArmario tamanhoArmario) {
        if (tamanhoArmario != null) {
            TamanhoArmarioDTO tamanhoArmarioDTO = new TamanhoArmarioDTO();
            tamanhoArmarioDTO.setCodigo(tamanhoArmario.getCodigo());
            tamanhoArmarioDTO.setDescricao(tamanhoArmario.getDescricao());
            return tamanhoArmarioDTO;
        }
        return null;
    }
}
