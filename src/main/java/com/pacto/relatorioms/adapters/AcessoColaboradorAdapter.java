package com.pacto.relatorioms.adapters;

import com.pacto.relatorioms.dto.AcessoColaboradorDTO;
import com.pacto.relatorioms.entities.AcessoColaborador;
import com.pacto.relatorioms.enums.MeioIdentificacaoEnum;
import com.pacto.relatorioms.utils.UteisRelatorioMs;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class AcessoColaboradorAdapter implements AdapterInterface<AcessoColaborador, AcessoColaboradorDTO> {

    @Autowired
    ColaboradorAdapter colaboradorAdapter;
    @Autowired
    LocalAcessoAdapter localAcessoAdapter;
    @Autowired
    ColetorAdapter coletorAdapter;
    @Autowired
    UsuarioAdapter usuarioAdapter;

    public AcessoColaboradorAdapter() {
    }

    @Override
    public AcessoColaboradorDTO toDto(AcessoColaborador acc) {
        if (acc != null) {
            AcessoColaboradorDTO accDto = new AcessoColaboradorDTO();
            accDto.setCodigo(acc.getCodigo());
            accDto.setSentido(acc.getSentido());
            accDto.setDataHoraEntrada(acc.getDtHrEntrada());
            accDto.setDataHoraSaida(acc.getDtHrSaida());

            if (acc.getSentido().equals("E") && acc.getMeioIdentificacaoEntrada() > 0) {
                accDto.setMeioIdentificacaoEntrada(MeioIdentificacaoEnum.getMeioIdentificacao(acc.getMeioIdentificacaoEntrada()));
            } else if (acc.getSentido().equals("S") && acc.getMeioIdentificacaoEntrada() > 0) {
                accDto.setMeioIdentificacaoEntrada(MeioIdentificacaoEnum.getMeioIdentificacao(acc.getMeioIdentificacaoSaida()));
            }
            try {
                accDto.setIntervaloDataHoras(UteisRelatorioMs.getIntervaloHorasEntreDatas(acc.getDtHrEntrada(), acc.getDtHrSaida()));
            } catch (Exception ex){
                accDto.setIntervaloDataHoras("");
            }
            accDto.setColaborador(colaboradorAdapter.toDto(acc.getColaborador()));
            accDto.setColetor(coletorAdapter.toDto(acc.getColetor()));
            accDto.setLocalAcesso(localAcessoAdapter.toDto(acc.getLocalAcesso()));
            return accDto;
        }
        return null;
    }

}
