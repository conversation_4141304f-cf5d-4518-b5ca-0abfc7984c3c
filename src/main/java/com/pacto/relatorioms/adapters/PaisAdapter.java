package com.pacto.relatorioms.adapters;

import com.pacto.relatorioms.dto.CidadeDTO;
import com.pacto.relatorioms.dto.PaisDTO;
import com.pacto.relatorioms.entities.empresa.Cidade;
import com.pacto.relatorioms.entities.empresa.Pais;
import org.springframework.stereotype.Component;

@Component
public class PaisAdapter implements AdapterInterface<Pais, PaisDTO> {

    @Override
    public PaisDTO toDto(Pais pais) {
        if (pais != null) {
            PaisDTO paisDTO = new PaisDTO();
            paisDTO.setCodigo(pais.getCodigo());
            paisDTO.setNome(pais.getNome());
            paisDTO.setNacionalidade(pais.getNacionalidade());
            return paisDTO;
        }
        return null;
    }
}
