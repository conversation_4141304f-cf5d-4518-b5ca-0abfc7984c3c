package com.pacto.relatorioms.adapters;

import org.springframework.stereotype.Component;

import javax.persistence.Id;
import java.lang.reflect.Field;
import java.util.*;
import java.util.stream.Collectors;

@Component
public interface AdapterInterface<ENTITY, DTO> {

    default DTO toDto(ENTITY entity) {
        throw new UnsupportedOperationException("Method not implemented");
    }

    default ENTITY toEntity(DTO dto) {
        throw new UnsupportedOperationException("Method not implemented");
    }

    default Set<ENTITY> toEntities(List<DTO> dtos) {
        return dtos.stream().map(this::toEntity).collect(Collectors.toSet());
    }

    default List<DTO> toDtos(List<ENTITY> entities) {
        return entities.stream().map(this::toDto).collect(Collectors.toList());
    }

    default List<DTO> toDtos(Set<ENTITY> entities) {
        return entities.stream().map(this::toDto).collect(Collectors.toList());
    }

    default ENTITY toEntity(DTO dto, ENTITY entity) {
        throw new UnsupportedOperationException("Method not implemented");
    }

    default void addAll(Collection<DTO> dtos, Collection<ENTITY> entities) {
        final Field[] id = {null};
        final Field[] idDTO = {null};
        if (entities.size() > dtos.size()) {
            List<ENTITY> itemsToRemove = new ArrayList<>();
            entities.forEach(
                    entity -> {
                        ENTITY finalEntity = entity;
                        if (id[0] == null) {
                            try {
                                id[0] = entity.getClass().getDeclaredField("codigo");
                            } catch (NoSuchFieldException e) {
                                id[0] = Arrays.stream(entity.getClass().getDeclaredFields())
                                        .filter(f -> f.isAnnotationPresent(Id.class))
                                        .collect(Collectors.toList()).get(0);
                            }
                        }
                        List<DTO> dtos1 = dtos.stream()
                                .filter(dto -> {
                                    try {
                                        if (idDTO[0] == null) {
                                            try {
                                                idDTO[0] = dto.getClass().getDeclaredField("codigo");
                                            } catch (NoSuchFieldException e) {
                                                idDTO[0] = Arrays.stream(dto.getClass().getDeclaredFields())
                                                        .filter(f -> f.isAnnotationPresent(Id.class))
                                                        .collect(Collectors.toList()).get(0);
                                            }
                                        }
                                        id[0].setAccessible(true);
                                        idDTO[0].setAccessible(true);
                                        boolean r = Objects.equals(id[0].get(finalEntity), idDTO[0].get(dto));
                                        idDTO[0].setAccessible(false);
                                        id[0].setAccessible(false);
                                        return r;
                                    } catch (IllegalAccessException e) {
                                        e.printStackTrace();
                                    }
                                    return false;
                                })
                                .collect(Collectors.toList());
                        if (!dtos1.isEmpty()) {
                            entity = toEntity(dtos1.get(0));
                        } else {
                            itemsToRemove.add(entity);
                        }

                    }
            );
            entities.removeAll(itemsToRemove);
        } else {
            List<ENTITY> entitiesAux = new ArrayList<>();
            List<ENTITY> itemsRemoved = new ArrayList<>();
            dtos.forEach(
                    dto -> {
                        if (idDTO[0] == null) {
                            try {
                                idDTO[0] = dto.getClass().getDeclaredField("codigo");
                            } catch (NoSuchFieldException e) {
                                idDTO[0] = Arrays.stream(dto.getClass().getDeclaredFields()).filter(f -> f.isAnnotationPresent(Id.class)).collect(Collectors.toList()).get(0);
                            }
                        }
                        List<ENTITY> entities1 = entities.stream()
                                .filter(entity -> {
                                    if (id[0] == null) {
                                        try {
                                            id[0] = entity.getClass().getDeclaredField("codigo");
                                        } catch (NoSuchFieldException e) {
                                            id[0] = Arrays.stream(entity.getClass().getDeclaredFields()).filter(f -> f.isAnnotationPresent(Id.class)).collect(Collectors.toList()).get(0);
                                        }
                                    }
                                    try {
                                        idDTO[0].setAccessible(true);
                                        id[0].setAccessible(true);
                                        boolean valor = Objects.equals(id[0].get(entity), idDTO[0].get(dto));
                                        id[0].setAccessible(false);
                                        idDTO[0].setAccessible(false);
                                        return valor;
                                    } catch (IllegalAccessException e) {
                                        e.printStackTrace();
                                    }
                                    return false;
                                }).collect(Collectors.toList());
                        if (!entities1.isEmpty()) {
                            toEntity(dto, entities1.get(0));
                        } else {
                            if (dtos.size() == entities.size()) {
                                List<Integer> codigosDTOs = new ArrayList<>();
                                dtos.forEach(dtoNotNull -> {
                                    try {
                                        idDTO[0].setAccessible(true);
                                        if (idDTO[0].get(dtoNotNull) != null) {
                                            codigosDTOs.add((Integer) idDTO[0].get(dtoNotNull));
                                        }
                                        idDTO[0].setAccessible(false);
                                    } catch (IllegalAccessException e) {
                                        e.printStackTrace();
                                    }
                                });
                                entities.forEach(
                                        e -> {
                                            try {
                                                id[0].setAccessible(true);
                                                if (!codigosDTOs.contains((Integer) id[0].get(e))) {
                                                    itemsRemoved.add(e);
                                                }
                                                id[0].setAccessible(false);
                                            } catch (IllegalAccessException ex) {
                                                ex.printStackTrace();
                                            }
                                        }
                                );
                                entities.removeAll(itemsRemoved);
                            }
                            entitiesAux.add(toEntity(dto));
                        }
                    }
            );
            entities.addAll(entitiesAux);
        }
    }
}
