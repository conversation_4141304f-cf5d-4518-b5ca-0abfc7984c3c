package com.pacto.relatorioms.adapters;

import com.pacto.relatorioms.dto.MovProdutoDTO;
import com.pacto.relatorioms.entities.MovProduto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class MovProdutoAdapter implements AdapterInterface<MovProduto, MovProdutoDTO> {

    @Autowired
    ProdutoAdapter produtoAdapter;

    @Override
    public MovProdutoDTO toDto(MovProduto movProduto) {
        if (movProduto != null) {
            MovProdutoDTO movProdutoDTO = new MovProdutoDTO();
            movProdutoDTO.setCodigo(movProduto.getCodigo());
            movProdutoDTO.setDescricao(movProduto.getDescricao());
            movProdutoDTO.setSituacao(movProduto.getSituacao());
            movProdutoDTO.setQuitado(movProduto.getQuitado());
            movProdutoDTO.setDataInicioVigencia(movProduto.getDataInicioVigencia());
            movProdutoDTO.setDataFinalVigencia(movProduto.getDataFinalVigencia());
            movProdutoDTO.setDataLancamento(movProduto.getDataLancamento());
            movProdutoDTO.setMesReferencia(movProduto.getMesReferencia());
            movProdutoDTO.setAnoReferencia(movProduto.getAnoReferencia());
            movProdutoDTO.setTotalFinal(movProduto.getTotalFinal());
            movProdutoDTO.setPrecoUnitario(movProduto.getPrecoUnitario());
            movProdutoDTO.setValorFaturado(movProduto.getValorFaturado());
            movProdutoDTO.setValorDesconto(movProduto.getValorDesconto());
            movProdutoDTO.setQuantidade(movProduto.getQuantidade());
            movProdutoDTO.setProduto(produtoAdapter.toDto(movProduto.getProduto()));
            return movProdutoDTO;
        }
        return null;
    }
}
