package com.pacto.relatorioms.adapters;

import com.pacto.relatorioms.dto.AcessoClienteDTO;
import com.pacto.relatorioms.entities.AcessoCliente;
import com.pacto.relatorioms.enums.MeioIdentificacaoEnum;
import com.pacto.relatorioms.enums.SituacaoAcessoEnum;
import com.pacto.relatorioms.utils.UteisRelatorioMs;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class AcessoClienteAdapter implements AdapterInterface<AcessoCliente, AcessoClienteDTO> {

    @Autowired
    ClienteAdapter clienteAdapter;
    @Autowired
    LocalAcessoAdapter localAcessoAdapter;
    @Autowired
    ColetorAdapter coletorAdapter;
    @Autowired
    UsuarioAdapter usuarioAdapter;

    public AcessoClienteAdapter() {
    }

    @Override
    public AcessoClienteDTO toDto(AcessoCliente acc) {
        if (acc != null) {
            AcessoClienteDTO accDto = new AcessoClienteDTO();
            accDto.setCodigo(acc.getCodigo());
            accDto.setSentido(acc.getSentido());
            accDto.setDataHoraEntrada(acc.getDtHrEntrada());
            accDto.setDataHoraSaida(acc.getDtHrSaida());
            accDto.setDataRegistro(acc.getDataRegistro());
            accDto.setTipoAcesso(acc.getTipoAcesso());
            accDto.setTicket(acc.getTicket());
            try {
                accDto.setIntervaloDataHoras(UteisRelatorioMs.getIntervaloHorasEntreDatas(acc.getDtHrEntrada(), acc.getDtHrSaida()));
            } catch (Exception ex){
                accDto.setIntervaloDataHoras("");
            }
            if(!acc.getSituacao().isEmpty()) {
                accDto.setSituacao(SituacaoAcessoEnum.consultarPorId(acc.getSituacao()));
            }
            if (acc.getSentido().equals("S") && acc.getMeioIdentificacaoSaida() != null && acc.getMeioIdentificacaoSaida() > 0) {
                accDto.setMeioIdentificacaoEntrada(MeioIdentificacaoEnum.getMeioIdentificacao(acc.getMeioIdentificacaoSaida()));
            } else if (acc.getMeioIdentificacaoEntrada() != null && acc.getMeioIdentificacaoEntrada() > 0){
                accDto.setMeioIdentificacaoEntrada(MeioIdentificacaoEnum.getMeioIdentificacao(acc.getMeioIdentificacaoEntrada()));
            }
            accDto.setCliente(clienteAdapter.toDto(acc.getCliente()));
            accDto.setColetor(coletorAdapter.toDto(acc.getColetor()));
            accDto.setLocalAcesso(localAcessoAdapter.toDto(acc.getLocalAcesso()));
            accDto.setUsuario(usuarioAdapter.toDto(acc.getUsuario()));
            accDto.setNomeCodEmpresaAcessou(acc.getNomeCodEmpresaAcessou());
            accDto.setNomeCodEmpresaOrigem(acc.getNomeCodEmpresaOrigem());
            accDto.setNomeCpfEmailClienteOrigem(acc.getNomeCpfEmailClienteOrigem());
            return accDto;
        }
        return null;
    }

}
