package com.pacto.relatorioms.adapters;

import com.pacto.relatorioms.dto.DescontoDTO;
import com.pacto.relatorioms.entities.Desconto;
import org.springframework.stereotype.Component;

@Component
public class DescontoAdapter implements AdapterInterface<Desconto, DescontoDTO> {


    @Override
    public DescontoDTO toDto(Desconto desconto) {
        if (desconto != null) {
            DescontoDTO descontoDTO = new DescontoDTO();
            descontoDTO.setCodigo(desconto.getCodigo());
            descontoDTO.setDescricao(desconto.getDescricao());
            descontoDTO.setTipoProduto(desconto.getTipoProduto());
            descontoDTO.setTipoDesconto(desconto.getTipoDesconto());
            descontoDTO.setValor(desconto.getValor());
            descontoDTO.setAtivo(desconto.getAtivo());
            descontoDTO.setAplicarEmpresas(desconto.getAplicarEmpresas());
            return descontoDTO;
        }
        return null;
    }
}
