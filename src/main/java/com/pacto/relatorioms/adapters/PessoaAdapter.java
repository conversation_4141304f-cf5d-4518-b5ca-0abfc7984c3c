package com.pacto.relatorioms.adapters;

import com.pacto.relatorioms.dto.EnderecoDTO;
import com.pacto.relatorioms.dto.PaisDTO;
import com.pacto.relatorioms.dto.PessoaDTO;
import com.pacto.relatorioms.entities.Email;
import com.pacto.relatorioms.entities.Pessoa;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class PessoaAdapter implements AdapterInterface<Pessoa, PessoaDTO> {


    @Autowired
    EstadoAdapter estadoAdapter;
    @Autowired
    CidadeAdapter cidadeAdapter;
    @Autowired
    PaisAdapter paisAdapter;
    @Autowired
    EmailAdapter emailAdapter;
    @Autowired
    TelefonesAdapter telefonesAdapter;
    @Autowired
    ProfissaoAdapter profissaoAdapter;
    @Autowired
    EnderecoAdapter enderecoAdapter;

    @Override
    public PessoaDTO toDto(Pessoa pessoa) {
        if (pessoa != null) {
            PessoaDTO pessoaDTO = new PessoaDTO();
            pessoaDTO.setCodigo(pessoa.getCodigo());
            pessoaDTO.setNome(pessoa.getNome());
            pessoaDTO.setDataNasc(pessoa.getDataNasc());
            pessoaDTO.setSexo(pessoa.getSexo());
            pessoaDTO.setEstadoCivil(pessoa.getEstadoCivil());
            pessoaDTO.setCidade(cidadeAdapter.toDto(pessoa.getCidade()));
            pessoaDTO.setEstado(estadoAdapter.toDto(pessoa.getEstado()));
            pessoaDTO.setPais(paisAdapter.toDto(pessoa.getPais()));
            pessoaDTO.setEnderecos(enderecoAdapter.toDtos(pessoa.getEnderecos()));
            pessoaDTO.setEmails(emailAdapter.toDtos(pessoa.getEmails()));
            pessoaDTO.setTelefones(telefonesAdapter.toDtos(pessoa.getTelefones()));
            pessoaDTO.setProfissao(profissaoAdapter.toDto(pessoa.getProfissao()));
            pessoaDTO.setCfp(pessoa.getCfp());
            pessoaDTO.setRg(pessoa.getRg());
            pessoaDTO.setFotokey(pessoa.getFotokey());

            if(pessoaDTO.getEmails().size() > 0) {
                pessoaDTO.setEmail(pessoaDTO.getEmails().get(0).getEmail());
            }
            if(pessoaDTO.getTelefones().size() > 0) {
                pessoaDTO.setTelefone(pessoaDTO.getTelefones().get(0).getNumero());
            }
            if(pessoaDTO.getEnderecos().size() > 0) {
                pessoaDTO.setEndereco(formatarEndereco(pessoaDTO.getEnderecos().get(0)));
            }
            return pessoaDTO;
        }
        return null;
    }

    @Override
    public Pessoa toEntity(PessoaDTO pessoaDTO) {
        if (pessoaDTO != null) {
            Pessoa pessoa = new Pessoa();
            pessoa.setCodigo(pessoa.getCodigo());
            pessoa.setNome(pessoaDTO.getNome());
            return pessoa;
        }
        return null;
    }

    public String formatarEndereco(EnderecoDTO enderecoDTO) {
        String endereco = enderecoDTO.getEndereco();
        String bairro = enderecoDTO.getBairro();
        String numero = enderecoDTO.getNumero();
        return endereco + ", " + bairro + ", Nº " + numero;
    }
}
