package com.pacto.relatorioms.adapters;

import com.pacto.relatorioms.dto.AluguelArmarioDTO;
import com.pacto.relatorioms.entities.AluguelArmario;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class AluguelArmarioAdapter implements AdapterInterface<AluguelArmario, AluguelArmarioDTO> {

    @Autowired
    ClienteAdapter clienteAdapter;
    @Autowired
    MovProdutoAdapter movProdutoAdapter;
    @Autowired
    ArmarioAdapter armarioAdapter;
    @Autowired
    VendaAvulsaAdapter vendaAvulsaAdapter;

    @Override
    public AluguelArmarioDTO toDto(AluguelArmario aluguelArmario) {
        if (aluguelArmario != null) {
            AluguelArmarioDTO aluguelArmarioRelDTO = new AluguelArmarioDTO();
            aluguelArmarioRelDTO.setCodigo(aluguelArmario.getCodigo());
            aluguelArmarioRelDTO.setDataCadastro(aluguelArmario.getDataCadastro());
            aluguelArmarioRelDTO.setDataInicio(aluguelArmario.getDataInicio());
            aluguelArmarioRelDTO.setFimOriginal(aluguelArmario.getFimOriginal());
            aluguelArmarioRelDTO.setContratoAssinado(aluguelArmario.getContratoAssinado());
            aluguelArmarioRelDTO.setRelacionamentoRenovacao(aluguelArmario.getRelacionamentoRenovacao());
            aluguelArmarioRelDTO.setCliente(clienteAdapter.toDto(aluguelArmario.getCliente()));
            aluguelArmarioRelDTO.setMovproduto(movProdutoAdapter.toDto(aluguelArmario.getMovproduto()));
            aluguelArmarioRelDTO.setArmario(armarioAdapter.toDto(aluguelArmario.getArmario()));
            aluguelArmarioRelDTO.setVendaavulsa(vendaAvulsaAdapter.toDto(aluguelArmario.getVendaavulsa()));
            return aluguelArmarioRelDTO;
        }
        return null;
    }
}