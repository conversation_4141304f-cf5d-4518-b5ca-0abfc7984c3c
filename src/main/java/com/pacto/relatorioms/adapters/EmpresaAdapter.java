package com.pacto.relatorioms.adapters;

import com.pacto.relatorioms.dto.EmpresaDTO;
import com.pacto.relatorioms.entities.empresa.Empresa;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class EmpresaAdapter implements AdapterInterface<Empresa, EmpresaDTO> {

    @Autowired
    CidadeAdapter cidadeAdapter;
    @Autowired
    EstadoAdapter estadoAdapter;

    public EmpresaAdapter() {
    }

    @Override
    public EmpresaDTO toDto(Empresa empresa) {
        if (empresa != null) {
            EmpresaDTO empresaDTO = new EmpresaDTO();
            empresaDTO.setCodigo(empresa.getCodigo());
            empresaDTO.setNome(empresa.getNome());
            empresaDTO.setAtiva(empresa.isAtiva());
            empresaDTO.setMoeda(empresa.getMoeda());
            empresaDTO.setCnpj(empresa.getCnpj());
            empresaDTO.setTelComercial1(empresa.getTelComercial1());
            empresaDTO.setTelComercial2(empresa.getTelComercial2());
            empresaDTO.setTelComercial3(empresa.getTelComercial3());
            empresaDTO.setEndereco(empresa.getEndereco());
            empresaDTO.setNumero(empresa.getNumero());
            empresaDTO.setComplemento(empresa.getComplemento());
            empresaDTO.setSetor(empresa.getSetor());
            empresaDTO.setCep(empresa.getCep());
            empresaDTO.setSite(empresa.getSite());
            empresaDTO.setCidade(cidadeAdapter.toDto(empresa.getCidade()));
            empresaDTO.setEstado(estadoAdapter.toDto(empresa.getEstado()));
            return empresaDTO;
        }
        return null;
    }

    @Override
    public Empresa toEntity(EmpresaDTO empresaDTO) {
        if (empresaDTO != null) {
            Empresa empresa = new Empresa();
            empresa.setCodigo(empresaDTO.getCodigo());
            empresa.setNome(empresaDTO.getNome());
            empresa.setAtiva(empresaDTO.isAtiva());
            return empresa;
        }
        return null;
    }
}
