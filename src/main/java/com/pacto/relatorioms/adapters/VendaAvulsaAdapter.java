package com.pacto.relatorioms.adapters;

import com.pacto.relatorioms.dto.VendaAvulsaDTO;
import com.pacto.relatorioms.entities.VendaAvulsa;
import org.springframework.stereotype.Component;

@Component
public class VendaAvulsaAdapter implements AdapterInterface<VendaAvulsa, VendaAvulsaDTO> {

    @Override
    public VendaAvulsaDTO toDto(VendaAvulsa vendaAvulsa) {
        if (vendaAvulsa != null) {
            VendaAvulsaDTO vendaAvulsaDTO = new VendaAvulsaDTO();
            vendaAvulsaDTO.setCodigo(vendaAvulsa.getCodigo());
            return vendaAvulsaDTO;
        }
        return null;
    }
}
