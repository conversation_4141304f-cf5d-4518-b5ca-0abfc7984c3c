package com.pacto.relatorioms.adapters;

import com.pacto.relatorioms.dto.EmailDTO;
import com.pacto.relatorioms.entities.Email;
import org.springframework.stereotype.Component;

@Component
public class EmailAdapter implements AdapterInterface<Email, EmailDTO> {

    @Override
    public EmailDTO toDto(Email email) {
        if (email != null) {
            EmailDTO emailDTO = new EmailDTO();
            emailDTO.setCodigo(email.getCodigo());
            emailDTO.setEmail(email.getEmail());
            emailDTO.setEmailCorrespondencia(email.getEmailCorrespondencia());
            emailDTO.setReceberEmailNovidades(email.getReceberEmailNovidades());
            emailDTO.setBloqueadoBounce(email.getBloqueadoBounce());
            return emailDTO;
        }
        return null;
    }
}
