package com.pacto.relatorioms.adapters;

import com.pacto.relatorioms.dto.ItemTaxaPersonalDTO;
import com.pacto.relatorioms.entities.ItemTaxaPersonal;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class ItemTaxaPersonalAdapter implements AdapterInterface<ItemTaxaPersonal, ItemTaxaPersonalDTO> {

    @Autowired
    ClienteAdapter clienteAdapter;
    @Autowired
    MovProdutoAdapter movProdutoAdapter;
    @Autowired
    ProdutoAdapter produtoAdapter;
    @Autowired
    DescontoAdapter descontoAdapter;

    @Override
    public ItemTaxaPersonalDTO toDto(ItemTaxaPersonal itemTaxaPersonal) {
        if (itemTaxaPersonal != null) {
            ItemTaxaPersonalDTO itemTaxaPersonalDTO = new  ItemTaxaPersonalDTO();
            itemTaxaPersonalDTO.setCodigo(itemTaxaPersonal.getCodigo());
            itemTaxaPersonalDTO.setDescontoEspecifico(itemTaxaPersonal.getDescontoEspecifico());
            itemTaxaPersonalDTO.setAluno(clienteAdapter.toDto(itemTaxaPersonal.getAluno()));
            itemTaxaPersonalDTO.setProduto(produtoAdapter.toDto(itemTaxaPersonal.getProduto()));
            itemTaxaPersonalDTO.setDesconto(descontoAdapter.toDto(itemTaxaPersonal.getDesconto()));
            itemTaxaPersonalDTO.setMovProduto(movProdutoAdapter.toDto(itemTaxaPersonal.getMovProduto()));
            return itemTaxaPersonalDTO;
        }
        return null;
    }
}
