package com.pacto.relatorioms.adapters;

import com.pacto.relatorioms.dto.LogDTO;
import com.pacto.relatorioms.entities.Log;
import org.springframework.stereotype.Component;

@Component
public class LogAdapter implements AdapterInterface<Log, LogDTO> {

    @Override
    public LogDTO toDto(Log log) {
        LogDTO logDTO = new LogDTO();
        logDTO.setCodigo(logDTO.getCodigo());
        logDTO.setNomeEntidade(logDTO.getNomeEntidade());
        logDTO.setNomeEntidadeDescricao(logDTO.getNomeEntidadeDescricao());
        logDTO.setChavePrimaria(logDTO.getChavePrimaria());
        logDTO.setChavePrimariaEntidadeSubordinada(logDTO.getChavePrimariaEntidadeSubordinada());
        logDTO.setNomeCampo(logDTO.getNomeCampo());
        logDTO.setValorCampoAnterior(logDTO.getValorCampoAnterior());
        logDTO.setValorCampoAlterado(logDTO.getValorCampoAlterado());
        logDTO.setDataAlteracao(logDTO.getDataAlteracao());
        logDTO.setResponsavelAlteracao(logDTO.getResponsavelAlteracao());
        logDTO.setOperacao(logDTO.getOperacao());
        logDTO.setPessoa(logDTO.getPessoa());
        logDTO.setCliente(logDTO.getCliente());
        return logDTO;
    }

    @Override
    public Log toEntity(LogDTO logDTO) {
        Log log = new Log();
        log.setCodigo(logDTO.getCodigo());
        log.setNomeEntidade(logDTO.getNomeEntidade());
        log.setNomeEntidadeDescricao(logDTO.getNomeEntidadeDescricao());
        log.setChavePrimaria(logDTO.getChavePrimaria());
        log.setChavePrimariaEntidadeSubordinada(logDTO.getChavePrimariaEntidadeSubordinada());
        log.setNomeCampo(logDTO.getNomeCampo());
        log.setValorCampoAnterior(logDTO.getValorCampoAnterior());
        log.setValorCampoAlterado(logDTO.getValorCampoAlterado());
        log.setDataAlteracao(logDTO.getDataAlteracao());
        log.setResponsavelAlteracao(logDTO.getResponsavelAlteracao());
        log.setOperacao(logDTO.getOperacao());
        log.setPessoa(logDTO.getPessoa());
        log.setCliente(logDTO.getCliente());
        return log;
    }
}
