package com.pacto.relatorioms.adapters;

import com.pacto.relatorioms.dto.PeriodoAcessoClienteDTO;
import com.pacto.relatorioms.entities.PeriodoAcessoCliente;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class PeriodoAcessoClienteAdapter implements AdapterInterface<PeriodoAcessoCliente, PeriodoAcessoClienteDTO> {

    @Autowired
    private PessoaAdapter pessoaAdapter;

    public PeriodoAcessoClienteAdapter() {
    }

    @Override
    public PeriodoAcessoClienteDTO toDto(PeriodoAcessoCliente pa) {
        if (pa != null) {
            PeriodoAcessoClienteDTO paDTO = new PeriodoAcessoClienteDTO();
            paDTO.setCodigo(pa.getCodigo());
            paDTO.setPessoa(pessoaAdapter.toDto(pa.getPessoa()));
            paDTO.setContrato(pa.getContrato());
            paDTO.setAulaAvulsaDiaria(pa.getAulaAvulsaDiaria());
            paDTO.setContratoBaseadoRenovacao(paDTO.getContratoBaseadoRenovacao());
            paDTO.setDataInicioAcesso(pa.getDataInicioAcesso());
            paDTO.setDataFinalAcesso(pa.getDataFinalAcesso());
            paDTO.setTipoAcesso(pa.getTipoAcesso());
            paDTO.setResponsavel(pa.getResponsavel());
            paDTO.setDataLancamento(pa.getDataLancamento());
            paDTO.setTokenGymPass(pa.getTokenGymPass());
            paDTO.setTipoGymPass(pa.getTipoGymPass());
            paDTO.setReposicao(pa.getReposicao());
            paDTO.setValorGympass(pa.getValorGympass());

            if(!pa.getTokenGymPass().isEmpty()) {
                StringBuilder descricao = new StringBuilder();
                descricao.append("Gympass").append(" - ").append(" Token: ").append(pa.getTokenGymPass());
                paDTO.setDescricaoTipoGymPass(descricao.toString());
            }

            if(!pa.getTokenGogood().isEmpty()) {
                StringBuilder descricao = new StringBuilder();
                descricao.append("Gogood").append(" - ").append("Token: ").append(pa.getTokenGogood());
                paDTO.setDescricaoTipoGogood(descricao.toString());
            }
            return paDTO;
        }
        return null;
    }

    @Override
    public PeriodoAcessoCliente toEntity(PeriodoAcessoClienteDTO paDTO) {
        if (paDTO != null) {
            PeriodoAcessoCliente pa = new PeriodoAcessoCliente();
            pa.setCodigo(paDTO.getCodigo());
            pa.setPessoa(pessoaAdapter.toEntity(paDTO.getPessoa()));
            pa.setContrato(paDTO.getContrato());
            pa.setAulaAvulsaDiaria(paDTO.getAulaAvulsaDiaria());
            pa.setContratoBaseadoRenovacao(paDTO.getContratoBaseadoRenovacao());
            pa.setDataInicioAcesso(paDTO.getDataInicioAcesso());
            pa.setDataFinalAcesso(paDTO.getDataFinalAcesso());
            pa.setTipoAcesso(paDTO.getTipoAcesso());
            pa.setResponsavel(paDTO.getResponsavel());
            pa.setDataLancamento(paDTO.getDataLancamento());
            pa.setTokenGymPass(paDTO.getTokenGymPass());
            pa.setReposicao(paDTO.getReposicao());
            pa.setValorGympass(paDTO.getValorGympass());
            return pa;
        }
        return null;
    }
}
