package com.pacto.relatorioms.adapters;

import com.pacto.relatorioms.dto.ProdutoDTO;
import com.pacto.relatorioms.entities.Produto;
import org.springframework.stereotype.Component;

@Component
public class ProdutoAdapter implements AdapterInterface<Produto, ProdutoDTO> {


    public ProdutoAdapter() {
    }

    @Override
    public ProdutoDTO toDto(Produto produto) {
        if (produto != null) {
            ProdutoDTO produtoDTO = new ProdutoDTO();
            produtoDTO.setCodigo(produto.getCodigo());
            produtoDTO.setDescricao(produto.getDescricao());
            produtoDTO.setValorFinal(produto.getValorFinal());
            produtoDTO.setNrdiasVigencia(produto.getNrdiasVigencia());
            produtoDTO.setTipoProduto(produto.getTipoProduto());
            return produtoDTO;
        }
        return null;
    }
}
