package com.pacto.relatorioms.adapters;

import com.pacto.relatorioms.dto.PlanoDTO;
import com.pacto.relatorioms.entities.Plano;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class PlanoAdapter implements AdapterInterface<Plano, PlanoDTO> {

    public PlanoAdapter() {
    }

    @Override
    public PlanoDTO toDto(Plano plano) {
        if (plano != null) {
            PlanoDTO planoDTO = new PlanoDTO();
            planoDTO.setCodigo(plano.getCodigo());
            planoDTO.setDescricao(plano.getDescricao());
            planoDTO.setEmpresa(plano.getEmpresa());
            return planoDTO;
        }
        return null;
    }

    @Override
    public Plano toEntity(PlanoDTO planoDTO) {
        if (planoDTO != null) {
            Plano plano = new Plano();
            plano.setCodigo(planoDTO.getCodigo());
            plano.setDescricao(planoDTO.getDescricao());
            plano.setEmpresa(plano.getEmpresa());
            return plano;
        }
        return null;
    }
}
