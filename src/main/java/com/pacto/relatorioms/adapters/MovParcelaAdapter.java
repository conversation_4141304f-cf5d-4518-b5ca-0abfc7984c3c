package com.pacto.relatorioms.adapters;

import com.pacto.relatorioms.dto.base.MovParcelaDTO;
import com.pacto.relatorioms.entities.MovParcela;
import org.springframework.stereotype.Component;

@Component
public class MovParcelaAdapter implements AdapterInterface<MovParcela, MovParcelaDTO> {

    public MovParcelaAdapter() {
    }

    @Override
    public MovParcelaDTO toDto(MovParcela movParcela) {
        if (movParcela != null) {
            MovParcelaDTO movParcelaDTO = new MovParcelaDTO();
            movParcelaDTO.setCodigo(movParcela.getCodigo());
            movParcelaDTO.setSituacao(movParcela.getSituacao());
            movParcelaDTO.setDescricao(movParcela.getDescricao());
            movParcelaDTO.setValorParcela(movParcela.getValorParcela());
            movParcelaDTO.setDataVencimento(movParcela.getDataVencimento());
            return movParcelaDTO;
        }
        return null;
    }
}
