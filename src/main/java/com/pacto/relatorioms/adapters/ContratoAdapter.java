package com.pacto.relatorioms.adapters;

import com.pacto.relatorioms.dto.ContratoDTO;
import com.pacto.relatorioms.entities.Contrato;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class ContratoAdapter implements AdapterInterface<Contrato, ContratoDTO> {

    @Autowired
    PessoaAdapter pessoaAdapter;
    @Autowired
    PlanoAdapter planoAdapter;

    @Override
    public ContratoDTO toDto(Contrato contrato) {
        if (contrato != null) {
            ContratoDTO contratoDTO = new ContratoDTO();
            contratoDTO.setCodigo(contrato.getCodigo());
            contratoDTO.setVigenciaDe(contrato.getVigenciaDe());
            contratoDTO.setVigenciaAteAjustada(contrato.getVigenciaAteAjustada());
            contratoDTO.setPlano(planoAdapter.toDto(contrato.getPlano()));
            return contratoDTO;
        }
        return null;
    }
}