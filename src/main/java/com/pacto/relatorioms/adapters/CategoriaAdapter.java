package com.pacto.relatorioms.adapters;

import com.pacto.relatorioms.dto.CategoriaDTO;
import com.pacto.relatorioms.entities.Categoria;
import org.springframework.stereotype.Component;

@Component
public class CategoriaAdapter implements AdapterInterface<Categoria, CategoriaDTO> {

    @Override
    public CategoriaDTO toDto(Categoria categoria) {
        if (categoria != null) {
            CategoriaDTO categoriaDTO = new CategoriaDTO();
            categoriaDTO.setCodigo(categoria.getCodigo());
            categoriaDTO.setNrConvitePermitido(categoria.getNrConvitePermitido());
            categoriaDTO.setTipoCategoria(categoria.getTipoCategoria());
            categoriaDTO.setNome(categoria.getNome());
            categoriaDTO.setTipoCategoriaClube(categoria.getTipoCategoriaClube());
            categoriaDTO.setTipoBloqueioInadimplencia(categoria.getTipoBloqueioInadimplencia());
            categoriaDTO.setNomeExterno(categoria.getNomeExterno());
            return categoriaDTO;
        }
        return null;
    }

    @Override
    public Categoria toEntity(CategoriaDTO categoriaDTO) {
        if (categoriaDTO != null) {
            Categoria categoria = new Categoria();
            categoria.setCodigo(categoriaDTO.getCodigo());
            categoria.setNrConvitePermitido(categoriaDTO.getNrConvitePermitido());
            categoria.setTipoCategoria(categoriaDTO.getTipoCategoria());
            categoria.setNome(categoriaDTO.getNome());
            categoria.setTipoCategoriaClube(categoriaDTO.getTipoCategoriaClube());
            categoria.setTipoBloqueioInadimplencia(categoriaDTO.getTipoBloqueioInadimplencia());
            categoria.setNomeExterno(categoriaDTO.getNomeExterno());
            return categoria;
        }
        return null;
    }

}
