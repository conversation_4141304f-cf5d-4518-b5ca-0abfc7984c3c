package com.pacto.relatorioms.adapters;

import com.pacto.relatorioms.dto.UsuarioDTO;
import com.pacto.relatorioms.entities.Usuario;
import org.springframework.stereotype.Component;

@Component
public class UsuarioAdapter implements AdapterInterface<Usuario, UsuarioDTO> {

    public UsuarioAdapter() {
    }

    @Override
    public UsuarioDTO toDto(Usuario usuario) {
        if (usuario != null) {
            UsuarioDTO usuarioDTO = new UsuarioDTO();
            usuarioDTO.setCodigo(usuario.getCodigo());
            usuarioDTO.setNome(usuario.getNome());
            return usuarioDTO;
        }
        return null;
    }
}
