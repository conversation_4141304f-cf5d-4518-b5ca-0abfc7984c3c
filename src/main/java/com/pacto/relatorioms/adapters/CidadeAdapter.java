package com.pacto.relatorioms.adapters;

import com.pacto.relatorioms.dto.CidadeDTO;
import com.pacto.relatorioms.entities.empresa.Cidade;
import org.springframework.stereotype.Component;

@Component
public class CidadeAdapter implements AdapterInterface<Cidade, CidadeDTO> {
    private final EstadoAdapter estadoAdapter;

    public CidadeAdapter(EstadoAdapter estadoAdapter) {
        this.estadoAdapter = estadoAdapter;
    }

    @Override
    public CidadeDTO toDto(Cidade cidade) {
        if (cidade != null) {
            CidadeDTO dto = new CidadeDTO();
            dto.setCodigo(cidade.getCodigo());
            dto.setNome(cidade.getNome());
            dto.setEstado(estadoAdapter.toDto(cidade.getEstado()));
            return dto;
        }
        return null;
    }

    @Override
    public Cidade toEntity(CidadeDTO cidadeDTO) {
        if (cidadeDTO != null) {
            Cidade cidade = new Cidade();
            cidade.setCodigo(cidadeDTO.getCodigo());
            cidade.setNome(cidadeDTO.getNome());
            cidade.setEstado(estadoAdapter.toEntity(cidadeDTO.getEstado()));
            return cidade;
        }
        return null;
    }
}
