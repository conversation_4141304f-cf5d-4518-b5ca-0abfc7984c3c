package com.pacto.relatorioms.adapters;

import com.pacto.relatorioms.dto.ClienteDTO;
import com.pacto.relatorioms.entities.Cliente;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class ClienteAdapter implements AdapterInterface<Cliente, ClienteDTO> {

    @Autowired
    PessoaAdapter pessoaAdapter;
    @Autowired
    EmpresaAdapter empresaAdapter;
    @Autowired
    CategoriaAdapter categoriaAdapter;

    public ClienteAdapter() {
    }

    @Override
    public ClienteDTO toDto(Cliente cliente) {
        if (cliente != null) {
            ClienteDTO clienteDTO = new ClienteDTO();
            clienteDTO.setCodigo(cliente.getCodigo());
            clienteDTO.setPessoa(pessoaAdapter.toDto(cliente.getPessoa()));
            clienteDTO.setSituacao(cliente.getSituacao());
            clienteDTO.setCodigoMatricula(cliente.getCodigoMatricula());
            clienteDTO.setMatricula(cliente.getMatricula());
            clienteDTO.setEmpresa(empresaAdapter.toDto(cliente.getEmpresa()));
            clienteDTO.setCategoria(categoriaAdapter.toDto(cliente.getCategoria()));
            return clienteDTO;
        }
        return null;
    }

    @Override
    public Cliente toEntity(ClienteDTO clienteDTO) {
        if (clienteDTO != null) {
            Cliente cliente = new Cliente();
            cliente.setCodigo(clienteDTO.getCodigo());
            cliente.setSituacao(clienteDTO.getSituacao());
            cliente.setMatricula(clienteDTO.getMatricula());
            cliente.setCodigoMatricula(clienteDTO.getCodigoMatricula());
            cliente.setPessoa(pessoaAdapter.toEntity(clienteDTO.getPessoa()));
            cliente.setEmpresa(empresaAdapter.toEntity(clienteDTO.getEmpresa()));
            cliente.setCategoria(categoriaAdapter.toEntity(clienteDTO.getCategoria()));
        }
        return null;
    }
}
