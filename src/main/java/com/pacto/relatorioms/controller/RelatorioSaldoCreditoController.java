package com.pacto.relatorioms.controller;

import com.pacto.config.dto.PaginadorDTO;
import com.pacto.config.exceptions.ServiceException;
import com.pacto.relatorioms.filter.FiltroRelatorioSaldoCreditoJSON;
import com.pacto.relatorioms.services.interfaces.RelatorioSaldoCreditoService;
import com.pacto.config.utils.EnvelopeRespostaDTO;
import com.pacto.config.utils.ResponseEntityFactory;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/relatorio-saldo-credito")
public class RelatorioSaldoCreditoController {

    @Autowired
    private RelatorioSaldoCreditoService relatorioSaldoCreditoService;

    @GetMapping(value = "")
    public ResponseEntity<EnvelopeRespostaDTO> consultarSaldoCredito(@RequestParam(value = "filters", required = false) JSONObject filtros, PaginadorDTO paginadorDTO) {
        try {
            FiltroRelatorioSaldoCreditoJSON filtroRelatorioSaldoCreditoJSON = new FiltroRelatorioSaldoCreditoJSON(filtros);
             return ResponseEntityFactory.ok(relatorioSaldoCreditoService.consultarSaldoCredito(filtroRelatorioSaldoCreditoJSON, paginadorDTO), paginadorDTO);
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }
}

