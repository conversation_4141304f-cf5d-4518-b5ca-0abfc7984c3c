package com.pacto.relatorioms.controller;

import com.pacto.config.exceptions.ServiceException;
import com.pacto.relatorioms.filter.FiltroEmpresaJSON;
import com.pacto.relatorioms.filter.FiltroRelatorioVisitantesJSON;
import com.pacto.relatorioms.services.interfaces.EmpresaService;
import com.pacto.config.utils.EnvelopeRespostaDTO;
import com.pacto.config.utils.ResponseEntityFactory;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/empresas")
public class EmpresaController {

    @Autowired
    private EmpresaService empresaService;

    @GetMapping(value = "/{id}")
    public ResponseEntity<EnvelopeRespostaDTO> findEmpresaById(@PathVariable Integer id) {
        try {
            return ResponseEntityFactory.ok(empresaService.findById(id));
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    @GetMapping("/find-all-actives")
    public ResponseEntity<EnvelopeRespostaDTO> findEmpresas(@RequestParam(value = "filters", required = false) JSONObject filtros) {
        try {
            FiltroEmpresaJSON filtrosFiltroEmpresaJSON = new FiltroEmpresaJSON(filtros);
            return ResponseEntityFactory.ok(empresaService.findAllActives(filtrosFiltroEmpresaJSON));
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }
}
