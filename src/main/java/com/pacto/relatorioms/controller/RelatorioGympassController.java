package com.pacto.relatorioms.controller;

import com.pacto.config.dto.PaginadorDTO;
import com.pacto.config.exceptions.ServiceException;
import com.pacto.relatorioms.filter.FiltroRelatorioGympassJSON;
import com.pacto.relatorioms.services.interfaces.RelatorioGympassService;
import com.pacto.config.utils.EnvelopeRespostaDTO;
import com.pacto.config.utils.ResponseEntityFactory;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/relatorio-gympass")
public class RelatorioGympassController {

    @Autowired
    private RelatorioGympassService relatorioGympassService;

    @GetMapping(value = "")
    public ResponseEntity<EnvelopeRespostaDTO> findAllAlunosGympass(@RequestParam(value = "filters", required = false) JSONObject filtros,
                                                                      PaginadorDTO paginadorDTO) {
        try {
            FiltroRelatorioGympassJSON filtroRelatorioGympassJSON = new FiltroRelatorioGympassJSON(filtros);
             return ResponseEntityFactory.ok(relatorioGympassService.consultarAlunosGympass(filtroRelatorioGympassJSON, paginadorDTO), paginadorDTO);
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }
}

