package com.pacto.relatorioms.controller;

import com.pacto.config.exceptions.ServiceException;
import com.pacto.relatorioms.filter.FiltroModalidadeJSON;
import com.pacto.relatorioms.services.interfaces.ModalidadeService;
import com.pacto.config.utils.EnvelopeRespostaDTO;
import com.pacto.config.utils.ResponseEntityFactory;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/modalidades")
public class ModalidadeController {

    @Autowired
    private ModalidadeService modalidadeService;

    @GetMapping(value = "/find-all")
    public ResponseEntity<EnvelopeRespostaDTO> findAll(@RequestParam(value = "filters", required = false) JSONObject filtros) {
        try {
            FiltroModalidadeJSON filtroModalidadeJSON = new FiltroModalidadeJSON(filtros);
            return ResponseEntityFactory.ok(modalidadeService.findAll(filtroModalidadeJSON));
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }
}
