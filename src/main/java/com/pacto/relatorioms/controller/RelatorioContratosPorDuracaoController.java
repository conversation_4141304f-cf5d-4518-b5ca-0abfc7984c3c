package com.pacto.relatorioms.controller;

import com.pacto.relatorioms.dto.RelatorioContratosPorDuracaoDTO;
import com.pacto.config.dto.PaginadorDTO;
import com.pacto.config.exceptions.ServiceException;
import com.pacto.relatorioms.filter.FiltroRelatorioContratoPorDuracaoJSON;
import com.pacto.relatorioms.services.interfaces.RelatorioContratosPorDuracaoService;
import com.pacto.config.utils.EnvelopeRespostaDTO;
import com.pacto.config.utils.ResponseEntityFactory;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/contratos-por-duracao")
public class RelatorioContratosPorDuracaoController {

    @Autowired
    private RelatorioContratosPorDuracaoService relatorioContratosPorDuracaoService;

    @GetMapping(value = "")
    public ResponseEntity<EnvelopeRespostaDTO> consultarContratosPorDuracao(@RequestParam(value = "filters", required = false) JSONObject filtros, PaginadorDTO paginadorDTO) {
        try {
            FiltroRelatorioContratoPorDuracaoJSON filtrosJson = new FiltroRelatorioContratoPorDuracaoJSON(filtros);
            RelatorioContratosPorDuracaoDTO relatorioContratosPorDuracaoDTO = relatorioContratosPorDuracaoService.consultarContratosPorDuracao(filtrosJson, paginadorDTO);
            if (filtrosJson.getShare()) {
                return ResponseEntityFactory.ok(relatorioContratosPorDuracaoDTO.getContratosPorDuracao(), paginadorDTO);
            } else {
                return ResponseEntityFactory.ok(relatorioContratosPorDuracaoDTO, relatorioContratosPorDuracaoDTO.getContratosPorDuracao(), paginadorDTO);
            }
        } catch (ServiceException e) {
            return ResponseEntityFactory.mensagemFront(e.getChaveExcecao(), e.getMessage());
        } catch (Exception e) {
            StringBuilder result = new StringBuilder(e.toString() + "\n");
            StackTraceElement[] trace = e.getStackTrace();
            for (StackTraceElement stackTraceElement : trace) {
                result.append(stackTraceElement.toString()).append("\n");
            }
            return ResponseEntityFactory.erroInterno(e.getMessage(), result.toString());
        }
    }

    @GetMapping(value = "/{duracao}")
    public ResponseEntity<EnvelopeRespostaDTO> consultarClientesPorDuracao(@PathVariable Integer duracao, @RequestParam(value = "filters", required = false) JSONObject filtros, PaginadorDTO paginadorDTO) {
        try {
            FiltroRelatorioContratoPorDuracaoJSON filtrosJson = new FiltroRelatorioContratoPorDuracaoJSON(filtros);
            return ResponseEntityFactory.ok(relatorioContratosPorDuracaoService.consultarClientesPorDuracao(filtrosJson, duracao, paginadorDTO), paginadorDTO);
        } catch (ServiceException e) {
            return ResponseEntityFactory.mensagemFront(e.getChaveExcecao(), e.getMessage());
        } catch (Exception e) {
            StringBuilder result = new StringBuilder(e.toString() + "\n");
            StackTraceElement[] trace = e.getStackTrace();
            for (StackTraceElement stackTraceElement : trace) {
                result.append(stackTraceElement.toString()).append("\n");
            }
            return ResponseEntityFactory.erroInterno(e.getMessage(), result.toString());
        }
    }

}

