package com.pacto.relatorioms.controller;

import com.pacto.config.dto.PaginadorDTO;
import com.pacto.config.exceptions.ServiceException;
import com.pacto.relatorioms.filter.RelatorioArmarioFiltroJSON;
import com.pacto.relatorioms.services.interfaces.RelatorioArmarioService;
import com.pacto.config.utils.EnvelopeRespostaDTO;
import com.pacto.config.utils.ResponseEntityFactory;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/relatorio-armario")
public class RelatorioArmarioController {
    @Autowired
    RelatorioArmarioService relatorioArmarioService;

    @GetMapping(value = "")
    public ResponseEntity<EnvelopeRespostaDTO> consultarRelatorio(
            @RequestParam(value = "filters", required = false)JSONObject filters, PaginadorDTO paginadorDTO
    ) {
        try {
            RelatorioArmarioFiltroJSON relatorioArmarioFiltroJSON = new RelatorioArmarioFiltroJSON(filters);
            return ResponseEntityFactory.ok(relatorioArmarioService.consultarArmarios(relatorioArmarioFiltroJSON, paginadorDTO), paginadorDTO);
        }  catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }
}
