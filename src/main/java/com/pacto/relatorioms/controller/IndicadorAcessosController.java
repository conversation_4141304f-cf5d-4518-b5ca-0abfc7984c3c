package com.pacto.relatorioms.controller;

import com.pacto.relatorioms.filter.FiltroIndicadorAcessosJSON;
import com.pacto.relatorioms.services.interfaces.IndicadorAcessosService;
import com.pacto.config.utils.EnvelopeRespostaDTO;
import com.pacto.config.utils.ResponseEntityFactory;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/indicador-de-acessos")
public class IndicadorAcessosController {

    @Autowired
    private IndicadorAcessosService indicadorAcessosService;

    @GetMapping(value = "")
    public ResponseEntity<EnvelopeRespostaDTO> consultarIndicadorAcessos(@RequestParam(value = "filters", required = false) JSONObject filtros) {
        try {
            FiltroIndicadorAcessosJSON filtrosJson = new FiltroIndicadorAcessosJSON(filtros);
            return ResponseEntityFactory.ok(indicadorAcessosService.consultarIndicadorAcessosDia(filtrosJson));
        } catch (Exception e) {
            StringBuilder result = new StringBuilder(e.toString() + "\n");
            StackTraceElement[] trace = e.getStackTrace();
            for (StackTraceElement stackTraceElement : trace) {
                result.append(stackTraceElement.toString()).append("\n");
            }
            return ResponseEntityFactory.erroInterno(e.getMessage(), result.toString());
        }
    }
}
