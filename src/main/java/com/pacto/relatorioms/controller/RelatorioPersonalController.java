package com.pacto.relatorioms.controller;

import com.pacto.config.dto.PaginadorDTO;
import com.pacto.config.exceptions.ServiceException;
import com.pacto.relatorioms.filter.FiltroRelatorioPersonalJSON;
import com.pacto.relatorioms.services.interfaces.RelatorioPersonalService;
import com.pacto.config.utils.EnvelopeRespostaDTO;
import com.pacto.config.utils.ResponseEntityFactory;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/relatorio-de-personal")
public class RelatorioPersonalController {

    @Autowired
    private RelatorioPersonalService relatorioPersonalService ;

    @GetMapping(value = "")
    public ResponseEntity<EnvelopeRespostaDTO> consultarRelatorioPersonal(@RequestParam(value = "filters", required = false) JSONObject filtros,
                                                                          PaginadorDTO paginadorDTO) {
        try {
            FiltroRelatorioPersonalJSON filtroRelatorioPersonalJSON = new FiltroRelatorioPersonalJSON(filtros);
             return ResponseEntityFactory.ok(relatorioPersonalService.consultarRelatorioPersonal(filtroRelatorioPersonalJSON, paginadorDTO), paginadorDTO);
        } catch (Exception e){
            StringBuilder result = new StringBuilder(e.toString()+"/n");
            StackTraceElement[] trace = e.getStackTrace();
            for(StackTraceElement stackTraceElement : trace){
                result.append(stackTraceElement.toString()).append("/n");
            }
            return ResponseEntityFactory.erroInterno(e.getMessage(), result.toString());
        }
    }

    @GetMapping(value = "/totais")
    public ResponseEntity<EnvelopeRespostaDTO> consultarTotais(@RequestParam(value = "filters", required = false) JSONObject filtros) {
        try {
            FiltroRelatorioPersonalJSON filtroRelatorioPersonalJSON = new FiltroRelatorioPersonalJSON(filtros);
            return ResponseEntityFactory.ok(relatorioPersonalService.consultarTotais(filtroRelatorioPersonalJSON));
        } catch (Exception e){
            StringBuilder result = new StringBuilder(e.toString()+"/n");
            StackTraceElement[] trace = e.getStackTrace();
            for(StackTraceElement stackTraceElement : trace){
                result.append(stackTraceElement.toString()).append("/n");
            }
            return ResponseEntityFactory.erroInterno(e.getMessage(), result.toString());
        }
    }
}

