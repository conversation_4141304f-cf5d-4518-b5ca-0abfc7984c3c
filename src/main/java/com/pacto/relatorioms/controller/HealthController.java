package com.pacto.relatorioms.controller;

import com.pacto.config.security.interfaces.LeituraTokenServico;
import com.pacto.config.utils.EnvelopeRespostaDTO;
import com.pacto.config.utils.ResponseEntityFactory;
import com.pacto.config.utils.Uteis;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.io.File;

@RestController
@RequestMapping("/health")
public class HealthController {

    @Autowired
    private LeituraTokenServico tokenServico;

    @ResponseBody
    @RequestMapping(value = "", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> todos() {
        return ResponseEntityFactory.ok("it's alive! v2");
    }

    @GetMapping("secret")
    public ResponseEntity<EnvelopeRespostaDTO> secret() {
        try {
            return ResponseEntityFactory.ok(Uteis.readLineByLineJava8(tokenServico.getSecretKeyPath()));
        } catch (Exception e) {
            return ResponseEntityFactory.erroConhecido(e.getMessage(), e.getMessage());
        }
    }

    @GetMapping("getSecretKeyPath")
    public ResponseEntity<EnvelopeRespostaDTO> getSecretKeyPath() {
        try {
            JSONObject obj = new JSONObject();
            obj.put("retorno", tokenServico.getSecretKeyPath());

            File file = new File(tokenServico.getSecretKeyPath());
            obj.put("existe", file.exists());

            File pastassh = new File("/root/.ssh/");
            obj.put("existeSSH", pastassh.isDirectory());

            return ResponseEntityFactory.ok(obj.toString());
        } catch (Exception e) {
            return ResponseEntityFactory.erroConhecido(e.getMessage(), e.getMessage());
        }
    }

    @GetMapping("getAlgoritmo")
    public ResponseEntity<EnvelopeRespostaDTO> getAlgoritmo() {
        try {
            JSONObject obj = new JSONObject();
            obj.put("retorno", tokenServico.getAlgoritmo());
            return ResponseEntityFactory.ok(obj.toString());
        } catch (Exception e) {
            return ResponseEntityFactory.erroConhecido(e.getMessage(), e.getMessage());
        }
    }

    @GetMapping("getVerificador")
    public ResponseEntity<EnvelopeRespostaDTO> getVerificador() {
        try {
            JSONObject obj = new JSONObject();
            obj.put("retorno", tokenServico.getVerificador());
            return ResponseEntityFactory.ok(obj.toString());
        } catch (Exception e) {
            return ResponseEntityFactory.erroConhecido(e.getMessage(), e.getMessage());
        }
    }

    @GetMapping("getEmitente")
    public ResponseEntity<EnvelopeRespostaDTO> getEmitente() {
        try {
            JSONObject obj = new JSONObject();
            obj.put("retorno", tokenServico.getEmitente());
            return ResponseEntityFactory.ok(obj.toString());
        } catch (Exception e) {
            return ResponseEntityFactory.erroConhecido(e.getMessage(), e.getMessage());
        }
    }

    @GetMapping("getDecodedJWT")
    public ResponseEntity<EnvelopeRespostaDTO> getDecodedJWT(@RequestParam(required = false) String token) {
        JSONObject obj = new JSONObject();
        try {
            obj.put("retorno", tokenServico.getDecodedJWT(token));
            return ResponseEntityFactory.ok(obj.toString());
        } catch (Exception e) {
            return ResponseEntityFactory.erroConhecido(e.getMessage(), e.getMessage());
        }

    }
}
