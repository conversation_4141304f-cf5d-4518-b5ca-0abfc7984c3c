package com.pacto.relatorioms.controller;

import com.pacto.config.dto.PaginadorDTO;
import com.pacto.config.exceptions.ServiceException;
import com.pacto.relatorioms.filter.FiltroRelatorioProdutosVigenciaFilterJSON;
import com.pacto.relatorioms.services.interfaces.RelatorioProdutosVigenciaService;
import com.pacto.config.utils.EnvelopeRespostaDTO;
import com.pacto.config.utils.ResponseEntityFactory;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/relatorio-produtos-vigencia")
public class RelatorioProdutosVigenciaController {

    @Autowired
    private RelatorioProdutosVigenciaService relatorioProdutosVigenciaService;

    @GetMapping(value = "")
    public ResponseEntity<EnvelopeRespostaDTO> consultarSaldoCredito(@RequestParam(value = "filters", required = false) JSONObject filtros, PaginadorDTO paginadorDTO) throws Exception {
        try {
            FiltroRelatorioProdutosVigenciaFilterJSON filtroRelatorioProdutosVigenciaFilterJSON = new FiltroRelatorioProdutosVigenciaFilterJSON(filtros);
             return ResponseEntityFactory.ok(relatorioProdutosVigenciaService.consultarRelatorio(filtroRelatorioProdutosVigenciaFilterJSON, paginadorDTO), paginadorDTO);
        }catch (Exception e){
            StringBuilder result = new StringBuilder(e.toString()+"/n");
            StackTraceElement[] trace = e.getStackTrace();
            e.printStackTrace();
            for(StackTraceElement stackTraceElement : trace){
                result.append(stackTraceElement.toString()).append("/n");
            }
            return ResponseEntityFactory.erroInterno(e.getMessage(), result.toString());
        }
    }
}

