package com.pacto.relatorioms.controller;

import com.pacto.config.dto.PaginadorDTO;
import com.pacto.config.exceptions.ServiceException;
import com.pacto.relatorioms.services.interfaces.ItemTaxaPersonalService;
import com.pacto.config.utils.EnvelopeRespostaDTO;
import com.pacto.config.utils.ResponseEntityFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/itemTaxaPersonal")
public class ItemTaxaPersonalController {

    @Autowired
    private ItemTaxaPersonalService itemTaxaPersonalService;

    @GetMapping(value = "/{controleTaxaPersonal}")
    public ResponseEntity<EnvelopeRespostaDTO> consultarPorControleTaxaPersonal(@PathVariable Integer controleTaxaPersonal, PaginadorDTO paginadorDTO) {
        try {
            return ResponseEntityFactory.ok(itemTaxaPersonalService.consultarPorControleTaxaPersonal(controleTaxaPersonal, paginadorDTO), paginadorDTO);
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }
}
