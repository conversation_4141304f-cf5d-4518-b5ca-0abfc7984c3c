package com.pacto.relatorioms.controller;

import com.pacto.config.exceptions.ServiceException;
import com.pacto.relatorioms.filter.FiltroPlanoJSON;
import com.pacto.relatorioms.services.interfaces.PlanoService;
import com.pacto.config.utils.EnvelopeRespostaDTO;
import com.pacto.config.utils.ResponseEntityFactory;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/planos")
public class PlanoController {

    @Autowired
    private PlanoService planoService;

    @GetMapping(value = "/find-all")
    public ResponseEntity<EnvelopeRespostaDTO> findAll(@RequestParam(value = "filters", required = false) JSONObject filtros) {
        try {
            FiltroPlanoJSON filtroPlanoJSON = new FiltroPlanoJSON(filtros);
            return ResponseEntityFactory.ok(planoService.findAll(filtroPlanoJSON));
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }
}
