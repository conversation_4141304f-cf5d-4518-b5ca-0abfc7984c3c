package com.pacto.relatorioms.controller;

import com.pacto.config.dto.PaginadorDTO;
import com.pacto.config.exceptions.ServiceException;
import com.pacto.relatorioms.filter.FiltroProdutoJSON;
import com.pacto.relatorioms.services.interfaces.ProdutoService;
import com.pacto.config.utils.EnvelopeRespostaDTO;
import com.pacto.config.utils.ResponseEntityFactory;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/produtos")
public class ProdutoController {

    @Autowired
    private ProdutoService produtoService;


    @GetMapping(value = "")
    public ResponseEntity<EnvelopeRespostaDTO> findAllMin(@RequestParam(value = "filters", required = false) JSONObject filtros) throws Exception {
        try {
            FiltroProdutoJSON filtroProdutoJSON = new FiltroProdutoJSON(filtros);
            return ResponseEntityFactory.ok(produtoService.findAllMin(filtroProdutoJSON));
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }
}
