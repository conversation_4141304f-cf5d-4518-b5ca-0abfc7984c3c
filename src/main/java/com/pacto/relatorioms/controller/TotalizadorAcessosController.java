package com.pacto.relatorioms.controller;

import com.pacto.relatorioms.dto.RelatorioTotalizadorAcessosDTO;
import com.pacto.config.dto.PaginadorDTO;
import com.pacto.config.exceptions.ServiceException;
import com.pacto.relatorioms.filter.FiltroTotalizadorAcessosJSON;
import com.pacto.relatorioms.services.interfaces.TotalizadorAcessosService;
import com.pacto.config.utils.EnvelopeRespostaDTO;
import com.pacto.config.utils.ResponseEntityFactory;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/totalizador-de-acessos")
public class TotalizadorAcessosController {

    @Autowired
    private TotalizadorAcessosService totalizadorAcessosService;

    @GetMapping(value = "")
    public ResponseEntity<EnvelopeRespostaDTO> consultarTotalizadorAcessos(@RequestParam(value = "filters", required = false) JSONObject filtros, PaginadorDTO paginadorDTO) {
        try {
            FiltroTotalizadorAcessosJSON filtrosJson = new FiltroTotalizadorAcessosJSON(filtros);
            if (!filtrosJson.isShare()) {
                RelatorioTotalizadorAcessosDTO relatorioTotalizadorAcessosDTO = totalizadorAcessosService.consultarRelatorioTotalizadorAcessos(filtrosJson, paginadorDTO);
                return ResponseEntityFactory.ok(relatorioTotalizadorAcessosDTO, relatorioTotalizadorAcessosDTO.getTotalizadorAcessos(), paginadorDTO);
            } else {
                return ResponseEntityFactory.ok(totalizadorAcessosService.consultarListaTotalizadorAcessos(filtrosJson, paginadorDTO), paginadorDTO);
            }
        } catch (ServiceException e) {
            return ResponseEntityFactory.mensagemFront(e.getChaveExcecao(), e.getMessage());
        } catch (Exception e) {
            StringBuilder result = new StringBuilder(e.toString() + "\n");
            StackTraceElement[] trace = e.getStackTrace();
            for (StackTraceElement stackTraceElement : trace) {
                result.append(stackTraceElement.toString()).append("\n");
            }
            return ResponseEntityFactory.erroInterno(e.getMessage(), result.toString());
        }
    }

}

