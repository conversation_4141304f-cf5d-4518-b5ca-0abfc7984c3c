package com.pacto.relatorioms.controller;

import com.pacto.config.dto.PaginadorDTO;
import com.pacto.config.exceptions.ServiceException;
import com.pacto.relatorioms.filter.FiltroRelatorioConvidadosJSON;
import com.pacto.relatorioms.services.interfaces.RelatorioConvidadosService;
import com.pacto.config.utils.EnvelopeRespostaDTO;
import com.pacto.config.utils.ResponseEntityFactory;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/relatorio-de-convidados")
public class RelatorioConvidadosController {

    @Autowired
    private RelatorioConvidadosService relatorioConvidadosService;

    @GetMapping(value = "")
    public ResponseEntity<EnvelopeRespostaDTO> findAllConvidados(@RequestParam(value = "filters", required = false) JSONObject filtros,
                                                                 PaginadorDTO paginadorDTO) {
        try {
            FiltroRelatorioConvidadosJSON filtrosConvidados = new FiltroRelatorioConvidadosJSON(filtros);
             return ResponseEntityFactory.ok(relatorioConvidadosService.consultarAlunosConvidados(filtrosConvidados, paginadorDTO), paginadorDTO);
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }
}

