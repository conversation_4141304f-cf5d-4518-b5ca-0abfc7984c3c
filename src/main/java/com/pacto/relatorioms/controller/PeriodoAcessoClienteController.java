package com.pacto.relatorioms.controller;

import com.pacto.config.dto.PaginadorDTO;
import com.pacto.config.exceptions.ServiceException;
import com.pacto.config.utils.EnvelopeRespostaDTO;
import com.pacto.config.utils.ResponseEntityFactory;
import com.pacto.relatorioms.services.interfaces.PeriodoAcessoClienteService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/periodosAcessosCliente")
public class PeriodoAcessoClienteController {

    @Autowired
    private PeriodoAcessoClienteService periodoAcessoClienteService;

    @GetMapping(value = "/{matricula}")
    public ResponseEntity<EnvelopeRespostaDTO> findEmpresaById(@PathVariable Integer matricula, PaginadorDTO paginadorDTO) {
        try {
            return ResponseEntityFactory.ok(periodoAcessoClienteService.findAllByMatriculaRelGympass(matricula, paginadorDTO), paginadorDTO);
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    @GetMapping(value = "gogood/{matricula}")
    public ResponseEntity<EnvelopeRespostaDTO> findGogoodEmpresaById(@PathVariable Integer matricula, PaginadorDTO paginadorDTO) {
        try {
            return ResponseEntityFactory.ok(periodoAcessoClienteService.findAllByMatriculaRelGogood(matricula, paginadorDTO), paginadorDTO);
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }
}
