package com.pacto.relatorioms.controller;

import com.pacto.config.dto.PaginadorDTO;
import com.pacto.config.exceptions.ServiceException;
import com.pacto.relatorioms.filter.FiltroRelatorioVisitantesJSON;
import com.pacto.relatorioms.services.interfaces.RelatorioVisitantesService;
import com.pacto.config.utils.EnvelopeRespostaDTO;
import com.pacto.config.utils.ResponseEntityFactory;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/relatorio-de-visitantes")
public class RelatorioVisitantesController {

    @Autowired
    private RelatorioVisitantesService relatorioVisitantesService;

    @GetMapping(value = "")
    public ResponseEntity<EnvelopeRespostaDTO> findAllVisitantes(@RequestParam(value = "filters", required = false) JSONObject filtros,
                                                                 PaginadorDTO paginadorDTO) {
        try {
            FiltroRelatorioVisitantesJSON filtroRelatorioVisitantesJSON = new FiltroRelatorioVisitantesJSON(filtros);
             return ResponseEntityFactory.ok(relatorioVisitantesService.findAllVisitantes(filtroRelatorioVisitantesJSON, paginadorDTO), paginadorDTO);
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }
}

