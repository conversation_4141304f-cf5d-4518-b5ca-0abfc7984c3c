package com.pacto.relatorioms.controller;

import com.pacto.config.dto.PaginadorDTO;
import com.pacto.config.exceptions.ServiceException;
import com.pacto.relatorioms.filter.FiltroColaboradorJSON;
import com.pacto.relatorioms.services.interfaces.ColaboradorService;
import com.pacto.config.utils.EnvelopeRespostaDTO;
import com.pacto.config.utils.ResponseEntityFactory;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/colaboradores")
public class ColaboradorController {

    @Autowired
    private ColaboradorService colaboradorService;


    @RequestMapping(value = "/find-all", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> findAll(@RequestParam(value = "filters", required = false) JSONObject filtros, @RequestBody PaginadorDTO paginadorDTO) {
        try {
            FiltroColaboradorJSON filtroColaboradorJSON = new FiltroColaboradorJSON(filtros);
            return ResponseEntityFactory.ok(colaboradorService.findAll(filtroColaboradorJSON, paginadorDTO), paginadorDTO);
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    @RequestMapping(value = "", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> findAll(@RequestParam(value = "filters", required = false) JSONObject filtros) {
        try {
            FiltroColaboradorJSON filtroColaboradorJSON = new FiltroColaboradorJSON(filtros);
            return ResponseEntityFactory.ok(colaboradorService.findAll(filtroColaboradorJSON));
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    @GetMapping(value = "empresa/{empresaId}")
    public ResponseEntity<EnvelopeRespostaDTO> findAllByEmpresaId(@PathVariable Integer empresaId, @RequestParam(value = "filters", required = false) JSONObject filtros) {
        try {
            FiltroColaboradorJSON filtroColaboradorJSON = new FiltroColaboradorJSON(filtros);
            return ResponseEntityFactory.ok(colaboradorService.findAllByEmpresaId(empresaId, filtroColaboradorJSON));
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }
}
