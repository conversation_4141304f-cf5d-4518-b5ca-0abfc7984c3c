package com.pacto.relatorioms.controller;

import com.pacto.config.dto.PaginadorDTO;
import com.pacto.config.exceptions.ServiceException;
import com.pacto.config.utils.EnvelopeRespostaDTO;
import com.pacto.config.utils.ResponseEntityFactory;
import com.pacto.relatorioms.filter.FiltroRelatorioGogoodJSON;
import com.pacto.relatorioms.services.interfaces.RelatorioGogoodService;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/relatorio-gogood")
public class RelatorioGogoodController {

    @Autowired
    private RelatorioGogoodService relatorioGogoodService;

    @GetMapping(value = "")
    public ResponseEntity<EnvelopeRespostaDTO> findAllAlunosGogood(@RequestParam(value = "filters", required = false) JSONObject filtros,
                                                                      PaginadorDTO paginadorDTO) {
        try {
            FiltroRelatorioGogoodJSON filtroRelatorioGogoodJSON = new FiltroRelatorioGogoodJSON(filtros);
             return ResponseEntityFactory.ok(relatorioGogoodService.consultarAlunosGogood(filtroRelatorioGogoodJSON, paginadorDTO), paginadorDTO);
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }
}

