package com.pacto.relatorioms.controller;

import com.pacto.config.exceptions.ServiceException;
import com.pacto.config.utils.EnvelopeRespostaDTO;
import com.pacto.config.utils.ResponseEntityFactory;
import com.pacto.relatorioms.dto.graduacao.ImpressaoAvaliacaoProgressoAlunoDTO;
import com.pacto.relatorioms.services.interfaces.GraduacaoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

@RestController
@RequestMapping("/graduacao")
public class GraduacaoController {

    @Autowired
    private GraduacaoService graduacaoService;

    @RequestMapping(value = "/avaliacao-progresso-aluno", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> exportarAvaliacaoProgressoAluni(@RequestBody ImpressaoAvaliacaoProgressoAlunoDTO avaliacao,
                                                                               HttpServletRequest request) {
        try {
            return ResponseEntityFactory.ok(graduacaoService.exportarAvaliacaoProgressoAluno(avaliacao, request));
        } catch (ServiceException e) {
            return ResponseEntityFactory.mensagemFront(e.getChaveExcecao(), e.getMessage());
        }
    }

}
