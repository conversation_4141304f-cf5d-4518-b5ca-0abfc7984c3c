package com.pacto.relatorioms.controller;

import com.pacto.config.exceptions.ServiceException;
import com.pacto.relatorioms.filter.FiltroListaAcessosJSON;
import com.pacto.relatorioms.services.implementations.ImpressaoAvaliacaoDTO;
import com.pacto.relatorioms.services.interfaces.AvaliacaoFisicaService;
import com.pacto.relatorioms.services.interfaces.ListaAcessosService;
import com.pacto.config.utils.EnvelopeRespostaDTO;
import com.pacto.config.utils.ResponseEntityFactory;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.Map;

@RestController
@RequestMapping("/avaliacao-fisica")
public class AvaliacaoFisicaController {

    @Autowired
    private AvaliacaoFisicaService avaliacaoFisicaService;

    @RequestMapping(value = "/unica", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> exportarAvaliacao(@RequestBody ImpressaoAvaliacaoDTO impressaoAvaliacaoDTO,
                                                                 HttpServletRequest request) {
        try {
            return ResponseEntityFactory.ok(avaliacaoFisicaService.exportarAvaliacao(impressaoAvaliacaoDTO, request));
        } catch (ServiceException e) {
            return ResponseEntityFactory.mensagemFront(e.getChaveExcecao(), e.getMessage());
        }
    }

    @RequestMapping(value = "/comparativo", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> exportarComparativo(@RequestBody ImpressaoAvaliacaoDTO impressaoAvaliacaoDTO,
                                                                 HttpServletRequest request) {
        try {
            return ResponseEntityFactory.ok(avaliacaoFisicaService.exportarComparativo(impressaoAvaliacaoDTO, request));
        } catch (ServiceException e) {
            return ResponseEntityFactory.mensagemFront(e.getChaveExcecao(), e.getMessage());
        }
    }

    @RequestMapping(value = "/parq", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> exportarParq(@RequestBody ImpressaoAvaliacaoDTO impressaoAvaliacaoDTO,
                                                                 HttpServletRequest request) {
        try {
            return ResponseEntityFactory.ok(avaliacaoFisicaService.exportarParq(impressaoAvaliacaoDTO, request));
        } catch (ServiceException e) {
            return ResponseEntityFactory.mensagemFront(e.getChaveExcecao(), e.getMessage());
        }
    }
}
