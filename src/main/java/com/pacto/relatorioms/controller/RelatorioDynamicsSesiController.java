package com.pacto.relatorioms.controller;

import com.pacto.config.exceptions.ServiceException;
import com.pacto.config.utils.EnvelopeRespostaDTO;
import com.pacto.config.utils.ResponseEntityFactory;
import com.pacto.relatorioms.filter.FiltroRelatorioDynamicsSesiJSON;
import com.pacto.relatorioms.services.interfaces.RelatorioDynamicsSesiService;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

@RestController
@RequestMapping("/relatorio-dynamics-sesi")
public class RelatorioDynamicsSesiController {

    @Autowired
    private RelatorioDynamicsSesiService relatorioDynamicsSesiService;

    @RequestMapping(value = "", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> findAll(@RequestParam(value = "filters", required = false) JSONObject filtros, HttpServletRequest request) {
        try {
            FiltroRelatorioDynamicsSesiJSON filtroRelatorioDynamicsSesiJSON = new FiltroRelatorioDynamicsSesiJSON(filtros);
            return ResponseEntityFactory.ok(relatorioDynamicsSesiService.gerarArquivoTexto(filtroRelatorioDynamicsSesiJSON, request));
        } catch (ServiceException e) {
            return ResponseEntityFactory.mensagemFront(e.getChaveExcecao(), e.getMessage());
        } catch (Exception e) {
            StringBuilder result = new StringBuilder(e.toString() + "\n");
            StackTraceElement[] trace = e.getStackTrace();
            for (StackTraceElement stackTraceElement : trace) {
                result.append(stackTraceElement.toString()).append("\n");
            }
            return ResponseEntityFactory.erroInterno(e.getMessage(),  result.toString());
        }
    }
}