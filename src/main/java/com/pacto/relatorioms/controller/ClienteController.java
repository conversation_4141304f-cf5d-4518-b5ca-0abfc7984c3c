package com.pacto.relatorioms.controller;

import com.pacto.config.dto.PaginadorDTO;
import com.pacto.config.exceptions.ServiceException;
import com.pacto.relatorioms.filter.FiltroClienteJSON;
import com.pacto.relatorioms.services.interfaces.ClienteService;
import com.pacto.config.utils.EnvelopeRespostaDTO;
import com.pacto.config.utils.ResponseEntityFactory;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/clientes")
public class ClienteController {

    @Autowired
    private ClienteService clienteService;

    @GetMapping("")
    public ResponseEntity<EnvelopeRespostaDTO> findAllClientes(@RequestParam(value = "filters", required = false) JSONObject filtros, PaginadorDTO paginadorDTO) {
        try {
            FiltroClienteJSON filtroClienteJSON = new FiltroClienteJSON(filtros);
            return ResponseEntityFactory.ok(clienteService.findAll(filtroClienteJSON, paginadorDTO), paginadorDTO);
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }
}
