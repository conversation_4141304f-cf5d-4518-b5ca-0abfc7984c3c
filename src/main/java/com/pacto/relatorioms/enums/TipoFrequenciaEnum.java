package com.pacto.relatorioms.enums;

public enum TipoFrequenciaEnum {

    GERAL("GERAL", "Geral"),
    GERAL_POR_DIA("GERAL_POR_DIA", "Geral por dia da semana"),
    GERAL_POR_HORARIO("GERAL_POR_HORARIO", "Geral por dia da semana");

    private TipoFrequenciaEnum(String id, String descricao) {
        this.id = id;
        this.descricao = descricao;
    }

    private String id;
    private String descricao;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }
}
