package com.pacto.relatorioms.enums;

public enum SituacaoClienteEnum {

    NORMAL("NO", "Normal"),
    ATESTADO("AT", "Atestado"),
    VENCIDO("VE", "Vencido"),
    AVENCER("AV", "A Vencer"),
    FERIAS("CR", "Férias"),
    TRANCADO("TR", "Trancado"),
    TRANCADO_VENCIDO("TV", "Trancado Vencido");

    private String codigo;
    private String descricao;

    private SituacaoClienteEnum(String codigo, String descricao) {
        this.codigo = codigo;
        this.descricao = descricao;
    }

    public static SituacaoClienteEnum getSituacaoCliente(final String codigo) {
        SituacaoClienteEnum situacao = null;
        for (SituacaoClienteEnum sit : SituacaoClienteEnum.values()) {
            if (sit.getCodigo().equals(codigo)) {
                situacao = sit;
                break;
            }
        }
        return situacao;
    }

    public String getCodigo() {
        return codigo;
    }

    public void setCodigo(String codigo) {
        this.codigo = codigo;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }
}
