/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package com.pacto.relatorioms.enums;

import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public enum SituacaoAcessoEnum {

    RV_BLOQALUNONAOCADASTRADO("RV_BLOQALUNONAOCADASTRADO", "Cartão de aluno não cadastrado.", "NAO CADASTRADO A", "B"),
    RV_BLOQALUNOMATNAOCADASTRADO("RV_BLOQALUNOMATNAOCADASTRADO", "Matrícula de aluno não cadastrada.", "NAO CADASTRADO A", "B"),
    RV_BLOQCOLABORADORNAOCADASTRADO("RV_BLOQCOLABORADORNAOCADASTRADO", "Cartão de colaborador não cadastrado.", "NAO CADASTRADO C", "B"),
    RV_BLOQCOLABORADORCODNAOCADASTRADO("RV_BLOQCOLABORADORCODNAOCADASTRADO", "Código de colaborador não cadastrado.", "NAO CADASTRADO C", "B"),
    RV_BLOQEMPRESANAOCONFERE("RV_BLOQEMPRESANAOCONFERE", "Cartão inválido para esta empresa.", "NAO ACESSA EMP", "B"),
    RV_BLOQTAMCARTAOINVALIDO("RV_BLOQTAMCARTAOINVALIDO", "Transação inválida pelo tamanho", "TRANS. INVALIDA", "B"),
    RV_BLOQFORAHORARIO("RV_BLOQFORAHORARIO", "Fora do horário do Plano.", "FORA HORARIO PLANO", "B"),
    RV_BLOQFORAHORARIOTURMA("RV_BLOQFORAHORARIOTURMA", "Fora do horário da turma.", "FORA HORARIO TURMA", "B"),
    RV_BLOQCONTRATOTRANCADO("RV_BLOQCONTRATOTRANCADO", "Contrato trancado.", "TRANCADO", "B"),
    RV_BLOQCONTRATOFERIAS("RV_BLOQCONTRATOFERIAS", "Contrato em férias.", "EM FERIAS", "B"),
    RV_BLOQCONTATOVENCIDO("RV_BLOQCONTATOVENCIDO", "Contrato vencido.", "CONTRATO VENCIDO", "B"),
    RV_BLOQCONTRATONAOINICIOU("RV_BLOQCONTRATONAOINICIOU", "Contrato ainda não iniciado.", "NAO INICIADO", "B"),
    RV_BLOQEXAMEVENCIDO("RV_BLOQEXAMEVENCIDO", "Avaliação ou exame médico vencido.", "VER EXAME/AVALIACAO", "B"),
    RV_BLOQMSGPERSONALIZADA("RV_BLOQMSGPERSONALIZADA", "Bloqueio por mensagem personalizada.", "BLOQ. POR MENSAGEM", "B"),
    RV_BLOQACESSOSSEGUIDOS("RV_BLOQACESSOSSEGUIDOS", "Bloqueio por acessos seguidos.", "ACESSOS SEGUIDOS", "B"),
    RV_BLOQCONTRATOATESTADOM("RV_BLOQCONTRATOATESTADOM", "Contrato em atestado médico.", "EM ATESTADO", "B"),
    RV_BLOQSTATUSALUNO("RV_BLOQSTATUSALUNO", "Aluno não encontra-se ativo.", "BLOQ - STATUS", "B"),
    RV_BLOQSEMAUTORIZACAO("RV_BLOQSEMAUTORIZACAO", "Aluno não possui autorização de acesso.", "BLOQ - AUTORIZACAO", "B"),
    RV_BLOQPLANOEMPRESA("RV_BLOQPLANOEMPRESA", "O plano do aluno não permite acesso a essa unidade.", "BLOQ - PLANO EMPRESA", "B"),
    RV_BLOQDVNAOCONFERE("RV_BLOQDVNAOCONFERE", "Dígito verificador não confere.", "BLOQ - DIGITO VERIF.", "B"),
    //RV_LIBACESSOENTRADA("RV_LIBACESSOENTRADA", "Liberado acesso - ENTRADA", "LIB. ENTRADA A", "L"),
    //RV_LIBACESSOSAIDA("RV_LIBACESSOSAIDA", "Liberado acesso - SAÍDA", "LIB. SAIDA A", "L")    ;
    RV_LIBACESSOAUTORIZADO("RV_LIBACESSOAUTORIZADO", "Acesso autorizado", "ACESSO AUTORIZADO", "L"),
    RV_BLOQREGRA_LIBERACAO("RV_BLOQREGRA_LIBERACAO", "Bloqueio por regra de validação do terminal.", "BLOQ - REGRA VAL", "B"),
    RV_BLOQPERSONAL("RV_BLOQPERSONAL", "Verificar Controle do Personal.", "BLOQ - PERSONAL", "B"),
    RV_BLOQCOLABORADORINATIVO("RV_BLOQCOLABORADORINATIVO", "Colaborador Inativo.", "COLABORADOR IN C", "B"),
    RV_BLOQPESSOASENHAINVALIDA("RV_BLOQPESSOASENHAINVALIDA", "Senha inválida.", "SENHA INVALIDA", "B"),
    RV_BLOQALUNOPARCELAABERTA("RV_BLOQALUNOPARCELAABERTA", "Por favor, compareça à Recepção", "COMPARECA A RECEP", "B"),
    RV_BLOQALUNOFREQUENCIAPLANO("RV_BLOQALUNOFREQUENCIAPLANO", "Quantidade máxima de frequência atingida", "COMPARECA A RECEP", "B"),
    RV_BLOQCARTEIRINHAVENCIDA("RV_BLOQCARTEIRINHAVENCIDA", "Carteirinha vencida.", "COMPARECA A RECEP", "B"),

    // GYMPASS
    RV_LIBACESSOAUTORIZADOGYMPASS("RV_LIBACESSOAUTORIZADOGYMPASS", "Acesso autorizado por Gympass", "ACESSO AUTORIZADO GYMPASS", "L"),
    RV_BLOQGYMPASS5("RV_BLOQGYMPASS5",  "O usuário já visitou esta academia hoje", "COMPARECA A RECEP", "B", 5),
    RV_BLOQGYMPASS11("RV_BLOQGYMPASS11", "Token Diário Inválido", "COMPARECA A RECEP", "B", 11),
    RV_BLOQGYMPASS12("RV_BLOQGYMPASS12", "Validador não autorizado. Um passe só é válido na academia onde foi comprado", "COMPARECA A RECEP", "B", 12),
    RV_BLOQGYMPASS13("RV_BLOQGYMPASS13", "Token já foi usado hoje", "COMPARECA A RECEP", "B", 13),
    RV_BLOQGYMPASS14("RV_BLOQGYMPASS14", "Item não está disponível devido a problemas com o pagamento", "COMPARECA A RECEP", "B", 14),
    RV_BLOQGYMPASS15("RV_BLOQGYMPASS15", "Passe já foi completamente usado", "COMPARECA A RECEP", "B", 15),
    RV_BLOQGYMPASS16("RV_BLOQGYMPASS16", "Passe já expirou e não pode mais ser usado", "COMPARECA A RECEP", "B", 16),
    RV_BLOQGYMPASS21("RV_BLOQGYMPASS21", "Número de Token Diário Inválido", "COMPARECA A RECEP", "B", 21),
    RV_BLOQGYMPASS22("RV_BLOQGYMPASS22", "Cartão não habilitado", "COMPARECA A RECEP", "B", 22),
    RV_BLOQGYMPASS23("RV_BLOQGYMPASS23", "O aluno não fez checkin", "COMPARECA A RECEP", "B", 23),
    RV_BLOQGYMPASS24("RV_BLOQGYMPASS24", "Sem permissão para validar passes diários", "COMPARECA A RECEP", "B", 24),
    RV_BLOQGYMPASS26("RV_BLOQGYMPASS26", "Não há créditos", "COMPARECA A RECEP", "B", 26),
    RV_BLOQGYMPASS27("RV_BLOQGYMPASS27", "Pessoa bloqueada", "COMPARECA A RECEP", "B", 27),
    RV_BLOQGYMPASS28("RV_BLOQGYMPASS28", "Erro ao aprovar o cartão bancário", "COMPARECA A RECEP", "B", 28),
    RV_BLOQGYMPASS29("RV_BLOQGYMPASS29", "Cartão desabilitado", "COMPARECA A RECEP", "B", 29),
    RV_BLOQGYMPASS30("RV_BLOQGYMPASS30", "Cartão expirado", "COMPARECA A RECEP", "B", 30),
    RV_BLOQGYMPASS32("RV_BLOQGYMPASS32", "Esta pessoa não tem passes disponíveis para essa academia", "COMPARECA A RECEP", "B", 32),
    RV_BLOQGYMPASS33("RV_BLOQGYMPASS33", "Academia bloqueada", "COMPARECA A RECEP", "B", 33),
    RV_BLOQGYMPASS34("RV_BLOQGYMPASS34", "Token diário desativado", "COMPARECA A RECEP", "B", 34),
    RV_BLOQGYMPASS35("RV_BLOQGYMPASS35", "Token Diário expirou", "COMPARECA A RECEP", "B", 35),
    RV_BLOQGYMPASS38("RV_BLOQGYMPASS38", "Pessoa não está na lista de permitidos para essa academia", "COMPARECA A RECEP", "B", 38),
    RV_BLOQGYMPASS39("RV_BLOQGYMPASS39", "Número máximo permitido de vezes na semana foi excedido", "COMPARECA A RECEP", "B", 39),
    RV_BLOQGYMPASS40("RV_BLOQGYMPASS40", "Número máximo permitido de vezes este mês foi excedido", "COMPARECA A RECEP", "B", 40),
    RV_BLOQGYMPASS41("RV_BLOQGYMPASS41", "Nenhuma reserva foi encontrada para esta pessoa", "COMPARECA A RECEP", "B", 41),
    RV_BLOQGYMPASS42("RV_BLOQGYMPASS42", "É muito cedo para validar esta reserva", "COMPARECA A RECEP", "B", 42),
    RV_BLOQGYMPASS43("RV_BLOQGYMPASS43", "É tarde demais para validar esta reserva", "COMPARECA A RECEP", "B", 43),
    RV_BLOQGYMPASS45("RV_BLOQGYMPASS45", "O usuário ainda não fez check-in", "COMPARECA A RECEP", "B", 45),
    RV_BLOQGYMPASS46("RV_BLOQGYMPASS46", "Usuário fez check-in em outra academia", "COMPARECA A RECEP", "B", 46),
    RV_BLOQGYMPASS47("RV_BLOQGYMPASS47", "User Check In para essa academia expirou", "COMPARECA A RECEP", "B", 47),
    RV_BLOQGYMPASSGENERICO("RV_BLOQGYMPASSGENERICO", "Token Gympass não foi validado", "COMPARECA A RECEP", "B", 48),
    RV_GYMPASS_AGUARDANDO_RESPOSTA("RV_GYMPASS_AGUARDANDO_RESPOSTA", "Aguardando resposta da Gympass", "AGUARDANDO RESPOSTA GYMPASS", "B"),
    RV_BLOQALUNOSEMASSINATURA("RV_BLOQALUNOSEMASSINATURA", "Verificar Assinatura Digital", "COMPARECA A RECEP", "B"),

    RV_BLOQALUNOCAPACIDADESIMULTANEA("RV_BLOQALUNOCAPACIDADESIMULTANEA", "Academia está lotada", "ACADEMIA EM CAPACIDADE MAXIMA (COVID-19)", "B"),
    RV_BLOQSEMCARTAOVACINA("RV_BLOQSEMCARTAOVACINA", "Sem comprovante de vacinação apresentado", "COMPARECA A RECEP", "B"),

    //TOTALPASS
    RV_LIBACESSOAUTORIZADOTOTALPASS("RV_LIBACESSOAUTORIZADOTOTALPASS", "Acesso autorizado por TotalPass", "ACESSO AUTORIZADO TOTAL PASS", "L"),
    RV_BLOQTOTALPASS_LIMITE_ACESSOS_DIARIOS_ATINGIDO("RV_BLOQTOTALPASS_LIMITE_ACESSOS_DIARIOS_ATINGIDO", "Limite de acessos diários TotalPass atingido",  "", "B"),
    RV_BLOQTOTALPASS_NESCESSARIO_CHECKIN_TOTALPASS("RV_BLOQTOTALPASS_NESCESSARIO_CHECKIN_TOTALPASS", "Realize o Check-in no aplicativo TotalPass", "", "B");

    private String id;
    private String descricao;
    private String msgColetor;
    private String bloqueadoLiberado;
    private Integer codigo;

    private SituacaoAcessoEnum(String id, String descricao, String msgColetor, String bloqueadoLiberado) {
        this.id = id;
        this.descricao = descricao;
        this.msgColetor = msgColetor;
        this.bloqueadoLiberado = bloqueadoLiberado;
    }

    SituacaoAcessoEnum(String id, String descricao, String msgColetor, String bloqueadoLiberado, Integer codigo) {
        this(id, descricao, msgColetor, bloqueadoLiberado);
        this.codigo = codigo;
    }

    public static SituacaoAcessoEnum consultarPorCodigo(Integer codigo){
        for (SituacaoAcessoEnum item : SituacaoAcessoEnum.values()) {
            if (item.getCodigo() != null && item.getCodigo().equals(codigo)) {
                return item;
            }
        }
        return SituacaoAcessoEnum.RV_BLOQGYMPASSGENERICO;
    }

    public static SituacaoAcessoEnum consultarPorId(String id){
        for (SituacaoAcessoEnum item : SituacaoAcessoEnum.values()) {
            if (item.getId() != null && item.getId().equals(id)) {
                return item;
            }
        }
        return SituacaoAcessoEnum.RV_BLOQGYMPASSGENERICO;
    }

    public static String consultarSituacoesAcessoGympassBloqueio(){
        String ids = "";

        for (SituacaoAcessoEnum item : SituacaoAcessoEnum.values()) {
            if (item.getId() != null && item.getId().contains("RV_BLOQGYMPASS")) {
                ids +=  "'"+item.getId()+"',";
            }
        }

        ids = ids.substring(0 , ids.length() -1);

        return ids;
    }

    public static List<SituacaoAcessoEnum> consultarSituacoesLiberacao() {
        List<SituacaoAcessoEnum> situacoes = new ArrayList<SituacaoAcessoEnum>();

        for (SituacaoAcessoEnum situacao: SituacaoAcessoEnum.values()) {
            if(situacao.getId().contains("RV_LIBACESSO")){
                situacoes.add(situacao);
            }
        }

        return situacoes;
    }

    public static String consultarSituacoesLiberacaoIds(){
        String ids = "";
        int index = 1;
        List<SituacaoAcessoEnum> situacoesLiberacao =  SituacaoAcessoEnum.consultarSituacoesLiberacao();
        for (SituacaoAcessoEnum situacao: situacoesLiberacao) {
            if(index == situacoesLiberacao.size()){
                ids += "'"+situacao+"'";
            }else{
                ids += "'"+situacao+"', ";
            }
            index++;
        }

        return ids;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getMsgColetor() {
        return msgColetor;
    }

    public void setMsgColetor(String msgColetor) {
        this.msgColetor = msgColetor;
    }

    public String getBloqueadoLiberado() {
        return bloqueadoLiberado;
    }

    public void setBloqueadoLiberado(String bloqueadoLiberado) {
        this.bloqueadoLiberado = bloqueadoLiberado;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }
}
