package com.pacto.relatorioms.enums;

/**
 * Enumerador para intentificar o meio de acesso da pessoa à Academia.
 *
 * <AUTHOR>
 */
public enum MeioIdentificacaoEnum {

    // Enumeradores
    DIGITAL(1, "Leitura de uma digital"),
    MATRICULATECLADOCOMPUTADOR(2, "Digitado a matrícula no teclado do computador"),
    MATRICULATECLADOCATRACA(3, "Digitado a matrícula do cliente no teclado da catraca"),
    CODIGOACESSOLEITORSERIAL(4, "Leitura de código de barras serial"),
    SENHACATRACA(5, "Digitado a senha de acesso na catraca"),
    LISTACHAMADA(6, "Presença em Lista de Chamada"),
    LIBERACAOACESSORAPIDO(7, "Liberação de Acesso Rápido"),
    COLETORDEDADOS(8, "A Definir..."),
    DIGITALCATRACA(9, "Digital Catraca"),
    CODIGOBARRACOMPUTADOR(10, "Digitado o código de barras no teclado do computador"),
    AVULSO(11, "Inserido manualmente"),
	RECONHECIMENTOFACIAL(12, "Reconhecimento Facial"),
	APLICATIVO(13, "Aplicativo"),
	RETIRA_FICHA(14, "Retira Ficha"),
	IMPORTACAO(15, "Importação"),
	ACESSOFACIL(16, "Acesso Fácil"),
	;

	// Atributos
	private Integer codigo;
	private String descricao;


	// Métodos da Classe
	/**
	 * Método que seta o código e descrição
	 *
	 * @param codigo
	 * @param descricao
	 */
	private MeioIdentificacaoEnum(final Integer codigo, final String descricao) {
		this.setCodigo(codigo);
		this.setDescricao(descricao);
	}

	/**
	 * Busca o código do enumerador e retorna o enumerador
	 *
	 * @param codigo
	 * @return meioIdentificacao
	 */
	public static MeioIdentificacaoEnum getMeioIdentificacao(final Integer codigo) {
		MeioIdentificacaoEnum meioIdentificacao = null;
		for (MeioIdentificacaoEnum meio : MeioIdentificacaoEnum.values()) {
			if (meio.getCodigo().equals(codigo)) {
				meioIdentificacao = meio;
			}
		}
		return meioIdentificacao;
	}

	// Getters and Setters
	/**
	 * @return O campo codigo.
	 */
	public Integer getCodigo() {
		return this.codigo;
	}

	/**
	 * @param codigo
	 *            O novo valor de codigo.
	 */
	private void setCodigo(final Integer codigo) {
		this.codigo = codigo;
	}

	/**
	 * @return O campo descricao.
	 */
	public String getDescricao() {
		return this.descricao;
	}

	/**
	 * @param descricao
	 *            O novo valor de descricao.
	 */
	private void setDescricao(final String descricao) {
		this.descricao = descricao;
	}


}
