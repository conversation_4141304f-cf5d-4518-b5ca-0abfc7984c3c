package com.pacto.relatorioms.enums;

public enum AgrupamentoEnum {

    NENHUM("NENHUM", "Nenhum"),
    AGRUPAR_SX_SAB("AGRUPAR_SX_SAB", "Agrupar sexta e sábado");

    private AgrupamentoEnum(String id, String descricao) {
        this.id = id;
        this.descricao = descricao;
    }

    private String id;
    private String descricao;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }
}
