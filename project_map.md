# Project Map - Relat�rio MS

## 1. Vis�o Geral e Pilha <PERSON>

### Prop�sito
O **Relat�rio MS** � um microsservi�o especializado na gera��o de relat�rios para um sistema de gest�o de academias/clubes. Ele processa dados de clientes, colaboradores, acessos, contratos e outros elementos do neg�cio, gerando relat�rios em formatos PDF e Excel.

### Pilha <PERSON>
- **Linguagem:** Java 8
- **Framework Principal:** Spring Boot 2.7.1
- **Persist�ncia:** Hibernate 5.4.4.Final + Spring Data JPA
- **Banco de Dados:** PostgreSQL (OAMD database)
- **Gera��o de Relat�rios:** JasperReports 6.19.0, Apache POI 3.17, iText ********
- **Autentica��o:** JWT (Auth0 Java-JWT 3.19.1)
- **Build:** Maven
- **Containeriza��o:** Docker
- **Comunica��o:** REST APIs, comunica��o entre microsservi�os

## 2. Arquitetura e Componentes L�gicos

### Paradigma Arquitetural
**Microsservi�o em Camadas** seguindo padr�es MVC com separa��o clara de responsabilidades:
- **Controller Layer:** Endpoints REST
- **Service Layer:** L�gica de neg�cio
- **DAO Layer:** Acesso a dados
- **Entity Layer:** Modelos de dom�nio
- **DTO Layer:** Objetos de transfer�ncia
- **Adapter Layer:** Convers�o Entity ? DTO

### Mapeamento de M�dulos

#### **M�dulo Controller** (`src/main/java/com/pacto/relatorioms/controller/`)
**Responsabilidade:** Exposi��o de endpoints REST para diferentes tipos de relat�rios
- `RelatorioSMDController` - Relat�rios SMD (Sistema de Monitoramento de Dados)
- `RelatorioArmarioController` - Relat�rios de arm�rios
- `RelatorioVisitantesController` - Relat�rios de visitantes
- `RelatorioConvidadosController` - Relat�rios de convidados
- `RelatorioPersonalController` - Relat�rios de personal trainers
- `RelatorioSaldoCreditoController` - Relat�rios de saldo de cr�dito
- `RelatorioGympassController` - Relat�rios Gympass
- `TotalizadorAcessosController` - Totalizadores de acesso
- `ListaAcessosController` - Listas de acesso
- `HealthController` - Health check

#### **M�dulo Services** (`src/main/java/com/pacto/relatorioms/services/`)
**Responsabilidade:** Implementa��o da l�gica de neg�cio para gera��o de relat�rios
- Processamento de filtros
- Consulta e agrega��o de dados
- Gera��o de arquivos (PDF/Excel)
- Aplica��o de regras de neg�cio

#### **M�dulo DAO** (`src/main/java/com/pacto/relatorioms/dao/`)
**Responsabilidade:** Acesso e consulta ao banco de dados
- Implementa��o de consultas HQL/SQL customizadas
- Padr�o DAO gen�rico (`DaoGenerico`)
- Consultas espec�ficas por entidade

#### **M�dulo Entities** (`src/main/java/com/pacto/relatorioms/entities/`)
**Responsabilidade:** Modelos de dom�nio JPA
- Mapeamento objeto-relacional
- Relacionamentos entre entidades
- Anota��es de auditoria e logging

#### **M�dulo DTOs** (`src/main/java/com/pacto/relatorioms/dto/`)
**Responsabilidade:** Objetos de transfer�ncia de dados
- Serializa��o JSON
- Contratos de API
- Isolamento da camada de apresenta��o

#### **M�dulo Adapters** (`src/main/java/com/pacto/relatorioms/adapters/`)
**Responsabilidade:** Convers�o entre Entities e DTOs
- Padr�o Adapter
- Mapeamento de dados
- Transforma��o de objetos

#### **M�dulo Filters** (`src/main/java/com/pacto/relatorioms/filter/`)
**Responsabilidade:** Processamento de filtros JSON
- Parsing de par�metros de consulta
- Valida��o de filtros
- Constru��o de crit�rios de busca

#### **M�dulo Utils** (`src/main/java/com/pacto/relatorioms/utils/`)
**Responsabilidade:** Utilit�rios para gera��o de relat�rios
- `VisualizadorRelatorio` - Gera��o de PDFs com JasperReports
- `exportador/` - Sistema de exporta��o para Excel
- `RelatorioBuilder` - Builder pattern para relat�rios

#### **M�dulo MSCommunication** (`src/main/java/com/pacto/relatorioms/mscomunication/`)
**Responsabilidade:** Comunica��o com outros microsservi�os
- `MediaMs` - Comunica��o com microsservi�o de m�dia
- Service discovery
- Integra��o REST

#### **M�dulo Enums** (`src/main/java/com/pacto/relatorioms/enums/`)
**Responsabilidade:** Enumera��es do dom�nio
- `SituacaoClienteEnum` - Estados do cliente
- `MeioIdentificacaoEnum` - Tipos de identifica��o
- `TipoFrequenciaEnum` - Frequ�ncias de relat�rio

## 3. Entidades de Dados e Seus Ciclos de Vida

### Entidades Centrais

#### **Pessoa**
- **Atributos:** codigo, nome, cfp, rg, fotokey, dataNasc, sexo, estadoCivil
- **Relacionamentos:** OneToOne com Cliente, OneToMany com Email/Telefone/Endereco
- **Ciclo:** Criada ? Vinculada a Cliente/Colaborador ? Atualizada ? (Soft Delete)

#### **Cliente**
- **Atributos:** codigo, situacao, matricula, codigoMatricula
- **Relacionamentos:** OneToOne com Pessoa, ManyToOne com Empresa/Categoria, OneToMany com Vinculo/QuestionarioCliente
- **Ciclo:** Cadastro ? Ativo ? Situa��es (Normal/Vencido/Trancado/F�rias) ? Inativo

#### **Empresa**
- **Atributos:** codigo, nome, ativa, cnpj, endereco, telefones
- **Relacionamentos:** OneToMany com Cliente/Colaborador
- **Ciclo:** Criada ? Ativa ? (Inativa)

#### **Colaborador**
- **Atributos:** codigo, situacao, diaVencimento
- **Relacionamentos:** OneToOne com Pessoa, ManyToOne com Empresa
- **Ciclo:** Contratado ? Ativo ? Situa��es diversas ? Desligado

#### **Contrato**
- **Atributos:** codigo, vigenciaDe, vigenciaAteAjustada
- **Relacionamentos:** ManyToOne com Plano/Pessoa
- **Ciclo:** Criado ? Vigente ? Renovado ? Expirado

#### **AcessoCliente**
- **Atributos:** codigo, sentido, dtHrEntrada, dtHrSaida, tipoAcesso, situacao
- **Relacionamentos:** ManyToOne com Cliente/LocalAcesso
- **Ciclo:** Entrada registrada ? Sa�da registrada ? Processado em relat�rios

### Regras de Neg�cio Cr�ticas
- **Situa��o do Cliente:** Clientes podem ter diferentes situa��es que afetam acesso e relat�rios
- **Contratos:** Vig�ncia determina direitos de acesso
- **Acessos:** Primeiro acesso do dia pode ser tratado diferentemente
- **Empresas:** Dados s�o sempre filtrados por empresa (multi-tenant)

## 4. Fluxos de Funcionalidade e L�gica de Neg�cio

### Funcionalidades Chave

#### **Gera��o de Lista de Acessos**
**Fluxo:** Controller ? Service ? DAO ? Filtros aplicados ? Dados agregados ? Exporta��o (PDF/Excel)
1. `ListaAcessosController.findAll()` recebe filtros JSON
2. `ListaAcessosService.consultarListaAcessos()` processa filtros
3. `AcessoClienteDao.findByFiltros()` executa consulta
4. Dados s�o transformados via Adapters
5. `VisualizadorRelatorio` ou `Exportador` gera arquivo final

#### **Relat�rio de Totalizador de Acessos**
**Fluxo:** Agrega��o de dados de acesso por per�odo/crit�rios
1. Filtros de per�odo, cliente, plano s�o aplicados
2. Dados s�o agrupados por pessoa/per�odo
3. C�lculos de frequ�ncia s�o realizados
4. Resultado � formatado para visualiza��o

#### **Relat�rios de Arm�rios**
**Fluxo:** Consulta contratos de loca��o de arm�rios
1. Filtros por per�odo de loca��o/vencimento
2. Consulta contratos ativos/vencidos
3. Gera��o de relat�rio com status de pagamento

## 5. Interfaces Externas e Pontos de Entrada

### Pontos de Entrada
- **REST Endpoints:** Todos os controllers expostos como `/relatorio-*`
- **Health Check:** `/health` para monitoramento
- **Filtros JSON:** Par�metro `filters` em requests GET/POST
- **Pagina��o:** `PaginadorDTO` para controle de p�ginas

### Padr�o de Request
```
GET /relatorio-{tipo}?filters={json}&page={n}&size={n}
POST /relatorio-{tipo} com filtros no body
```

## 6. Persist�ncia de Dados e Servi�os Externos

### Mecanismos de Persist�ncia
- **Banco Principal:** PostgreSQL (OAMD database)
- **Connection Pool:** HikariCP
- **ORM:** Hibernate com Spring Data JPA
- **Dialect:** PostgreSQL93Dialect

### Integra��es Externas
- **Discovery Service:** Service registry para localizar outros microsservi�os
- **Authentication Service:** Valida��o de tokens JWT
- **Media Service:** Download de arquivos/imagens via `MediaMs`
- **Outros Microsservi�os:** Comunica��o via REST com discovery

## 7. Seguran�a: Autentica��o e Autoriza��o

### Autentica��o
- **JWT Tokens:** Valida��o via Auth0 Java-JWT
- **Secret Keys:** M�ltiplas chaves para diferentes contextos (ZW, Persona)
- **Request Service:** Interface para obter usu�rio/empresa atual

### Autoriza��o
- **Multi-tenant:** Dados sempre filtrados por empresa do usu�rio
- **Controllers Livres:** Configura��o `app.config.free-controllers` para endpoints sem autentica��o

## 8. Gest�o de Configura��o e Ambientes

### Configura��o
- **application.properties:** Configura��es base com placeholders Maven
- **Profiles Maven:** desenv, docker, local, producao, servidor-local
- **Vari�veis de Ambiente:** Docker/Kubernetes overrides
- **Discovery URLs:** Configura��o din�mica de endpoints

### Ambientes
- **Desenvolvimento:** Porta 8399, secrets locais
- **Docker:** Porta 8080, secrets em volumes
- **Produ��o:** Porta 28098, secrets em filesystem, URLs HTTPS

## 9. Observabilidade: Tratamento de Erros e Logging

### Estrat�gia de Erros
- **ServiceException:** Exce��es de neg�cio com chaves de internacionaliza��o
- **ResponseEntityFactory:** Padroniza��o de respostas HTTP
- **Try-Catch:** Captura em controllers com logging de stack trace

### Logging
- **Spring Boot Logging:** Configura��o via application.properties
- **Debug Level:** Configur�vel por ambiente
- **Auditoria:** Anota��es `@NomeEntidadeLog` nas entidades

## 10. Gest�o de Depend�ncias e Processo de Build

### Depend�ncias
- **Maven:** `pom.xml` com depend�ncias espec�ficas para relat�rios
- **Jasper Reports:** 6.19.0 para PDFs
- **Apache POI:** 3.17 para Excel
- **Pacto Commons:** Biblioteca interna 1.0.11

### Build/Compila��o
- **Maven Build:** `mvn clean package`
- **Docker Build:** Multi-stage com OpenJDK 8
- **Profiles:** Diferentes configura��es por ambiente
- **Deploy:** Scripts automatizados via SSH (profiles scp/redeploy)

---

## **Hist�rico de Mudan�as e Corre��es**

### **2025-01-04 - Corre��o Bug: Resposta (Select) N�o Aparece no PDF**

**Problema Identificado:**
- Campo `resposta` de atividades n�o aparecia no PDF de avalia��o de progresso
- Campo `observacao` funcionava corretamente
- Causa: Uso incorreto do construtor de `ItemRelatorioAvaliacaoProgressoTO`

**Corre��o Implementada:**
- **Arquivo:** `src/main/java/com/pacto/relatorioms/dto/graduacao/GraduacaoServiceImpl.java`
- **M�todo:** `montarSubAtividades()`
- **Mudan�a:** Corrigido mapeamento de campos para template Jasper
- **Resultado:** Agora mapeia corretamente:
  - `valor1` = Resposta (Select/Conceito) ? `$F{valor1}`
  - `valor2` = Observa��o (com formata��o condicional) ? `$F{valor2}`
  - `valor3` = Nome/Descri��o da atividade

**An�lise Arquitetural:**
- **Princ�pio Violado:** DRY - Construtores duplicados com comportamentos diferentes
- **Melhoria Sugerida:** Refatorar `ItemRelatorioAvaliacaoProgressoTO` para usar Builder Pattern
- **Impacto:** Baixo - Corre��o pontual sem quebra de compatibilidade

---

**Status:** Mapa atualizado - Bug corrigido, sistema funcional
**Pr�ximos Passos:** Monitoramento cont�nuo de mudan�as e propostas de melhorias
