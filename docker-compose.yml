version: '3.5'
services:
    discovery:
        image: registry.gitlab.com/plataformazw/relatorio-ms
        ports:
            - 8087:8080
        environment:
            DEBUG: "true"
        depends_on:
            - postgres
        restart: always
    postgres:
        image: registry.gitlab.com/plataformazw/docker-pacto/postgres:9.4
        restart: always
        ports:
            - 5432:5432
        environment:
            IP_HOST: postgres
            URL_ZW: http://zw:8080/ZillyonWeb
            URL_TREINO: http://treino:8080/TreinoWeb
            URL_AUTENTICACAO: http://autenticacao:8080
            URL_GRADUACAO: http://graduacao:8080
            URL_PERSONAGEM: http://personagem:8080
    relatorio:
        build: .
        image: registry.gitlab.com/plataformazw/relatorio-ms:master
        ports:
            - 8098:8080
        environment:
            SERVER_PORT: 8080
            DISCOVERY_URL: http://discovery:8080
        restart: always
